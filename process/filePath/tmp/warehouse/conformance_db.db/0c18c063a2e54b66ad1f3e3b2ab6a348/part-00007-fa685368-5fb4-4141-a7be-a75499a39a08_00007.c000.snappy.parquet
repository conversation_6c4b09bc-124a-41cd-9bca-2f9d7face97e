PAR1�8�����<�  ��>%   提交需求 is an undesired activity+   处室领导审批b/ 1 ^/ �Ffollowed by 服务台处理L   信息化与流程推进部项目经理�1环�v� 	�交接z/ :  
/P分析与功能设计�> ./ 0说明书评�>  (!04��作量复核b\ F,��个!��签署R| z� �q	ezP �n� �A:�J  R�,移交开发z� E�),计划维护�5 I�z5 aX	jz) �(结果确讎� 8UAT申请与下�� .  5�g 2 后评估�d 2�� z�)mi� 澄清请n� CA9���W��口i#.A 回复z� 	Vb� 7R� � =R; �L��(��管副总)Tb� E�R/ j� @�j�e��y }�调整z5 �科嚘 4.�  �.Q �r� X.8 与��,（转办）bqag �6
,jn>N管理I��KAR�>N.; ��,   ConformsŶ��训师zB!z 填报人s�!� �z�工单!M系统�,b�.B )2|^�>, ��[	�)g员2R��n[ ,运维人员	�e�1R	q^.� F5 gI�JUR� g	2FN pR5 gJa .� :� 2: ^j2 4  
�)d�� Iv�,数据接口!ov!�)组)��R!JɖIm�va �� �b�� |�长	�投�v�
5�b层z� ,6   ES协作	:JR 3B: !}Q�bTE �>/.:F" �CSCMΪ 4  >; �� �=z�./*�zz!�./ �]采购�� ��ӟ��
�  �	���   ��2�*��(�>�$��~.�&�������������	�����	��������(���	����������	���$	���������~��~ ��>   ���`@(��b�h0�$"� ��@�X
���`(��P,
��L ��f�!�q� ����8��d��
��9	ł�p< ��hB�s0 .DB�`4HD�L   �� h
���`@\0��b�h0�$"� :� $"<���H(����H&��`�!<"�X0�D2���P���b�h8 � 4  :�`C(xDB�`4
�� �AHD(x��H(����H"���F�*pJ�'�j��H���DB�P�Q��r�F�S5�F�k��H�N`$��F�k��:�N`$��f�шT�F-�����l7ب8�:�Sj�	�L��id�F#�h���p9�h8�N��hD*u�	E�v<�hD�\/lD*u#��T�J���6�x�шT
dF��h�ӍF��	8�F�R`$��F���f��n�Q�Z�X#ֈ5N*T�Z�F�*�
�R� *0��F#�h4j�L��42�L��8�R#R)0��F#.��#�)0��F��h�	�d�ш5b�F�R 3j�L�R 3��H�@f$��v<�kDB�F#\N7u�ԩ�	�R��I���V#ֈ5b�X��ȠH� j�f�k��F��i�r�F�ш5b�x�k4"�:��$��p9�h�	�d�.��:�j�	�T��j5u#Y�f�P�R`$3�H��H��f�шT
�L�R'02�'T��9�P�S�Z�X��h4��l7؈T
�d�ш5b�r��(pR�2'�j4�F�N`$��F��I���V���	�d5��	�d��h4"�#�l4��`��	8�N���H�Q`$��p9ݎ��:�N��:�F#��42�F��h4b�X;�n��F�Q��r�x��6�N#�h�ӍF�RE*u#M6�F����J���T
�d��h4N�ӈ�"�H(�T
�d&j8eN*�h4�v<݈���v<�hD*F��l4�N�N`D�F6       ���ͨ�
�   ���   �O  L    k    V  c  <    2  m  ,  �   �    �   I    �  �   �  �   �  �  0  `    �   =  Y  �   =      �         �  �  =    p   �  �  %   <  �  �     �  2  {  ����    �  �  	  �   �   �  �  w     �  �  �   �   �  �   �  �  .  |  G  �  e  �     �   	  2  �  p  `   �  z   ��1Ύ���   ����   �y  提交需求,处室领导审批,服务台处理,信息化与流程推进部项目经理处理环节,需求交接环节,	qX分析与功能设计�5. 0说明书评�"P��作量复核,各个���签署RD 
`�� 	I�4 %5�. ��目)+,移交开发
h	
,计划维护J  �%�J �2 2? (结果确�q8UAT申请与下�F/  后评估2, .[ :�}�}j}j)wI� 澄清请a=,业务归口I�.% 回复-o�_ 	:mE�2	��D �D �D 	D��B�6�B J��p�p�p�p�pMω�J�%�>V>^�>,分管副总i� ,����j�I%
lim ���>�	W���<�<�<�<�<�<B<��调整M?mU
	Fq�bfb �>
科>
�0
Z0
�brb].�	1t>R
*�
Z�
�t
�t
�t
�t
t
��� T��V*�.
,（转办）�ź���B	�0	�0	�0	�0	�0	�0	b0	�F)"�
��
 pʚ
�G�G.�管理g
ify���� �� �}Z.� �L
L
n0�F
n> �(�(=)��n�n�n�n�nU
��� ��n
�n
]m.��WZ ������� ��6�j4 �<�<<��
��
 u>��W
�W
�W
�W
�W
�W
�W
�W
�W
V�	R
 *�y�E�E�E�k2�
/����j�*���� �s�``Zq�a	.��.� �>�����������������Z��>�>V> /������Z��	�kd��������������������� hո<工单,内训师M� 填报人�'��#	6	\ {Bl F@ 系统�X#.F NV6 �Z! �� 	_�<员,运维人员
�:l �#  �B� �zfzB�6Q:y .  �>� >@�V �(��)  ^>� �<	b)��FX �>b B� ��!���# ;n
iZB�U�)�2���	B�	,数据接口aL)/组-l	 �5�F 	F 
>?J)9-_J  J�>1j�V3%�h�-LnJ �w%w \>y&)��MFMi��0%0IS]c�M9长I�投诉@层-h�/ 50�x �>Y�N���I :�  O֥ >�.6 y>S n- � )�nL �>} >�>� JS ES协作	>1 6 2� 	12 6. �L :� �B� �W	IcM���
9*�%>& J� :�  kB� :b0I0Ο�-�� �~o .- �	SCMn
: 2��- ���  �6e.��V �o 
?�)  �~� ��-�:� n�Rdr�j�  8��J_�� �n� ��:�>� : ��  �<:<�DVI�*F�:�$6$B�����J}���c��U�� ��:�>0n�R�)�r�:� 
32�V# 绚�	���	�/ :�  �BQި	��	�# �#  ��� �6��f �� ~� ~  �� ֊� ~i ����V� �>� >� V�>& 2� > �C 
Cr` . B�	J�.0 N  ��	ْB .������V��B
jB
 B2F>j��2�a Na ��R:R �>�J��� �� �� -#n �"\B/��)�2�J	��
i��`:`�qֶ � �� �� F� B?^M�:^T UV� �>v>0I}Z�>K	�2��MjM �>� �2��Z& 2�o o 0�V� 	&Z�2�. 2F>< J�:/�# �# .#  �>�>��)�2� ��*��_ .L  �B� �J)���:�
V2�5h�S�#  x> >I.-B ��>3 		�n�  �~| �C �# ��FW�v _Be���>�Ƽ��)*r���j� �>]�7�7�� ��  L�� 2�J.  B�J0 J2��Y��	�!�! ĊJ� ��E�ni:�)2(.� �>� 9W�.- B ��	&~z�� �	g �X& ����N�采购2jb�n���� �~���.����>,.o  YB�FW	�YΉ%.F NV6 JsV) 	is��o �o �n	�#  �>8>��f �5^5U��
 QB��J:>� �����T2���j�B�J)I�9nic	y	>& �S�~ �>��q�q�C �:�: �>� �ɬJ�	2�)�9䆕 �����~n1 :\	6��p�� �� F� )�rI
:q �Bt�J��q�q��: �B��z�31��m�9w�z.� �B� ��6�.l B�	.  -B�F+���,.,��r�B\J3.aN  Jd��YI3�Y:&  �>\>�2� � ��2	:�  >m>} �C �# �rڔ : �LB3�9^) ��T b8 : H>�Jm�M �>L ymK:�JTv�J1 :�  h>�>� 0i�6�	FrP	&q�L JH>n�	R�
�
�
-l�J )\�	F�8F�Vv		�'	9���.| >��J� >y.l ���\ ����-�n [B���2� *~�rb� Z n�J� >¹�� �;�8.�  o>�>� �
nON# �����q 6q �N 6N U|� �B��6�¬Jr	�:� B�J0JV���I Fo:B��FConforms:需求分析与功能设计环节 is an undesired activity     m    ?提交工单,内训师审批,填报人提交领导审批,处室?提交需求,科室领导审批,处室领导审批,服务台夅  ��   �#�   �)�1   lHspark_schema 5 resultL<   5list %element% L   %	variantId %variant% L   �<&�%8resultlistelement��K�#&�&6 (:需求分析与功能设计环节 is an undesired activityConforms ,    �^�Z� &�#5 	variantId���&�#<m      (m          �^�\. &�)5 variant����1&�)<6 (�提交需求,科室领导审批,处室领导审批,服务台处理,信息化与流程推进部项目经理处理环节,信息化与流程推进部领导审批,需求交接环节,需求分析与功能设计环节,信息化项目经理审批,工作量复核,功能设计说明书评审环节,各个部门签署功能设计说明书环节,信息化与流程推进部项目经理签署环节,信息化与流程推进部领导签署环节,信息化与流程推进部项目经理移交开发环节,开发计划维护环节,开发计划审批环节,开发计划维护环节,开发计划审批环节,开发环节,开发结果确认环节,UAT申请与下发环节,UAT结果确认环节,后评估下发环节�提交工单,内训师审批,填报人提交领导审批,处室领导审批,分管副总审批,提交工单,内训师审批,填报人提交领导审批,处室领导审批,提交工单     �_�\� ���&�Z  \org.apache.spark.timeZone
Asia/Shanghai org.apache.spark.legacyINT96  org.apache.spark.version3.3.1 )org.apache.spark.sql.parquet.row.metadata�{"type":"struct","fields":[{"name":"result","type":{"type":"array","elementType":"string","containsNull":true},"nullable":false,"metadata":{}},{"name":"variantId","type":"integer","nullable":true,"metadata":{}},{"name":"variant","type":"string","nullable":true,"metadata":{}}]} org.apache.spark.legacyDateTime  4parquet-mr version 1.12.2-prx (build ${buildNumber})<       =  PAR1