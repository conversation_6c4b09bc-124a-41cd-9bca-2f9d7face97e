PAR1�3�����<z  ��>%   提交需求 is an undesired activity+   科室领导审批r/ 处�/ (��管副总z^ 1   V/ �Ffollowed by 服务台处理L   信息化与流程推进部项目经理�1环�v)5交接z/ :  
/P分析与功能设计�> ./ 0说明书评�>  (!04��作量复核b� F,��个!��签署R| z� �q	ezP �n� �A��J  R�,移交开发z� E�),计划维护�5 I�z5 a�	jz) �(结果确讎� 8UAT申请与下�� .  5�g 2 后评估�d 2�� V�j) 7A���<��口.zp =R; ��>| 管理	���b�>| .; 	�I\bA  4��! �. �r�Ae相关	����.5 	��;  @.� 与>��z� ,   Conforms�,SCM协作运a�6;r3:; 人员z�4   	j>/ .�F" ���工单�.2 . , 内训师z� 填报人	E�Ga�转办�2 Iv�,数据接口!vz�)%组zsA�	�	2^���J� .s57���2 :Q Z	采购i?z� / �F�.� )�I"���.zd  *
:d execut
s start"
cQ=)>�Fs!(系统Y�v	;b�
.� ).� :� a: z2 aI"�� 6   ES�? 3B: ��A�	�)�员�R6 Czp�j!�)�组长)�投诉b| !�
5��层zyA�z�.a. �.	},人员处理 �����  �H�   �$�&�0� 	(�8�.� �	~��������~�{�����	�����$	����~ ~��	������	�����߿XD������� �"D��K�@�=   �@ Daȁ$��4��<P$MT�`ƁH�\��0��@�4Q  �`�f[�(\�\(X�g[�(�8�
*۱%�raTE�DU<$�iƱ%��(H�> X�ql�vl	� ʅ)�8IU�` 	�,L�< 	'�P$}����	�G��g�h�~�g�`��9�e0��0|'�}Y�w�'{�����0|�'߉~�����}�����}�G���Y
߉~`(���}�g��G������d��#Y���}�G��������`(~�������G�������}d�ْe |'��Y
߉~`(~0��'����#Y
_K��w���}_m�`(~9��0����im�jZ���v:�����}�-~pm����~�������#9�e |'�}��1-d)��H��w��Y
#9~d)�d��e �d��e |'���}�߉~�'�C,�����}d)|'������}�߉~�}d)�d�G���nK���}��3�}B,ǻ�Y
߹���}d���Y#Y�H���!KL��}��9�#�}�'��(!�(�����_ � �H�d)|'�~-Q(W��׶�}�H�B4�	߉��G��H��w�	�\߉~�-ֶ�}�W���>��`(~- ~`(~0�A���Id)|�IZ�im��G�4��������nK�E -��v<~_���W
߉~���AK  �%�|�iAf8�(ģ0
�1�~�� �������   ��b   �^  &  !  .  >  A  )  F  j  {  \  �  �   S  )  �   "   �  6  �  �   i   �  '   �   �   �  �   �  !   �   k   �    S    A  c  �  z  /    7   6   (   �   �   �  �  �  m   �  4   �   �  �  \   �   �   x  4  �     �  -  �   5  �  1   c   Y  c  �  �  9   �   �   K   �   �     �  #  �  �  �  ~   ��0�ߢ��   ���   ��  提交需求,科室领导审批,处�> ,��管副总�	&�服务台处理,信息化与流程推进部项目经理	.环节,	�交接2 P分析与功能设计
". 0说明书评�"	W�D �工作量复核,各个部门签署���
��
	I�4 %��.  �9o,移交开发
h	
,计划维护J )�J �2 2? (结果确�q8UAT申请与下�F/  后评估2, .[ :�J�2N  ��������r���a���口.-�F J��S�S�S�SiS-.i�J�a�!V!, �! �Q�1m1.9管理I^�7b I^�����V��.>
QBN�2���相关	�2�. 	�2 �f .[Jl F �r 	r�2� n4 ������ �>�	��Z���mf	�g�g�gn� .3	�	-�n4 ����A���	
��t�t ;�D�Djw%���^�inx�.q
��f~fF�6�B�	)-2% �rj��q�q�q�?�?m��	��	��	�	.M��	��	��	��	���Q�y
:��.q�q�q�q�q�q�q�q�q�q ���������v�����v��+��B2F �>�>�>�>�>�>:> *>>���QQj
��r�>
�Bj� ��
��
��
 ��.2@�N�N�N�N���> ������������ �"工单B,运维人员� SCM协作	>6: 2; 	22  填报人�&�
�N��	  �>�  内训师�`	62�N\ :U转办> j
,数据接口�A	�组-D	W	&5]�) �~� >� 	YV Jo :�>8 : 2� 	jJT :� ;�)�B�)�Z M	&2�J�	&�L J� �F_�I W~?0	�~"J� >����A A采购�m2 )K}c�[ {~]n[JH�1)�I� �B  ���":�"��Q� \>� >�.� B �� >�J�Jr� �C �v�v���� .� [>��T:T�eNe:�>: 6�F):/ �N �G�� �>N���m>� �I�"2���J )5�n(		2^:�  XB� ��	*
 ��\ >\ :
.l � U~� �Y  bYn� ���#  ��֎�I�F�:��� �# �=�=�=系统yY�*> �& �& &�:��  �Jf��	������ x��������� �� �6�� ~�|J�>���N���r�-J2	�q rq  L�
*RV�   8~�6M ��n��\ :\ �KNK:Z �>�>� ig2ESnS6 29 JL �iri �>� >��22�:e.  "�[Fs�B�JC6q>7 6 6�^R :$���{ �{ �{ �{ �{ �{  
��:���2�Ҏ6g>� 6 6��o  �>��	�	^� �>� >� �� �# �-r-B�n�� �P�����)�*.
�w �>�J�	V	
	�员
9O�
�~	,> 51� K~0JaJ� 	&�~|^r �i :�����-�2d�)O uB�:�0)2��O ��y �y j�  �� �~n	j�  G���� ��n�:K^DrD ���� �>�J2�J  N��?	��� n>� J� ZIF>Z) z �# >���2�J� �}�} S>�.�@�@�)	r)	M�nb�#  �>�Jb2J  �!�!I!��  �>� �D�^�E_ �� ����bf.� Bt.  �>� >�����
F组长投诉X=层�
:�
&n�  �>� J#�Z& .4�\   �R�R>R>�n����F����Wq
>� B }� }�J� .\ B�U�	&2uJL J�	9Z�
�_ 
_������	�1*>� ��.�nh�i 	i �B�:r�v :��B��bF'6� J  :� �i�i �BdF� V�0V V �n	?�������#  �>� �6	�Yz��� :� $~36#��j	C2!J��lEln�:�i^�� �~(n� ���%�� 6�>r 6 2P	\2 ��N# 6Q �o 2o  �>>��I�Z{>� �� : �B� F�>y n���	�A . .��N� *�	B�5.	&�-�-)Ԯ
3n�O0N2����.��L .L U�^B�+>)�2��� j� 
o�	��/ :� B��� zS	�2/z2 : 2; JN :� �#  �Bp�"�"J� :� >l: �-:R �q �q �q  ���>� B 2A�VV �I#�+��#B!>�u#	���~� B�:��� )>��ofo��^��R FR 5S�� ��F�F&F:� y"�' �B��6�֟5F� �������>�	�Z"
j� ����f� �Bh�C�<B-��3�>�>>16�*	�f~f��� �>������� `~�)W�)�)�).)N���
r�
 �>����>�ư�G 	Ge��$
 ��� z�
V~6 ~  RV  d>� ��RL -�R  Oh:�6� :#)�2�
8填报人确认Conforms:需求分析与功能设计环节 is an undesired activity     �    ?内训师审批,提交工单,填报人提交领导审批,处室@服务台处理,提交工单,运维人员处理,填报人确认-  ��   ��   �%�0   lHspark_schema 5 resultL<   5list %element% L   %	variantId %variant% L   �<&�%8resultlistelement��D�&�&6 (:需求分析与功能设计环节 is an undesired activityConforms ,    �Y�U� &�5 	variantId���&�<�      (�          �Y�V. &�%5 variant����0&�%<6 (b服务台处理,提交工单,运维人员处理,填报人确认,运维人员处理,填报人确认�内训师审批,提交工单,填报人提交领导审批,处室领导审批,服务台处理,信息化与流程推进部数据接口人审批,运维组处理,运维人员处理,填报人确认     �Y�W� ���&�U  \org.apache.spark.timeZone
Asia/Shanghai org.apache.spark.legacyINT96  org.apache.spark.version3.3.1 )org.apache.spark.sql.parquet.row.metadata�{"type":"struct","fields":[{"name":"result","type":{"type":"array","elementType":"string","containsNull":true},"nullable":false,"metadata":{}},{"name":"variantId","type":"integer","nullable":true,"metadata":{}},{"name":"variant","type":"string","nullable":true,"metadata":{}}]} org.apache.spark.legacyDateTime  4parquet-mr version 1.12.2-prx (build ${buildNumber})<       �  PAR1