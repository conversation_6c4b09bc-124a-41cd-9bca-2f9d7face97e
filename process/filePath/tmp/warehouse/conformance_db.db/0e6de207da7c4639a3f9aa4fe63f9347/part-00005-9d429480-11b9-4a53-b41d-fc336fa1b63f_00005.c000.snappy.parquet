PAR1�6�����<~  ��>%   提交需求 is an undesired activity+   科室领导审批r/ 处�/ 1  Z/ hfollowed by 服务台处理�,分管副总z� dR/ jd �L   信息化与流程推进部项目经理	�环�fG @zP  ��\�中心	ϒ5  7y��!@(��口管理���	�b� C�; ,（转办）bG  =jG �� !�I�交接zk:  
/P分析与功能设计z>  4�1�2� �r� (A�4��作量复核b�.� 0说明书评傢  Fj��个)�签署RP z� ��	ezP �5<J5E�J  R.�JJ },移交开发z� aA	),计划维护�5 Iz5 �_	jz) �(结果确讎� 8UAT申请与下�� .  5�g 2 后评估�d 2�� >�.�z;>X	;�e� �6[a�j,   Conformse���训师I>RE�U 填报人�we
z8  Iv8数据接口人zM � 运维组z�!系统��|r� ��8 ��)工单a.�57IOe01J	>2 	�!��	�dz�		�!)	��-员dR�!).< ).� F� ^n2 . �F�v��z#:  0�R��IOBG��,6   ES协作	�JbR3 3B: .�!AR7 4  M�>/ vp�bSCMΪ s:; >| R� !�i�!�	2)L�g Ja .:�g ��长i投�f�	�	5<
层z� *]:pexecutYs start"bg
r	F���8?   BD等其他��A+Fo� <f~ �� ��
�����  �\�   ��(�*�"�.��������}���������	�߿��������(�����/<���������j 
,������ � $����e��	��~    �@   �q �0��0��@�4Q�ua��q@ F�0��8u^$MTe]X�m ��4�E�IUY�i�` �@|-0�@Q�E�DUօe *N�<P$	<,�0�8��@<   f�L L�<�� �$�Bi�`� �	� ��(�`

���`(8� ��`Ȇ,(�c;� ���P��
oy�B�B�
�	��E� 
�Q�!���2
��`!�	H<(
���8��L�3-�` *(�H� 
�� �)	~ BI������	�$
`=��m��k����8(���P��P���p���δ�B��` �*%�`��!�� ��(�#	��,��P�B��`��U � �{
8�)� � :�2��` �!��
%+�D�� �8�$���H�	`(�#	�2��@�#	�(�l(�b !6�L�3� � 
! �Y���` �N
�
��(�d�(��7~�H�3mߠ ��
��
l �l�r �]8� Di�+�c$
�3-� δ!�!δ<��!�@� ��(� �3-ϴ� !���p,�(��=۳}��(��Hڳ}�.� �
�H�3-��L� 
�	0��2�±	KX`(
8�#	�` �c;��A� �i%G����8�cۏ	�N� (�'��`(8�m?� ��    �������   ��r   �t    (  b  9  s  C  o  K  *  �     l  �    B    �   *  �  �  �  �  x   �  �   d   �    L   �  �  ]   �  u  �  �  �   }      �  �   �  �  N  t   �  [  �  �  r  �  +  �  >   �   n         �   �  e     �  y   [   �     �   �  `  a  f  ~   n  �  D  �   8  �  �  U  �     �  K  j  �  �  �    ���/����
�   ��   �O   提交需求,科室领导审批,处�: <服务台处理,.C  �>S J@ 0分管副总�	f>S �信息化与流程推进部项目经理	�环节r4 .� 中心	�. �:J� �� ���!?(��口管理���	� ,v ,（转办）b+ .�  ,)�交接-*	P分析与功能设计
"5�2n)�<工作量复核,.? 0说明书评�N各个	�签署R4 >| v�	I�4 %��. ]^,移交开发
h	
,计划维护J )(2 6
 $��果确�?8UAT申请与下qF/  后评估2, .[ j:���Uv�I�
�v�b )�)EM���]�����z��.0JR	)-2��n�n�n�n�n�n ��D�Dj ���?^?����������� ��o�o.�
��
��
F�
]Ū�	������������������F� �������~���	��	z�	j�.h
�	j4 �ڢ. .b ����^� �>�����~i�C	^C	��´��������� ����2�JvN�*�C:�JC �� ����^}m��*��� �5�<工单,内训师�
 填报人-}��
��B�8数据接口人
g 运维组i
 ��
� aB�  �B�系统��hV :e &IZ;>	�n�  X>� �b��  �>\ Ɵ �� �#  9>� 9�l>� Iz)e hB= ��)%�员��  �Bl ���a��  F~� .�F���)�  ��	��V JVIIj�VJ i��j
3n� kBN��:qM�Y���:&  ]�o 大)BG6�R:a  Ċa VI .��2I �O B���Ju >��~�~I~�� �� �B# �,��>?6�:o .  ������ES协作�q>� 6 2\	12 :� �B�Fo>q «�s�s �B� jB�5.IC2��*N*)��S:S �~�� �� )M�� /B���J��WSCMne: 2S	^2 :/ >B: 2; JN 5��B�F3 #BV:�S)�����>�D)�: �>FJ��R�)ui �>� >�>;)
245�I!�# 	# �>� �V�	J�V) nS	2��
 �>� N^*�N  	v9�H	��  V��z�):>.�iiiL%�u�) �)  �B�:�>�� G长)�投诉
<-���=�/ :� �B� FIq�އ
��@ ��� ��ImV:] ��R�)��N6T>> 6 2JI2 :� &�
9��0)2���>� ��e
n� N� :�>� : 2^ �q  U!�����5p�=B��tV�	�-��6��Y .�  �B~���>�B 2^zS �- �� �,z,��V���It9��)6��_.F  ��wIwI��� �) �) F�F@>>�bn1 :�.� =>*�V� �.� �t �t I%�: �>�>	v2�:3N# :�>U : 2^ �q :�  2>� � r>6 �G�2�� �	C.� �Bv F`��.���	u���� �B� :9jN}>8n&Rn1 :W	ޮ_ >}�� ���)>� nRB>A 	2�5�C
 ���>	�r�:�>x	Bn� � J�J�>� ������ F�	:�����
f�
*� K�zF� �>O F# N�����z�: 2��� z��J���6�>r 6 �N26Q �o 2o  �>vJR�yj�LV V JfV) 	��=	i>� �� j� N�:��#  v�3�3�3�� �	N# :�>.: �� ��z�z>z>� §>A i�> �W ��*		2t:�>y : 2; bT :l ��>x �� f� ��^C:� B�J.�N  �~I .   ��=J� J� ��%�.� Bb
r 5�	&.t�� �� i_}u�K �>Jg	�SJ  J��XI�VE 	� !>w>%J':w>2 : 6��uNq U>�N# :u �� 6� Κ�� r� N�%:# F�>0:�J��� 6� �N 6N :�  D>�>� 0i�2�J�>���}绚0 �f\:#  ~H>�� �6�N 6N : >�):f �H�S	��A� �>DB�	V J� z�	N�8 8:� <>��D:D	�>�Z�Z%e�f
�Ja :@ �~�{P	>�` 
#�� uB�F >� �)�Fhz�: �b	b	:R >� : �q 
q: >y>� J%:C �# �# �# F#  �������o .o �L .L ��o jo B�����.��� j�  >��Jl>J�~�4�4 t> �1�� �� �no:�n+ R�j1 2�
��� �>v>�>b2�~- 	�2�:#  �>� >� �f (BD等其他nU4�]  �>��	N	��y�bJ F 2\j�V�FP �$nJ :*)~�� �>.J�Y��:w|�^0:?  �B:B0iR2l������b�F 2� nc R�����Z' 2�	n2 8填报人确认<BD等其他协作运维人员处理 is an undesired activity:需求分析与功能设计环节 is an undesired activity     �    ?提交工单,内训师审批,填报人提交领导审批,处室>服务台处理,提交工单,运维人员处理,ES协作运绵  ��   ��   �$�0   lHspark_schema 5 resultL<   5list %element% L   %	variantId %variant% L   �<&�%8resultlistelement��F�&�&6 (:需求分析与功能设计环节 is an undesired activity<BD等其他协作运维人员处理 is an undesired activity ,    �Y�T� &�5 	variantId���&�<�      (�          �Y�V. &�$5 variant����0&�$<6 (�服务台处理,提交工单,运维人员处理,ES协作运维服务台处理,ES协作运维人员处理,运维人员处理,填报人确认�提交工单,内训师审批,填报人提交领导审批,处室领导审批,分管副总审批,服务台处理,信息化与流程推进部数据接口人审批,归口管理部门审批,信息化与流程推进部数据接口人审批,运维组处理,运维人员处理,填报人确认     �Y�W� ���&�T  \org.apache.spark.timeZone
Asia/Shanghai org.apache.spark.legacyINT96  org.apache.spark.version3.3.1 )org.apache.spark.sql.parquet.row.metadata�{"type":"struct","fields":[{"name":"result","type":{"type":"array","elementType":"string","containsNull":true},"nullable":false,"metadata":{}},{"name":"variantId","type":"integer","nullable":true,"metadata":{}},{"name":"variant","type":"string","nullable":true,"metadata":{}}]} org.apache.spark.legacyDateTime  4parquet-mr version 1.12.2-prx (build ${buildNumber})<       �  PAR1