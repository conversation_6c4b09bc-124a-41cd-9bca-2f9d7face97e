#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x00000001030c4660, pid=11321, tid=0x0000000000004607
#
# JRE version: OpenJDK Runtime Environment (Zulu 8.78.0.19-CA-macos-aarch64) (8.0_412-b08) (build 1.8.0_412-b08)
# Java VM: OpenJDK 64-Bit Server VM (25.412-b08 mixed mode bsd-aarch64 compressed oops)
# Problematic frame:
# C  [libasyncProfiler.dylib+0x54660]  ThreadFilter::accept(int)+0x10
#
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   http://www.azul.com/support/
#

---------------  T H R E A D  ---------------

Current thread is native thread

siginfo: si_signo: 11 (SIGSEGV), si_code: 2 (SEGV_ACCERR), si_addr: 0x00000000000518fc

Registers:
 x0=0x0000000108018108  x1=0x00000000ffffffff  x2=0x0000001800001513  x3=0x0000550700000203
 x4=0x00000d4a00000000  x5=0x0000550700000000  x6=0x0000000000000040  x7=0x0000000000000000
 x8=0x0000000000041900  x9=0x0000000000003fff x10=0x0000000000000021 x11=0x0000b17f56860000
x12=0x0000000000000040 x13=0x0000600008587d74 x14=0x00000000001ff800 x15=0x00000000000007fb
x16=0xffffffffffffffd1 x17=0x00000001ef999e70 x18=0x0000000000000000 x19=0x0000000000000000
x20=0x0000000000000001 x21=0x0000000000004607 x22=0x00006000087e5460 x23=0x000000000086f893
x24=0x00000000000186a0 x25=0x0000000000000002 x26=0x00000000ffffffff x27=0x0000000000000000
x28=0x0000000000000001  fp=0x000000016ef02fb0  lr=0x00000001030c8698  sp=0x000000016ef02f10
pc=0x00000001030c4660 cpsr=0x00000000a0001000
Top of Stack: (sp=0x000000016ef02f10)
0x000000016ef02f10:   00000001030e4ed8 0000000108018000
0x000000016ef02f20:   0000a21c75e156ed 000000016ef02f40
0x000000016ef02f30:   0000000108018108 0000600009df6bc0
0x000000016ef02f40:   0000600009df6bc0 0000000000000001
0x000000016ef02f50:   0000000000000000 0000000000000000
0x000000016ef02f60:   0000000000000000 0000000000000000
0x000000016ef02f70:   0000000000000000 0000000000000000
0x000000016ef02f80:   0000000000000000 0000000000000000
0x000000016ef02f90:   0000000000000000 0000000000000000
0x000000016ef02fa0:   0000000000000000 000000016ef03000
0x000000016ef02fb0:   000000016ef02fc0 00000001030c8544
0x000000016ef02fc0:   000000016ef02fe0 000000018083ac0c
0x000000016ef02fd0:   0000000000000000 0000000000000000
0x000000016ef02fe0:   0000000000000000 0000000180835b80
0x000000016ef02ff0:   0000000000000000 0000000000000000
0x000000016ef03000:   17a2cd1b6b7b15d1 0000000000000000
0x000000016ef03010:   000000016f733000 0000000314e13010
0x000000016ef03020:   0000000000000000 0000000000000000
0x000000016ef03030:   0000000000000101 0000000a0000001f
0x000000016ef03040:   0000000000000000 0001000000000000
0x000000016ef03050:   0000000000000000 0000000000000000
0x000000016ef03060:   0000000000000000 0000000000000000
0x000000016ef03070:   0000000000000000 0000000000000000
0x000000016ef03080:   0000000000000000 0000000000000000
0x000000016ef03090:   00000001030c8538 00000001030e4ed8
0x000000016ef030a0:   0003000000000000 0000003c00000000
0x000000016ef030b0:   000000016ef03000 000000016ee80000
0x000000016ef030c0:   000000016ee7c000 000000000008c000
0x000000016ef030d0:   0000000000004000 0000000000cb5d9a
0x000000016ef030e0:   000000016ef03000 000000016ef030ac
0x000000016ef030f0:   0000000000005507 0000000000004607
0x000000016ef03100:   00000000000008ff 0000000000000000 

Instructions: (pc=0x00000001030c4660)
0x00000001030c4640:   f140213f 54ffff41 aa0803e0 d65f03c0
0x00000001030c4650:   53137c28 f8685808 b40000c8 53054829
0x00000001030c4660:   b8695908 1ac12508 12000100 d65f03c0
0x00000001030c4670:   52800000 d65f03c0 53137c28 f8685808 

Register to memory mapping:

 x0=0x0000000108018108 is an unknown value
 x1=0x00000000ffffffff is an unknown value
 x2=0x0000001800001513 is an unknown value
 x3=0x0000550700000203 is an unknown value
 x4=0x00000d4a00000000 is an unknown value
 x5=0x0000550700000000 is an unknown value
 x6=0x0000000000000040 is an unknown value
 x7=0x0000000000000000 is an unknown value
 x8=0x0000000000041900 is an unknown value
 x9=0x0000000000003fff is an unknown value
x10=0x0000000000000021 is an unknown value
x11=0x0000b17f56860000 is an unknown value
x12=0x0000000000000040 is an unknown value
x13=0x0000600008587d74 is an unknown value
x14=0x00000000001ff800 is an unknown value
x15=0x00000000000007fb is an unknown value
x16=0xffffffffffffffd1 is an unknown value
x17=0x00000001ef999e70 is an unknown value
x18=0x0000000000000000 is an unknown value
x19=0x0000000000000000 is an unknown value
x20=0x0000000000000001 is an unknown value
x21=0x0000000000004607 is an unknown value
x22=0x00006000087e5460 is an unknown value
x23=0x000000000086f893 is an unknown value
x24=0x00000000000186a0 is an unknown value
x25=0x0000000000000002 is an unknown value
x26=0x00000000ffffffff is an unknown value
x27=0x0000000000000000 is an unknown value
x28=0x0000000000000001 is an unknown value


Stack: [0x000000016ee80000,0x000000016ef03000],  sp=0x000000016ef02f10,  free space=523k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libasyncProfiler.dylib+0x54660]  ThreadFilter::accept(int)+0x10
C  [libasyncProfiler.dylib+0x58544]  WallClock::threadEntry(void*)+0xc
C  [libsystem_pthread.dylib+0x6c0c]  _pthread_start+0x88


---------------  P R O C E S S  ---------------

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

heap address: 0x0000000580000000, size: 9216 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0
Compressed class space size: 1073741824 Address: 0x0000000800000000

Heap:
 PSYoungGen      total 1628672K, used 373933K [0x0000000700000000, 0x0000000770c00000, 0x00000007c0000000)
  eden space 1622016K, 23% used [0x0000000700000000,0x0000000716d2b458,0x0000000763000000)
  from space 6656K, 0% used [0x0000000763000000,0x0000000763000000,0x0000000763680000)
  to   space 112640K, 0% used [0x0000000769e00000,0x0000000769e00000,0x0000000770c00000)
 ParOldGen       total 816128K, used 158030K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 19% used [0x0000000580000000,0x0000000589a53b58,0x00000005b1d00000)
 Metaspace       used 248434K, capacity 271378K, committed 288128K, reserved 1300480K
  class space    used 30804K, capacity 33102K, committed 36480K, reserved 1048576K

Card table byte_map: [0x0000000106a00000,0x0000000107c04000] byte_map_base: 0x0000000103e00000

Marking Bits: (ParMarkBitMap*) 0x00000001044f21a8
 Begin Bits: [0x0000000150000000, 0x0000000159000000)
 End Bits:   [0x0000000159000000, 0x0000000162000000)

Polling page: 0x0000000102dd0000

CodeCache: size=131072Kb used=50636Kb max_used=50636Kb free=80435Kb
 bounds [0x0000000148000000, 0x000000014b180000, 0x0000000150000000]
 total_blobs=27249 nmethods=25973 adapters=1195
 compilation: enabled

Compilation events (10 events):
Event: 6554.630 Thread 0x000000011f231000 26615       1       org.apache.spark.rpc.netty.Dispatcher::endpointRefs (5 bytes)
Event: 6554.630 Thread 0x000000011f231000 nmethod 26615 0x000000014b177cd0 code [0x000000014b177e00, 0x000000014b177e90]
Event: 6554.634 Thread 0x000000011f231000 26616  s    1       java.lang.Thread::nextThreadID (10 bytes)
Event: 6554.634 Thread 0x000000011f231000 nmethod 26616 0x000000014b177f10 code [0x000000014b178080, 0x000000014b1782d0]
Event: 6554.635 Thread 0x000000011f231000 26617       1       java.lang.Thread::init (214 bytes)
Event: 6554.636 Thread 0x000000011f231000 nmethod 26617 0x000000014b178390 code [0x000000014b178600, 0x000000014b178fb8]
Event: 6554.638 Thread 0x000000011f231000 26619   !   1       java.lang.ThreadGroup::addUnstarted (40 bytes)
Event: 6554.638 Thread 0x000000011f231000 nmethod 26619 0x000000014b17a0d0 code [0x000000014b17a240, 0x000000014b17a568]
Event: 6554.639 Thread 0x000000011f231000 26618       1       java.lang.Thread::init (12 bytes)
Event: 6554.639 Thread 0x000000011f231000 nmethod 26618 0x000000014b17a810 code [0x000000014b17a980, 0x000000014b17aaa8]

GC Heap History (10 events):
Event: 2817.506 GC heap before
{Heap before GC invocations=32 (full 7):
 PSYoungGen      total 1414144K, used 37106K [0x0000000700000000, 0x0000000771400000, 0x00000007c0000000)
  eden space 1290240K, 0% used [0x0000000700000000,0x0000000700000000,0x000000074ec00000)
  from space 123904K, 29% used [0x000000074ec00000,0x000000075103cbe0,0x0000000756500000)
  to   space 116736K, 0% used [0x000000076a200000,0x000000076a200000,0x0000000771400000)
 ParOldGen       total 816128K, used 178521K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 21% used [0x0000000580000000,0x000000058ae56568,0x00000005b1d00000)
 Metaspace       used 248129K, capacity 272354K, committed 286720K, reserved 1300480K
  class space    used 31128K, capacity 33594K, committed 36352K, reserved 1048576K
Event: 2817.869 GC heap after
Heap after GC invocations=32 (full 7):
 PSYoungGen      total 1414144K, used 0K [0x0000000700000000, 0x0000000771400000, 0x00000007c0000000)
  eden space 1290240K, 0% used [0x0000000700000000,0x0000000700000000,0x000000074ec00000)
  from space 123904K, 0% used [0x000000074ec00000,0x000000074ec00000,0x0000000756500000)
  to   space 116736K, 0% used [0x000000076a200000,0x000000076a200000,0x0000000771400000)
 ParOldGen       total 816128K, used 177449K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 21% used [0x0000000580000000,0x000000058ad4a498,0x00000005b1d00000)
 Metaspace       used 246650K, capacity 269236K, committed 286720K, reserved 1300480K
  class space    used 30634K, capacity 32922K, committed 36352K, reserved 1048576K
}
Event: 3707.410 GC heap before
{Heap before GC invocations=33 (full 7):
 PSYoungGen      total 1414144K, used 257683K [0x0000000700000000, 0x0000000771400000, 0x00000007c0000000)
  eden space 1290240K, 19% used [0x0000000700000000,0x000000070fba4cf0,0x000000074ec00000)
  from space 123904K, 0% used [0x000000074ec00000,0x000000074ec00000,0x0000000756500000)
  to   space 116736K, 0% used [0x000000076a200000,0x000000076a200000,0x0000000771400000)
 ParOldGen       total 816128K, used 177449K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 21% used [0x0000000580000000,0x000000058ad4a498,0x00000005b1d00000)
 Metaspace       used 247367K, capacity 270026K, committed 287232K, reserved 1300480K
  class space    used 30699K, capacity 32970K, committed 36352K, reserved 1048576K
Event: 3707.446 GC heap after
Heap after GC invocations=33 (full 7):
 PSYoungGen      total 1733632K, used 13352K [0x0000000700000000, 0x0000000770f00000, 0x00000007c0000000)
  eden space 1622016K, 0% used [0x0000000700000000,0x0000000700000000,0x0000000763000000)
  from space 111616K, 11% used [0x000000076a200000,0x000000076af0a2a8,0x0000000770f00000)
  to   space 114176K, 0% used [0x0000000763000000,0x0000000763000000,0x0000000769f80000)
 ParOldGen       total 816128K, used 177457K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 21% used [0x0000000580000000,0x000000058ad4c498,0x00000005b1d00000)
 Metaspace       used 247367K, capacity 270026K, committed 287232K, reserved 1300480K
  class space    used 30699K, capacity 32970K, committed 36352K, reserved 1048576K
}
Event: 3707.446 GC heap before
{Heap before GC invocations=34 (full 8):
 PSYoungGen      total 1733632K, used 13352K [0x0000000700000000, 0x0000000770f00000, 0x00000007c0000000)
  eden space 1622016K, 0% used [0x0000000700000000,0x0000000700000000,0x0000000763000000)
  from space 111616K, 11% used [0x000000076a200000,0x000000076af0a2a8,0x0000000770f00000)
  to   space 114176K, 0% used [0x0000000763000000,0x0000000763000000,0x0000000769f80000)
 ParOldGen       total 816128K, used 177457K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 21% used [0x0000000580000000,0x000000058ad4c498,0x00000005b1d00000)
 Metaspace       used 247367K, capacity 270026K, committed 287232K, reserved 1300480K
  class space    used 30699K, capacity 32970K, committed 36352K, reserved 1048576K
Event: 3707.843 GC heap after
Heap after GC invocations=34 (full 8):
 PSYoungGen      total 1733632K, used 0K [0x0000000700000000, 0x0000000770f00000, 0x00000007c0000000)
  eden space 1622016K, 0% used [0x0000000700000000,0x0000000700000000,0x0000000763000000)
  from space 111616K, 0% used [0x000000076a200000,0x000000076a200000,0x0000000770f00000)
  to   space 114176K, 0% used [0x0000000763000000,0x0000000763000000,0x0000000769f80000)
 ParOldGen       total 816128K, used 165629K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 20% used [0x0000000580000000,0x000000058a1bf4f8,0x00000005b1d00000)
 Metaspace       used 247367K, capacity 270026K, committed 287232K, reserved 1300480K
  class space    used 30699K, capacity 32970K, committed 36352K, reserved 1048576K
}
Event: 5766.556 GC heap before
{Heap before GC invocations=35 (full 8):
 PSYoungGen      total 1733632K, used 406938K [0x0000000700000000, 0x0000000770f00000, 0x00000007c0000000)
  eden space 1622016K, 25% used [0x0000000700000000,0x0000000718d66948,0x0000000763000000)
  from space 111616K, 0% used [0x000000076a200000,0x000000076a200000,0x0000000770f00000)
  to   space 114176K, 0% used [0x0000000763000000,0x0000000763000000,0x0000000769f80000)
 ParOldGen       total 816128K, used 165629K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 20% used [0x0000000580000000,0x000000058a1bf4f8,0x00000005b1d00000)
 Metaspace       used 247958K, capacity 270748K, committed 287872K, reserved 1300480K
  class space    used 30713K, capacity 33021K, committed 36480K, reserved 1048576K
Event: 5766.610 GC heap after
Heap after GC invocations=35 (full 8):
 PSYoungGen      total 1628672K, used 6528K [0x0000000700000000, 0x0000000770c00000, 0x00000007c0000000)
  eden space 1622016K, 0% used [0x0000000700000000,0x0000000700000000,0x0000000763000000)
  from space 6656K, 98% used [0x0000000763000000,0x0000000763660070,0x0000000763680000)
  to   space 112640K, 0% used [0x0000000769e00000,0x0000000769e00000,0x0000000770c00000)
 ParOldGen       total 816128K, used 165637K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 20% used [0x0000000580000000,0x000000058a1c14f8,0x00000005b1d00000)
 Metaspace       used 247958K, capacity 270748K, committed 287872K, reserved 1300480K
  class space    used 30713K, capacity 33021K, committed 36480K, reserved 1048576K
}
Event: 5766.610 GC heap before
{Heap before GC invocations=36 (full 9):
 PSYoungGen      total 1628672K, used 6528K [0x0000000700000000, 0x0000000770c00000, 0x00000007c0000000)
  eden space 1622016K, 0% used [0x0000000700000000,0x0000000700000000,0x0000000763000000)
  from space 6656K, 98% used [0x0000000763000000,0x0000000763660070,0x0000000763680000)
  to   space 112640K, 0% used [0x0000000769e00000,0x0000000769e00000,0x0000000770c00000)
 ParOldGen       total 816128K, used 165637K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 20% used [0x0000000580000000,0x000000058a1c14f8,0x00000005b1d00000)
 Metaspace       used 247958K, capacity 270748K, committed 287872K, reserved 1300480K
  class space    used 30713K, capacity 33021K, committed 36480K, reserved 1048576K
Event: 5766.949 GC heap after
Heap after GC invocations=36 (full 9):
 PSYoungGen      total 1628672K, used 0K [0x0000000700000000, 0x0000000770c00000, 0x00000007c0000000)
  eden space 1622016K, 0% used [0x0000000700000000,0x0000000700000000,0x0000000763000000)
  from space 6656K, 0% used [0x0000000763000000,0x0000000763000000,0x0000000763680000)
  to   space 112640K, 0% used [0x0000000769e00000,0x0000000769e00000,0x0000000770c00000)
 ParOldGen       total 816128K, used 158030K [0x0000000580000000, 0x00000005b1d00000, 0x0000000700000000)
  object space 816128K, 19% used [0x0000000580000000,0x0000000589a53b58,0x00000005b1d00000)
 Metaspace       used 247958K, capacity 270748K, committed 287872K, reserved 1300480K
  class space    used 30713K, capacity 33021K, committed 36480K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Classes redefined (1 events):
Event: 0.837 Thread 0x0000000105a2f800 redefined class name=java.lang.Throwable, count=1

Internal exceptions (10 events):
Event: 1123.083 Thread 0x000000011eed4800 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.$anonfun$postToAll$3(Lorg/apache/spark/util/ListenerBus;Ljava/lang/Object;JLscala/runtime/LazyRef;Ljava/lang/Object;)Ljava/lang/String;> (0x0000000743357fb0) thrown at [/Users/<USER>/jenkins/blu
Event: 1745.279 Thread 0x0000000168ef9000 Exception <a 'java/lang/NoSuchMethodError': <clinit>> (0x000000074b528878) thrown at [/Users/<USER>/jenkins/blue/workspace/Zulu/zulu-platform-build/27961/zulu-src.git/hotspot/src/share/vm/prims/jni.cpp, line 1617]
Event: 2826.140 Thread 0x0000000168ef9000 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.lambda$builder$1(Ljava/lang/String;)Lio/micrometer/core/instrument/Timer$Builder;> (0x00000007055f7360) thrown at [/Users/<USER>/jenkins/blue/workspace/Zulu/zulu-platform-build/27961/zulu-src.gi
Event: 6554.454 Thread 0x000000011f354000 Exception <a 'java/lang/NoClassDefFoundError': jdk/internal/vm/VMSupport> (0x000000070b8f92a8) thrown at [/Users/<USER>/jenkins/blue/workspace/Zulu/zulu-platform-build/27961/zulu-src.git/hotspot/src/share/vm/classfile/systemDictionary.cpp, line 209]
Event: 6554.521 Thread 0x0000000166292800 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.deleteById(Ljava/io/Serializable;)I> (0x000000070e757268) thrown at [/Users/<USER>/jenkins/blue/workspace/Zulu/zulu-platform-build/27961/zulu-src.git/hotspot/src/share/vm/interpreter/linkResolve
Event: 6554.524 Thread 0x0000000166292800 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.deleteById(Ljava/lang/Object;Z)I> (0x000000070e764f80) thrown at [/Users/<USER>/jenkins/blue/workspace/Zulu/zulu-platform-build/27961/zulu-src.git/hotspot/src/share/vm/interpreter/linkResolver.c
Event: 6554.532 Thread 0x0000000163561800 Exception <a 'java/io/IOException'> (0x000000070eac3788) thrown at [/Users/<USER>/jenkins/blue/workspace/Zulu/zulu-platform-build/27961/zulu-src.git/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 6554.532 Thread 0x0000000166292800 Exception <a 'java/lang/NoSuchMethodError': <clinit>> (0x000000070e7cfc90) thrown at [/Users/<USER>/jenkins/blue/workspace/Zulu/zulu-platform-build/27961/zulu-src.git/hotspot/src/share/vm/prims/jni.cpp, line 1617]
Event: 6554.545 Thread 0x000000016643a000 Exception <a 'java/lang/InterruptedException'> (0x00000007007f0dc8) thrown at [/Users/<USER>/jenkins/blue/workspace/Zulu/zulu-platform-build/27961/zulu-src.git/hotspot/src/share/vm/runtime/objectMonitor.cpp, line 1692]
Event: 6554.599 Thread 0x0000000105629800 Exception <a 'java/lang/InterruptedException'> (0x00000007004bc358) thrown at [/Users/<USER>/jenkins/blue/workspace/Zulu/zulu-platform-build/27961/zulu-src.git/hotspot/src/share/vm/runtime/objectMonitor.cpp, line 1692]

Events (10 events):
Event: 6554.642 Thread 0x000000016358f000 DEOPT UNPACKING pc=0x0000000148039480 sp=0x0000000177c3e560 mode 1
Event: 6554.642 Thread 0x000000016358f000 DEOPT PACKING pc=0x0000000149e3f784 sp=0x0000000177c3e800
Event: 6554.642 Thread 0x000000016358f000 DEOPT UNPACKING pc=0x0000000148039480 sp=0x0000000177c3e630 mode 1
Event: 6554.642 loading class scala/Function0
Event: 6554.642 loading class scala/Function0 done
Event: 6554.642 Thread 0x000000016358f000 Thread exited: 0x000000016358f000
Event: 6554.642 loading class scala/Function0
Event: 6554.642 loading class scala/Function0 done
Event: 6554.642 loading class scala/Function1
Event: 6554.642 loading class scala/Function1 done


Dynamic libraries:
0x000000019d04a000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x0000000184811000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x0000000187b00000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x0000000181e99000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000018ea7d000 	/usr/lib/libSystem.B.dylib
0x0000000185ca3000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000022edb3000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x0000000196070000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x000000018c611000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x0000000191247000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x000000019159e000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000025d823000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00000001e7799000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000026289c000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x00000002618e4000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x0000000181afd000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x00000001906ab000 	/usr/lib/libspindump.dylib
0x0000000185e55000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000018de6f000 	/usr/lib/libbsm.0.dylib
0x000000018a094000 	/usr/lib/libapp_launch_measurement.dylib
0x000000018943d000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x000000018a098000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x000000018bc2b000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x000000018ce4f000 	/usr/lib/liblangid.dylib
0x000000018c617000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x000000018687b000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x0000000186da1000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x000000019674f000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x00000001904ee000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x000000018bc08000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000018946e000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000018e9c7000 	/usr/lib/libz.1.dylib
0x000000019a662000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x000000018c5fc000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x0000000184076000 	/usr/lib/libicucore.A.dylib
0x0000000192625000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000019154f000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001ad5b9000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x00000001867c6000 	/usr/lib/libMobileGestalt.dylib
0x000000018c2f5000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x0000000189975000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x0000000183c6b000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00000001960ac000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x0000000189d9b000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x0000000183536000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000018955c000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x0000000190b14000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x00000001867c4000 	/usr/lib/libenergytrace.dylib
0x00000001a195f000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x00000001846c1000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00000001964a3000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000018a025000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001e1169000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x000000018a0e2000 	/usr/lib/libxml2.2.dylib
0x000000018dd53000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x0000000180440000 	/usr/lib/libobjc.A.dylib
0x000000018074d000 	/usr/lib/libc++.1.dylib
0x0000000196424000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x00000001874d1000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x00000001808a9000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x000000018c9d1000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x0000000183317000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001e26ae000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001e2c32000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x00000001e2c35000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x000000018c652000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00000001e835c000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00000001d4978000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000018ea82000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00000001c028c000 	/usr/lib/swift/libswiftAccelerate.dylib
0x0000000191fba000 	/usr/lib/swift/libswiftCore.dylib
0x00000001a9c62000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001a9cbc000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001a7076000 	/usr/lib/swift/libswiftDarwin.dylib
0x00000002694b1000 	/usr/lib/swift/libswiftDataDetection.dylib
0x0000000197f9b000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001a9cbd000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001b6813000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001c57e9000 	/usr/lib/swift/libswiftOSLog.dylib
0x000000019ac08000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x00000002694de000 	/usr/lib/swift/libswiftObservation.dylib
0x00000001bbc3a000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001c027d000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001a9c74000 	/usr/lib/swift/libswiftXPC.dylib
0x00000002695c4000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x00000002695c7000 	/usr/lib/swift/libswift_Concurrency.dylib
0x0000000269726000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x00000002697b9000 	/usr/lib/swift/libswift_errno.dylib
0x00000002697bb000 	/usr/lib/swift/libswift_math.dylib
0x00000002697be000 	/usr/lib/swift/libswift_signal.dylib
0x00000002697bf000 	/usr/lib/swift/libswift_stdio.dylib
0x00000002697c0000 	/usr/lib/swift/libswift_time.dylib
0x000000019ac0c000 	/usr/lib/swift/libswiftos.dylib
0x00000001ad510000 	/usr/lib/swift/libswiftsimd.dylib
0x00000002697c1000 	/usr/lib/swift/libswiftsys_time.dylib
0x00000002697c2000 	/usr/lib/swift/libswiftunistd.dylib
0x000000018ecaa000 	/usr/lib/libcompression.dylib
0x00000001911a7000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x000000019018d000 	/usr/lib/libate.dylib
0x000000018ea77000 	/usr/lib/system/libcache.dylib
0x000000018ea32000 	/usr/lib/system/libcommonCrypto.dylib
0x000000018ea5d000 	/usr/lib/system/libcompiler_rt.dylib
0x000000018ea52000 	/usr/lib/system/libcopyfile.dylib
0x000000018059b000 	/usr/lib/system/libcorecrypto.dylib
0x0000000180681000 	/usr/lib/system/libdispatch.dylib
0x0000000180841000 	/usr/lib/system/libdyld.dylib
0x000000018ea6d000 	/usr/lib/system/libkeymgr.dylib
0x000000018ea15000 	/usr/lib/system/libmacho.dylib
0x000000018de48000 	/usr/lib/system/libquarantine.dylib
0x000000018ea6a000 	/usr/lib/system/libremovefile.dylib
0x0000000186840000 	/usr/lib/system/libsystem_asl.dylib
0x0000000180530000 	/usr/lib/system/libsystem_blocks.dylib
0x00000001806cb000 	/usr/lib/system/libsystem_c.dylib
0x000000018ea61000 	/usr/lib/system/libsystem_collections.dylib
0x000000018ce3c000 	/usr/lib/system/libsystem_configuration.dylib
0x000000018bbd7000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000018e558000 	/usr/lib/system/libsystem_coreservices.dylib
0x0000000184342000 	/usr/lib/system/libsystem_darwin.dylib
0x00000002698f9000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000018ea6e000 	/usr/lib/system/libsystem_dnssd.dylib
0x00000002698fd000 	/usr/lib/system/libsystem_eligibility.dylib
0x00000001806c8000 	/usr/lib/system/libsystem_featureflags.dylib
0x0000000180879000 	/usr/lib/system/libsystem_info.dylib
0x000000018e9d6000 	/usr/lib/system/libsystem_m.dylib
0x000000018063a000 	/usr/lib/system/libsystem_malloc.dylib
0x00000001867a9000 	/usr/lib/system/libsystem_networkextension.dylib
0x00000001847a4000 	/usr/lib/system/libsystem_notify.dylib
0x000000018ce41000 	/usr/lib/system/libsystem_sandbox.dylib
0x0000000269905000 	/usr/lib/system/libsystem_sanitizers.dylib
0x000000018ea66000 	/usr/lib/system/libsystem_secinit.dylib
0x00000001807f8000 	/usr/lib/system/libsystem_kernel.dylib
0x0000000180871000 	/usr/lib/system/libsystem_platform.dylib
0x0000000180834000 	/usr/lib/system/libsystem_pthread.dylib
0x000000018839a000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000018057f000 	/usr/lib/system/libsystem_trace.dylib
0x000000018ea40000 	/usr/lib/system/libunwind.dylib
0x0000000180534000 	/usr/lib/system/libxpc.dylib
0x00000001807da000 	/usr/lib/libc++abi.dylib
0x00000002684d1000 	/usr/lib/libRosetta.dylib
0x0000000184640000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000018ea4a000 	/usr/lib/liboah.dylib
0x000000018ea7f000 	/usr/lib/libfakelink.dylib
0x000000019a115000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00000001a6b9f000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x00000001863de000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x000000018a05c000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x000000018434d000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x00000001894d1000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000018e55f000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000018ebcc000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x0000000188315000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x0000000180de8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x000000018ffe2000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x000000018a06a000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000018ec61000 	/usr/lib/libapple_nghttp2.dylib
0x0000000187f76000 	/usr/lib/libsqlite3.dylib
0x0000000192c9d000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00000001882ac000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00000001883a3000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x000000018ea19000 	/usr/lib/system/libkxld.dylib
0x000000022a96d000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x0000000268329000 	/usr/lib/libCoreEntitlements.dylib
0x0000000245da9000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x0000000187f5b000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000018e53f000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000018de57000 	/usr/lib/libcoretls.dylib
0x0000000190058000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000018eca4000 	/usr/lib/libpam.2.dylib
0x00000001900cc000 	/usr/lib/libxar.1.dylib
0x000000018ead4000 	/usr/lib/libarchive.2.dylib
0x000000019439f000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000022edd7000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000024edd0000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000024faa2000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x0000000269555000 	/usr/lib/swift/libswiftSystem.dylib
0x000000018ce4a000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00000001a9b4d000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x000000019745c000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00000001e765e000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x000000018a26d000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x000000018630c000 	/usr/lib/libboringssl.dylib
0x0000000188389000 	/usr/lib/libdns_services.dylib
0x00000001a8d01000 	/usr/lib/libquic.dylib
0x0000000191f49000 	/usr/lib/libusrtcp.dylib
0x00000001cd653000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x00000002694b2000 	/usr/lib/swift/libswiftDistributed.dylib
0x000000026954b000 	/usr/lib/swift/libswiftSynchronization.dylib
0x00000001863dd000 	/usr/lib/libnetwork.dylib
0x00000001ba879000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000018ec3c000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x0000000198731000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001c931f000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x000000019870d000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x00000002681f4000 	/usr/lib/libAppleArchive.dylib
0x000000018e54b000 	/usr/lib/libbz2.1.0.dylib
0x0000000190039000 	/usr/lib/liblzma.5.dylib
0x000000018db47000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00000001a9bd6000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x0000000190227000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x00000001818f4000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000023e0f9000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x0000000198233000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000018dd7f000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000018dee1000 	/usr/lib/libgermantok.dylib
0x000000018cf6e000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x00000001881c7000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x0000000198300000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x000000018dd70000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x0000000199f50000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000018c022000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x0000000186858000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x000000019daeb000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x0000000190b12000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x0000000182e3e000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x000000018bec6000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x000000018bc21000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000018a1cc000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000018eca2000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000024e121000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x0000000190b5a000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x000000018ce48000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x000000019005a000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x00000001900db000 	/usr/lib/libutil.dylib
0x0000000258fe0000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x0000000189565000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x000000019647e000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x0000000190113000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x0000000181260000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x0000000192874000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x0000000187687000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x0000000191130000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x0000000192c67000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x0000000192c5e000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x0000000192846000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000018db14000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00000002351d2000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x0000000190660000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x0000000189d49000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x00000002688b5000 	/usr/lib/libhvf.dylib
0x000000024a02f000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x00000002694fb000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x0000000269683000 	/usr/lib/swift/libswift_RegexParser.dylib
0x00000001909db000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000019047c000 	/usr/lib/libexpat.1.dylib
0x0000000191006000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x0000000191031000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000019111b000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x0000000190a21000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x00000001910c2000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x00000001910b9000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x0000000239ea0000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x000000023529d000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00000001e115b000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x0000000235e12000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x00000002351d3000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x000000019bd51000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x000000024d290000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x0000000190654000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001e11b9000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001e117d000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001e1345000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001e1186000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001e117a000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00000001e1163000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x000000018cd8c000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000018e4aa000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000018def9000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000018e330000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000018e145000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000018e362000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00000001e4a50000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00000001e4a32000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x00000001810db000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001ae8b6000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001bc02f000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001a9c3d000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x00000001910ed000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x0000000192bf2000 	/usr/lib/libcups.2.dylib
0x0000000192c8c000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x00000001928f2000 	/usr/lib/libresolv.9.dylib
0x000000018eab9000 	/usr/lib/libiconv.2.dylib
0x00000001906b2000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x000000019ab5f000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x0000000190497000 	/usr/lib/libheimdal-asn1.dylib
0x000000018a02f000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x0000000192cef000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x000000018a03d000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000018ea14000 	/usr/lib/libcharset.1.dylib
0x00000001e16e1000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001a3325000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x0000000241a28000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x000000019282d000 	/usr/lib/libAudioStatistics.dylib
0x000000018be9e000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x0000000182f5f000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x000000019ce77000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x00000001927d0000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x0000000194107000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x0000000190581000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x000000025ba9b000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x000000022f894000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x00000001c094f000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00000001a9c41000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x00000001906a1000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x0000000192c70000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00000001a8dfa000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x0000000192b11000 	/usr/lib/libSMC.dylib
0x0000000190fd0000 	/usr/lib/libAudioToolboxUtility.dylib
0x0000000192c7e000 	/usr/lib/libperfcheck.dylib
0x000000022b72a000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00000001cd41a000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001a9c08000 	/usr/lib/libmis.dylib
0x00000001925de000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x0000000191121000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001e2d82000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x000000019883e000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x000000019037b000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x0000000197365000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x000000019ab60000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000018dbd8000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x0000000232838000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x000000019679f000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00000001b2252000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x0000000268172000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00000001a33e1000 	/usr/lib/libAccessibility.dylib
0x000000018da22000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000018ed80000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000018dee4000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000018ec7b000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000018ed7b000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x000000018cf75000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x0000000181a01000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x0000000243199000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x00000001910b4000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x0000000191094000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x00000001910bc000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000001cd821000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00000001a2600000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000025f2d7000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x0000000190433000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00000001983ad000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x0000000192e62000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x000000019754d000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x0000000197498000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x0000000192c51000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x000000018a22d000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x00000001904a1000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x000000019021e000 	/usr/lib/libIOReport.dylib
0x000000022b33a000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x0000000268534000 	/usr/lib/libTLE.dylib
0x00000001de1b7000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x000000018de81000 	/usr/lib/libmecab.dylib
0x0000000181b91000 	/usr/lib/libCRFSuite.dylib
0x000000018ce51000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000018ec34000 	/usr/lib/libThaiTokenizer.dylib
0x000000018de4b000 	/usr/lib/libCheckFix.dylib
0x0000000189470000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000023e9db000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x0000000184680000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001bc139000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x00000001900df000 	/usr/lib/libxslt.1.dylib
0x000000018de0e000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x000000019a2d3000 	/usr/lib/libcurl.4.dylib
0x0000000268768000 	/usr/lib/libcrypto.46.dylib
0x0000000269290000 	/usr/lib/libssl.48.dylib
0x0000000199fac000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x0000000199fe8000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x000000019290f000 	/usr/lib/libsasl2.2.dylib
0x00000001a7075000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x0000000196dcd000 	/usr/lib/swift/libswiftFoundation.dylib
0x00000001ddc41000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00000001c9ad3000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x000000024fb99000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x0000000196099000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x0000000103e3c000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/server/libjvm.dylib
0x0000000102de8000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libverify.dylib
0x0000000102e4c000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libjava.dylib
0x0000000102ee8000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libjdwp.dylib
0x0000000102e00000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libnpt.dylib
0x0000000102e98000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libinstrument.dylib
0x0000000103070000 	/private/var/folders/63/xn2bgkf97fs884npb0k5y5v40000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x0000000102f78000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libzip.dylib
0x0000000103048000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libdt_socket.dylib
0x000000011e594000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libnio.dylib
0x0000000128100000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libnet.dylib
0x000000012a72c000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libmanagement.dylib
0x000000012a7ac000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libsunec.dylib
0x000000012a794000 	/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/libjaas_unix.dylib
0x000000016902c000 	/Users/<USER>/Downloads/sp/proxverse-project/liblz4-java5833773900211750609.dylib
0x0000000169000000 	/Users/<USER>/Downloads/sp/proxverse-project/snappy-1.1.8-db67ea9e-ef20-4d82-892a-8ff5095c15e7-libsnappyjava.dylib

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:54297,suspend=y,server=n -javaagent:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar=file:///var/folders/63/xn2bgkf97fs884npb0k5y5v40000gn/T/capture16852816548527603332.props -Djava.io.tmpdir=/Users/<USER>/Downloads/sp/proxverse-project -agentpath:/private/var/folders/63/xn2bgkf97fs884npb0k5y5v40000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/MainApplication_2025_08_08_134233.jfr,log=/private/var/folders/63/xn2bgkf97fs884npb0k5y5v40000gn/T/MainApplication_2025_08_08_134233.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.profiles.active=feng -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.sp.web.main.MainApplication
java_class_path (initial): /Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/cat.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/charsets.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/crs-agent.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/dnsns.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/jaccess.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/localedata.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/nashorn.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/sunec.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/zipfs.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/jce.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/jfr.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/jsse.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/management-agent.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/resources.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/rt.jar:/Users/<USER>/Documents/shangping_develop/backend/process-mining-platform2/process-mining-platform/proxverse-main/target/classes:/Users/<USER>/.m2/repository/org/apache/hadoop/hadoop-aws/3.3.4/hadoop-aws-3.3.4.jar:/Users/<USER>/.m2/repository/org/wildfly/openssl/wildfly-openssl/1.0.7.Final/wildfly-openssl-1.0.7.Final.jar:/Users/<USER>/.m2/repository/com/amazonaws/aws-java-sdk-bundle/1.12.262/aws-java-sdk-bundle-1.12.262.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home
CLASSPATH=/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/lib/tools.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/lib/dt.jar
PATH=/opt/homebrew/opt/mysql@8.0/bin:/Users/<USER>/.nvm/versions/node/v20.19.3/bin:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/bin:/usr/local/opt/ruby/bin:/usr/local/sbin:/usr/local/opt/coreutils/libexec/gnubin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts:/bin:/Users/<USER>/go/bin::/Users/<USER>/Documents/develop_env/apache-maven-3.9.7/bin:/Users/<USER>/Documents/shangping_develop/backend/process-mining-platform/hadoop-3.3.1/bin:/Users/<USER>/Documents/shangping_develop/backend/process-mining-platform/hadoop-3.3.1/sbin
SHELL=/bin/zsh

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x565ff4], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x565ff4], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x45e87c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x45e87c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x45e87c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x45e87c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x45f1a0], sa_mask[0]=00000000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x45d128], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x45d128], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x45d128], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x45d128], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:54:29 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6030 arm64
rlimit: STACK 8176k, CORE 0k, NPROC 6000, NOFILE 10240, AS infinity
load average:2.31 3.53 3.78

CPU:total 12 (initial active 12) 0x61:0x0:0x5f4dea93:0, simd, crc, lse

Memory: 16k page, physical 37748736k(454512k free)

/proc/meminfo:


vm_info: OpenJDK 64-Bit Server VM (25.412-b08) for bsd-aarch64 JRE (Zulu 8.78.0.19-CA-macos-aarch64) (1.8.0_412-b08), built on Mar 28 2024 03:46:06 by "zulu_re" with gcc Apple LLVM 12.0.0 (clang-1200.0.32.28)

time: Fri Aug  8 15:31:48 2025
timezone: CST
elapsed time: 6554.647560 seconds (0d 1h 49m 14s)

