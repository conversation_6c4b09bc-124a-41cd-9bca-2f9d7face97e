package com.sp.proxverse.engine.controller;

import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.dto.conformance.ConformanceGeneralData;
import com.sp.proxverse.common.model.vo.conformance.request.QueryConformanceGeneralRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 改进的一致性分析控制器
 * 提供更好的异常处理和诊断信息
 */
@Slf4j
@RestController
@RequestMapping(value = "/conformance/improved")
public class ImprovedConformanceController {

    @Autowired
    private ConformanceServiceScala conformanceService;

    @Autowired
    private ConformanceFixService conformanceFixService;

    /**
     * 改进的获取一致性概览数据接口
     * 提供详细的错误信息而不是静默返回"--"
     */
    @PostMapping(value = "/getConformanceGeneralData")
    public Response<ConformanceGeneralData> getConformanceGeneralDataImproved(
            @Valid @RequestBody QueryConformanceGeneralRequest request) {

        try {
            log.info("获取一致性概览数据: topicId={}, topicSheetId={}", 
                    request.getTopicId(), request.getTopicSheetId());

            ConformanceGeneralData conformanceGeneralData =
                    conformanceService.getConformanceGeneralData(
                            request.getTopicId(),
                            request.getTopicSheetId(),
                            request.getStartTime(),
                            request.getEndTime());

            return Response.success(conformanceGeneralData);

        } catch (Exception e) {
            log.error("获取一致性概览数据失败: topicId={}, topicSheetId={}", 
                    request.getTopicId(), request.getTopicSheetId(), e);

            // 尝试自动修复
            boolean fixed = conformanceFixService.fixConformanceIssue(request.getTopicSheetId());
            if (fixed) {
                try {
                    // 重试一次
                    ConformanceGeneralData conformanceGeneralData =
                            conformanceService.getConformanceGeneralData(
                                    request.getTopicId(),
                                    request.getTopicSheetId(),
                                    request.getStartTime(),
                                    request.getEndTime());
                    return Response.success(conformanceGeneralData);
                } catch (Exception retryException) {
                    log.error("修复后重试仍然失败", retryException);
                }
            }

            // 返回详细的错误信息
            return Response.error(500, "一致性数据获取失败: " + e.getMessage());
        }
    }

    /**
     * 诊断接口
     */
    @PostMapping(value = "/diagnose")
    public Response<String> diagnoseConformanceIssue(@RequestBody Integer topicSheetId) {
        try {
            conformanceFixService.diagnoseConformanceIssue(topicSheetId);
            return Response.success("诊断完成，请查看日志");
        } catch (Exception e) {
            return Response.error(500, "诊断失败: " + e.getMessage());
        }
    }

    /**
     * 手动修复接口
     */
    @PostMapping(value = "/fix")
    public Response<String> fixConformanceIssue(@RequestBody Integer topicSheetId) {
        try {
            boolean success = conformanceFixService.fixConformanceIssue(topicSheetId);
            if (success) {
                return Response.success("修复成功");
            } else {
                return Response.error(500, "修复失败");
            }
        } catch (Exception e) {
            return Response.error(500, "修复过程中发生异常: " + e.getMessage());
        }
    }
}
