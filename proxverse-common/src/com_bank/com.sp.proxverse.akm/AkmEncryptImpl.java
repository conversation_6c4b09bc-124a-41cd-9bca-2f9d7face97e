package com.sp.proxverse.akm;
import com.bocom.jump.comp.akm.client.AKMClient;
import com.sp.proxverse.common.action.AkmEncrypt;
import com.sp.proxverse.common.config.AkmApplicationResource;

import java.util.HashMap;
import java.util.Map;

@lombok.extern.slf4j.Slf4j
public class AkmEncryptImpl implements AkmEncrypt {
    @Override
    public String akmEncryptPassword(String password,String service,String env,String address,String token) {
        return passwordDecrypt(password,type,env,address,token);
    }

    @Override
    public String akmEncryptPassword(String password) {
        return passwordDecrypt(password);
    }

    private static final Map<String, String> passwordCache = new HashMap<>();

    //解密
    public static String passwordDecrypt(String password) {
        if (passwordCache.containsKey(password)) {
            return passwordCache.get(password);
        }
        String dbInfo = null;
        try {
            System.setProperty("akm.service.type", AkmApplicationResource.getServiceTypeValue());
                    System.setProperty("akm.env.name", AkmApplicationResource.getEnvNameValue());
            System.setProperty("akm.remote.address.list", AkmApplicationResource.getRemoteAddressListValue());
                    System.setProperty("akm.app.token", AkmApplicationResource.getAppTokenValue());
            AKMClient akmclient = new AKMclient();
            dbInfo = akmclient.getDbInfo(password);
            passwordCache.put(password, dbInfo);
        } catch (Exception e) {
            log.info("passwordDecrypt Exception:{}",e);
            throw new RuntimeException();
        }
        return dbInfo;
    }

    //解密
    public static String passwordDecrypt(String password,String service,String env,String address,String token) {
        if (passwordCache.containsKey(password)) {
            return passwordCache.get(password);
        }
        String dbInfo = null;
        try {
            System.setProperty("akm.service.type", service);
            System.setProperty("akm.env.name", env);
            System.setProperty("akm.remote.address.list", address);
            System.setProperty("akm.app.token", token);
            AKMClient akmclient = new AKMclient();
            dbInfo = akmclient.getDbInfo(password);
            passwordCache.put(password, dbInfo);
        } catch (Exception e) {
            log.info("passwordDecrypt Exception:{}",e);
            throw new RuntimeException();
        }
        return dbInfo;
    }
}