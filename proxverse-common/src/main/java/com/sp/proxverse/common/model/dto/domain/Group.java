package com.sp.proxverse.common.model.dto.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_group")
public class Group extends BaseEntity {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty("邮箱")
  private String email;

  @ApiModelProperty("手机号")
  private String phone;

  @ApiModelProperty("用户名")
  private String company;

  @ApiModelProperty("密码")
  private String password;

  @ApiModelProperty("全局提示词")
  private String globalPrompt;

  @ApiModelProperty("流程标准库的Id，通过, 分割")
  private String datumLibraryProcessId;

  public List<Integer> getDatumLibraryProcessIdList() {
    List<Integer> arrayList = new ArrayList<>();
    if (StringUtils.isNotBlank(datumLibraryProcessId)) {
      String[] split = this.datumLibraryProcessId.split(",");
      for (String s : split) {
        arrayList.add(Integer.valueOf(s));
      }
    }
    return arrayList;
  }

  @ApiModelProperty("有效期")
  private Date expiration;

  @ApiModelProperty("版本号")
  private Integer versions;

  @ApiModelProperty("'员工数量'")
  private Integer employeesNumber;

  @ApiModelProperty("空间大小MB")
  private Long spaceSize;

  private Long modelCountLimit;

  @ApiModelProperty("状态（10：申请中，20：驳回，30：已开通，40：重复申请，50：已过期）")
  private Integer status;

  @ApiModelProperty("1:用户申请，2：后台申请")
  private Integer type;

  private Boolean aiEnabled;

  @ApiModelProperty("是否删除（0：否，1：是）")
  private Integer deleted;

  private Date createTime;

  @ApiModelProperty("空间大小GB")
  private Long fileSpaceSize;
}
