package com.sp.proxverse.common.action.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-05-09 1:58 下午
 */
@Data
@ApiModel
public class ActionDoResultBo {

  private Integer actionCode;

  /** 节点结果描述 */
  private String resultsDesc;

  @ApiModelProperty("本次动作，系统请求是否成功")
  private Boolean requestStart;

  @ApiModelProperty("当前节点是否被过滤掉")
  private Boolean filterFlag;

  /** 错误信息 */
  private String errorMessage;

  public ActionDoResultBo() {
    this.requestStart = true;
    this.filterFlag = false;
  }
}
