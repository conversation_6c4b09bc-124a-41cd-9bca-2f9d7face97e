package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Data
@ApiModel("查询业务知识模型下数据详情请求对象")
@Builder
public class QueryKnowledgeDataDetailRequest extends PageRequest {

  @ApiModelProperty(value = "fileId，在业务知识模型中查数据详情")
  @NotNull(message = "{400006}")
  private Integer fileId;

  @ApiModelProperty(value = "主题ID，在业务知识模型中查数据列表1.2新增请求字段")
  @NotNull(message = "{400006}")
  private Integer topicId;
}
