package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("相关系数请求对象")
public class CalculateRootCauseRequest {

  @ApiModelProperty(value = "相关变量集合", required = true)
  private List<VariableSaveSubRequest> variableList;

  @ApiModelProperty(value = "业务主题表格ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private Integer topicId;

  @ApiModelProperty(value = "事件相关性")
  private String event;

  @ApiModelProperty(value = "路径相关性")
  private String variant;

  @ApiModelProperty(value = "连线相关性")
  private String line;

  @ApiModelProperty(value = "返工相关性")
  private String rework;

  @ApiModelProperty(value = "自定义相关性表达式")
  private String customerExpression;

  private String startTime;

  private String endTime;
}
