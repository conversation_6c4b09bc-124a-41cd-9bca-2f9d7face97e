package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-21 14:46
 */
@Data
@ApiModel("仿真事件信息响应")
public class SimulationEventResVo {

  @ApiModelProperty("事件名称")
  private String eventName;

  @ApiModelProperty("事件Id")
  private Integer eventId;

  @ApiModelProperty("事件元素Id")
  private String eventElementId;

  @ApiModelProperty("事件类型 -2：结束 -1：开始， 0:事件 ，1：专属网关（开始），2：并行网关（开始）， 11：专属网关（结束），12：并行网关（结束）")
  private Integer eventType;
}
