package com.sp.proxverse.common.model.dict;

import java.util.Objects;

public enum UserLevelEnum {
  SUPER,
  ROOT,
  ANALYST,
  USER,
  ;

  public static boolean isSuper(String level) {
    return Objects.equals(SUPER.name(), level);
  }

  public static boolean isRoot(String level) {
    return Objects.equals(ROOT.name(), level) || Objects.equals(SUPER.name(), level);
  }

  public static boolean isUser(String level) {
    return false;
  }

  public static boolean isAnalyst(String level) {
    return !isRoot(level);
  }
}
