package com.sp.proxverse.common.config;

import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/** 行内不要同步这块代码 */
@Configuration
public class AkmApplicationResource {
  @Value("${akm.service.type:remote}")
  private String serviceType;

  @Value("${akm.env.name:UAT}")
  private String envName;

  @Value("${akm.remote.address.list:jcld.sdc.bocomm.com:8093}")
  private String remoteAddressList;

  @Value("${akm.app.token:BMK0000010881-3e2fd01cc36747969782bf27975da85b}")
  private String appToken;

  private static String serviceTypeValue;
  private static String envNameValue;
  private static String remoteAddressListValue;
  private static String appTokenValue;

  @PostConstruct
  public void init() {
    serviceTypeValue = serviceType;
    envNameValue = envName;
    remoteAddressListValue = remoteAddressList;
    appTokenValue = appToken;
  }

  public static String getEnvNameValue() {
    return envNameValue;
  }

  public static String getRemoteAddressListValue() {
    return remoteAddressListValue;
  }

  public static String getAppTokenValue() {
    return appTokenValue;
  }

  public static String getServiceTypeValue() {
    return serviceTypeValue;
  }
}
