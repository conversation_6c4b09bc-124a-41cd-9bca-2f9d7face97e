package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
public class ActionParamDTO implements Serializable {

  @Tolerate
  public ActionParamDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "topicId")
  private Integer topicId;

  private String username;

  @ApiModelProperty(value = "参数map")
  private Map<String, String> map;
}
