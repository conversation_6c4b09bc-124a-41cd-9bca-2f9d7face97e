package com.sp.proxverse.common.model.vo.conformance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2022-08-10 9:51 上午
 */
@Data
@ApiModel("更新一个sheet的BPMN")
@AllArgsConstructor
@NoArgsConstructor
public class UpdateSheetBpmnReqVo {

  @ApiModelProperty("xml文件内容")
  private String fileCode;

  @ApiModelProperty("sheetId")
  private Integer sheetId;
}
