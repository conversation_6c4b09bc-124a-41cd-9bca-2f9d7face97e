package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("添加外键请求子对象")
public class AddForeignKeySubRequest {

  @ApiModelProperty(value = "文件ID（连接线的一端）", required = true)
  @NotNull(message = "{400006}")
  private Integer fileIdLeft;

  @ApiModelProperty(value = "字段ID（连接线的一端）", required = true)
  @NotNull(message = "{400006}")
  private Integer fieldIdLeft;

  @ApiModelProperty(value = "文件ID（连接线的另一端）", required = true)
  @NotNull(message = "{400006}")
  private Integer fileIdRight;

  @ApiModelProperty(value = "字段ID（连接线的另一端）", required = true)
  @NotNull(message = "{400006}")
  private Integer fieldIdRight;
}
