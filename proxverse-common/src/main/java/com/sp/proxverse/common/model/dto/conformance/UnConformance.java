package com.sp.proxverse.common.model.dto.conformance;

import com.sp.proxverse.common.model.dto.ConformanceKipDTO;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;
import org.codehaus.jackson.annotate.JsonIgnore;

@Getter
@Setter
@Builder
public class UnConformance {

  @Tolerate
  public UnConformance() {
    // comment empty
  }

  private String event;

  private String result;

  private String resultOrigin;

  private String variant;

  private Integer reasonType;

  private List<String> variantList;

  private List<Integer> variantIDList;

  private Long caseNum;

  private BigDecimal caseRate;

  @JsonIgnore private String filter;

  public String getFilter() {
    if (filter == null) {
      if (variantIDList == null || variantList.isEmpty()) {
        filter = "true";
      } else {
        String resultLocal =
            String.join(",", variantIDList.stream().map(String::valueOf).toArray(String[]::new));
        filter = "`case_table`.variantId in ( " + resultLocal + ")";
      }
    }
    return filter;
  }

  @ApiModelProperty("kpi列表信息")
  private List<ConformanceKipDTO> kipDTOList;
}
