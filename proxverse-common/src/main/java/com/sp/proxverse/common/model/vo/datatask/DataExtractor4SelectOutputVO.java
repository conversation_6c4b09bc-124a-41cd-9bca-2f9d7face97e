package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据提取列表（select框）VO对象")
public class DataExtractor4SelectOutputVO {
  @Tolerate
  public DataExtractor4SelectOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据提取ID")
  private Integer dataExtractorId;

  @ApiModelProperty(value = "数据名称")
  private String name;

  @ApiModelProperty(value = "数据提取的状态（status=1（已使用过不可选择）status=0（正常可选择））")
  private Integer status;
}
