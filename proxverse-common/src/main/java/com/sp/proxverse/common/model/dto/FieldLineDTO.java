package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("字段连线DTO")
public class FieldLineDTO {

  @Tolerate
  public FieldLineDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "表ID")
  private Integer fileId;

  @ApiModelProperty(value = "列ID")
  private Integer fieldId;

  @ApiModelProperty(value = "列名称")
  private String field;

  @ApiModelProperty(value = "列名称别名")
  private String fieldAlias;

  /** @see DataTypeEnum */
  @ApiModelProperty(value = "列字段类型")
  private Integer fieldType;

  @ApiModelProperty(value = "连线的字段列表")
  private List<FieldLineDTO> lineList;
}
