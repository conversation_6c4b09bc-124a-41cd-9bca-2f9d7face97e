package com.sp.proxverse.common.model.dto.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import scala.Serializable;

@Data
@Builder
@TableName("t_user_group_detail")
public class UserGroupDetail implements Serializable {

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  private Integer userGroupId;

  private Integer userId;

  private Integer tenantId;

  private Integer createUser;

  private Integer deleted;
}
