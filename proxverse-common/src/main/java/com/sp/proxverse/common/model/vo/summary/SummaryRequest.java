package com.sp.proxverse.common.model.vo.summary;

import com.sp.proxverse.common.util.PromptUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class SummaryRequest {

  // @ApiModelProperty(value = "Model ID", required = true)
  // @NotNull(message = "Model ID cannot be null")
  // private Integer modelId;

  private Integer topicId;
  private Integer topicSheetId;
  private Integer componentId;

  private String sheetPrompt;

  private enum Type {
    single_component,
    global
  }

  @ApiModelProperty(value = "Report type (e.g., 'global')", required = true)
  @NotNull(message = "Type cannot be null")
  private Type type;

  @ApiModelProperty(value = "Global prompt for the report")
  private String prompt;

  @ApiModelProperty(value = "List of report components", required = true)
  @NotEmpty(message = "Components cannot be empty")
  private List<Component> components;

  public String getComponentPrompt(Map<String, String> componentPrompt) {
    StringBuilder stringBuilder = new StringBuilder();
    for (Component component : components) {

      if (component.getType().equals("variant-com")) {
        continue;
      }
      stringBuilder.append(
          String.format("------ 分析主题：%s，类型和完整数据如下 ------ %n ", component.getTitle()));
      switch (component.getType()) {
        case "cake-com":
          stringBuilder.append("下面是二维饼图完整json数据 \n");
          break;
        case "table-com":
          stringBuilder.append("下面是OLAP表的完整json数据 \n");
          break;
        case "line-com":
          stringBuilder.append("下面是折线图的完整json数据 \n");
          break;
        case "column-com":
          stringBuilder.append("下面是二维柱状图完整json数据 \n");
          break;
        case "throughput-com":
          stringBuilder.append("流程吞吐时间的完整json数据 \n");
          break;
        case "process-com":
          stringBuilder.append("下面是流程路径的完整json数据 \n");
          break;
        case "variant-chart":
          stringBuilder.append("下面是不同流程变体和流程路径的完整json数据 \n");
          break;
        case "number-new-com":
          stringBuilder.append("下面是KPI的完整json数据 \n");
          break;
      }
      if (component.getType().equals("process-com")) {
        String data = PromptUtils.processAndSimplifyJson(component.getData());
        log.info(
            "component: {}  type: {}  consumed {} token",
            component.getTitle(),
            component.getType(),
            data.length());
        stringBuilder.append(data).append("\n");
        continue;
      }

      log.info(
          "component: {}  type: {}  consumed {} token",
          component.getTitle(),
          component.getType(),
          component.getData().length());
      stringBuilder.append(component.getData()).append("\n");
    }
    return stringBuilder.toString();
  }

  @Data
  @ApiModel("Report Component")
  public static class Component {
    @ApiModelProperty(value = "Component ID", required = true)
    @NotNull(message = "Component ID cannot be null")
    private Integer componentId;

    @ApiModelProperty(value = "Component type")
    private String type;

    private String data;

    @ApiModelProperty(value = "Component title")
    private String title;
  }
}
