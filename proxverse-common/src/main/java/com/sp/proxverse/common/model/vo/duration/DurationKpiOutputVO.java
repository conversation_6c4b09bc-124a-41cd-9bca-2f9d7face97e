package com.sp.proxverse.common.model.vo.duration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询事件吞吐kpi VO对象")
public class DurationKpiOutputVO {
  @Tolerate
  public DurationKpiOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "当前流程kpi值")
  private String current;

  @ApiModelProperty(value = "单位")
  private String unit;

  @ApiModelProperty(value = "一致流程kpi值")
  private String currentAll;

  @ApiModelProperty(value = "kpi ID,删除用这个字段")
  private Integer kpiId;

  @ApiModelProperty(value = "kpi名称")
  private String name;

  private String originalName;
  private String originalUnit;

  @ApiModelProperty("基线值")
  private String baseLine;

  @ApiModelProperty("公式")
  private String expression;

  @ApiModelProperty("1:越大越优，2：越小越优")
  private Integer kpiType;

  @ApiModelProperty("创建kpi的方式（1：从知识模型中导入，2：自定义）")
  private Integer saveType;

  @ApiModelProperty("0：正常创建，可以删除，  1：初始创建，不能删除")
  private Integer initType;

  @ApiModelProperty(value = "当前业务分析图谱这个主题表格的ID")
  private Integer topicSheetId;

  private String formatting;

  private String format;

  private String columnType;
}
