package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("保存过滤条件请求对象")
public class SaveTopicFilterPreRequest {

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty(value = "过滤项类型（100：流程通过，200：流程不通过，300：流程开始于，400：流程结束于）", required = true)
  @NotNull(message = "{400007}")
  private Integer type;

  @ApiModelProperty(value = "变量值", required = true)
  @NotNull(message = "{400009}")
  private String param;
}
