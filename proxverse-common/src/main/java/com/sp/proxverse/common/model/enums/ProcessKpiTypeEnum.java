package com.sp.proxverse.common.model.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/30 13:50
 */
public enum ProcessKpiTypeEnum {
  EVENT(0),
  LINE(1),
  PATH(2);
  private Integer code;

  ProcessKpiTypeEnum(Integer code) {
    this.code = code;
  }

  public Integer getCode() {
    return this.code;
  }

  public static ProcessKpiTypeEnum getProcessKpiTypeEnumByCode(Integer code) {
    if (code == null) {
      return null;
    }
    for (ProcessKpiTypeEnum value : ProcessKpiTypeEnum.values()) {
      if (Objects.equals(value.code, code)) {
        return value;
      }
    }
    return null;
  }
}
