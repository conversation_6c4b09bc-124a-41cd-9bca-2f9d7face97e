package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class DataModelDTO implements Serializable {

  @Tolerate
  public DataModelDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "主键ID")
  private Integer id;

  @ApiModelProperty(value = "数据源ID（dataModel或者业务知识模型）")
  private Integer dataId;

  @ApiModelProperty(value = "主题ID")
  private Integer topicId;

  @ApiModelProperty(value = "数据源名称")
  private String name;

  @ApiModelProperty(value = "数据池ID")
  private Integer poolId;

  @ApiModelProperty(value = "数据池名称")
  private String poolName;

  private Date createTime;

  @ApiModelProperty(value = "状态")
  private Integer status;
}
