package com.sp.proxverse.common.model.vo.processai.request;

import com.sp.proxverse.common.model.TopicMultiProcessDTO;
import com.sp.proxverse.common.model.po.ComponentWorkTimePO;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询事件列表时案例数量与占比（变量过滤）请求对象")
public class QueryEventRate4VariableRequest {
  @Tolerate
  public QueryEventRate4VariableRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  private Integer sheetId;

  private Integer componentId;

  private Integer hasInvert;

  private List<TopicFilterPO> addFilterList;

  @ApiModelProperty(value = "过滤项类型（700:变量过滤(v1.1)，800：流程过滤，900：吞吐时间过滤，1000：返工过滤）", required = true)
  private Integer type;

  @ApiModelProperty(value = "表ID(事件过滤此字段不填，type=700时此字段必填)")
  private Integer fileId;

  @ApiModelProperty(value = "变量ID(事件过滤此字段不填，type=700时此字段必填)")
  private Integer fieldId;

  @ApiModelProperty(
      value = "运算符号类型（10：>=，11：>，20：<=，21：<，30：=，41：time的范围）(事件过滤此字段不填)，type=700时此字段可填可不填")
  private Integer operationType;

  @ApiModelProperty(
      value = "变量值列表（operationType=10、11、20、21、30时，此列表只需传一个值，=40时，也传一个值比如：2021-10-10&2021-10-12）",
      required = true)
  private List<String> paramValue;

  @ApiModelProperty(value = "事件1")
  private String event1;

  @ApiModelProperty(value = "事件1的类型,(type=900时，10：第一次发生，20：最后一次发生)")
  private Integer type1;

  @ApiModelProperty(value = "事件2")
  private String event2;

  @ApiModelProperty(value = "事件2的类型,type=900时，10：第一次发生，20：最后一次发生")
  private Integer type2;

  @ApiModelProperty(value = "当type=1000（返工过滤）、900（吞吐时间过滤）时，此为最小值")
  private Integer num1;

  @ApiModelProperty(value = "当type=1000（返工过滤）、900（吞吐时间过滤）时，此为最大值")
  private Integer num2;

  @ApiModelProperty(value = "当type=900（吞吐时间过滤）时，此为时间单位1：天，2：时，3：秒")
  private Integer unit;

  private String expression;

  private Integer prikey;

  private List<ComponentWorkTimePO> workTimeList;

  private String extension;

  // START_TIMESTAMP_COLUMN() or TIMESTAMP_COLUMN();
  private String startTime;

  private String endTime;

  private List<SaveTopicFilterRequest> filterPqlLsit;

  // 操作：2-页面查询、3-功能查询、不传默认事件查询
  private Integer eventIndex;
  // 获取流程图设置数据列表
  private List<TopicMultiProcessDTO> topicMultiProcessList;
}
