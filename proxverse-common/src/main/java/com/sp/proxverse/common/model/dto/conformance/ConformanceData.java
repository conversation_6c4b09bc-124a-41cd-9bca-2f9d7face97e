package com.sp.proxverse.common.model.dto.conformance;

import com.sp.proxverse.common.model.vo.conformance.ConformanceViewDataOutputVO;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Builder
public class ConformanceData {

  private List<UnConformance> unConformanceList;

  private List<ConformanceKpi> kpiList;

  private ConformanceViewDataOutputVO viewData;

  private ConformanceGeneralData generalData;
}
