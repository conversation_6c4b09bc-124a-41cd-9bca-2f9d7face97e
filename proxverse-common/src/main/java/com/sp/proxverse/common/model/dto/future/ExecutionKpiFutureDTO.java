package com.sp.proxverse.common.model.dto.future;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class ExecutionKpiFutureDTO {

  @Tolerate
  public ExecutionKpiFutureDTO() {
    // comment empty
  }

  private Integer kpiId;

  private CompletableFuture<BigDecimal> future;
}
