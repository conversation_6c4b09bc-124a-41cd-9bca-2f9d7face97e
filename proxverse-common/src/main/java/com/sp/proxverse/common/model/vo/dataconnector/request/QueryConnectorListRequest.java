package com.sp.proxverse.common.model.vo.dataconnector.request;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询链接列表请求对象")
public class QueryConnectorListRequest extends PageRequest {

  @ApiModelProperty(value = "数据池ID", required = true)
  @NotNull(message = "{400006}")
  private Integer poolId;
}
