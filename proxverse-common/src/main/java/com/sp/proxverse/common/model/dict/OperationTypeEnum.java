package com.sp.proxverse.common.model.dict;

import java.util.Objects;
import java.util.stream.Stream;

public enum OperationTypeEnum {
  GTEQ(10, ">="),
  GT(11, ">"),
  LTEQ(20, "<="),
  LT(21, "<"),
  <PERSON><PERSON>(30, "="),
  STR_EQ(31, "="),
  NOT_EQ(32, "!="),
  TIME(41, "时间范围"),
  TIME_MARK(40, "时间勾选"),
  LIKE(50, "ILIKE"),
  NOT_LIKE(51, "NOT ILIKE"),
  ;

  private final Integer value;
  private final String name;

  OperationTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static OperationTypeEnum fromIntValue(Integer intValue) {
    for (OperationTypeEnum opType : OperationTypeEnum.values()) {
      if (Objects.equals(opType.getValue(), intValue)) {
        return opType;
      }
    }
    return LIKE;
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    OperationTypeEnum[] values = OperationTypeEnum.values();
    for (OperationTypeEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value.getName();
      }
    }
    return "";
  }

  /**
   * 根据文件后缀名得到枚举int值
   *
   * @param f
   * @return
   */
  public static Integer filterFileType(String f) {
    OperationTypeEnum fileTypeEnum =
        Stream.of(values()).filter(e -> Objects.equals(e.getName(), f)).findFirst().orElse(null);
    return Objects.isNull(fileTypeEnum) ? null : fileTypeEnum.getValue();
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  public static boolean isScope(Integer type) {
    if (Objects.equals(type, GTEQ.getValue())
        || Objects.equals(type, GT.getValue())
        || Objects.equals(type, LTEQ.getValue())
        || Objects.equals(type, LT.getValue())
        || Objects.equals(type, EQ.getValue())
        || Objects.equals(type, TIME.getValue())) {
      return true;
    }
    return false;
  }

  public static boolean isTime(Integer type) {
    if (Objects.equals(type, TIME.getValue())) {
      return true;
    }
    return false;
  }

  public boolean isLike() {
    return this == LIKE || this == NOT_LIKE;
  }

  public static boolean isLike(Integer type) {
    if (Objects.equals(type, NOT_LIKE.getValue()) || Objects.equals(type, LIKE.getValue())) {
      return true;
    }
    return false;
  }
}
