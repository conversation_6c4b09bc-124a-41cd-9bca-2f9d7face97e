package com.sp.proxverse.common.model.job.create;

import com.sp.proxverse.common.model.po.DataExtractorPO;
import com.sp.proxverse.common.model.po.DataTaskChildPo;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-10 14:03
 */
@Data
public class KafkaTaskGeneratorInfo extends TaskGeneratorInfo {

  Integer poolId;

  protected Integer dataExtractId;

  DataExtractorPO dataExtractorPO;

  DataTaskChildPo dataTaskChildPo;
}
