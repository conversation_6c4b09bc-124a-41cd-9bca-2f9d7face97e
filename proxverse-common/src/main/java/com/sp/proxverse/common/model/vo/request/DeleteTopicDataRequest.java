package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("删除数据请求对象")
public class DeleteTopicDataRequest {

  @ApiModelProperty(
      value = "当前主题数据模型所属数据item的ID，即getTopicDataModelList接口中返回的dataId字段",
      required = true)
  @NotNull(message = "{400006}")
  private Integer dataId;
}
