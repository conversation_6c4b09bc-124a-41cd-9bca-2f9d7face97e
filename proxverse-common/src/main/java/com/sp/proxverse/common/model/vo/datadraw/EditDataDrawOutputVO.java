package com.sp.proxverse.common.model.vo.datadraw;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据提取回显示VO对象")
public class EditDataDrawOutputVO {
  @Tolerate
  public EditDataDrawOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据链接ID")
  private Integer dataExtractorId;

  @ApiModelProperty(value = "名称")
  private String name;
}
