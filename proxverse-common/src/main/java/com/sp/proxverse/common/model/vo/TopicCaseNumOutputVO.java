package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("数据模型案例数VO对象")
public class TopicCaseNumOutputVO {
  @Tolerate
  public TopicCaseNumOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "当前案例")
  private Integer currentNum;

  @ApiModelProperty(value = "总的案例数")
  private Integer totalNum;
}
