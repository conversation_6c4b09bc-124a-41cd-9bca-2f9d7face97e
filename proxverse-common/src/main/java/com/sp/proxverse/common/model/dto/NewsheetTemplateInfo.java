package com.sp.proxverse.common.model.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class NewsheetTemplateInfo {

  @Tolerate
  public NewsheetTemplateInfo() {
    // comment empty
  }

  private String newsheetInfo;

  private List<NewsheetTemplateParameterInfo> templateParameters;

  private List<ComponentFilter> componentFilterList;

  private List<TempPQLTableDto> tempPQLTableDtoList;

  public List<NewsheetTemplateParameterInfo> getTemplateParameters() {
    return templateParameters == null ? new ArrayList<>() : templateParameters;
  }

  public List<ComponentFilter> getComponentFilterList() {
    return componentFilterList == null ? new ArrayList<>() : componentFilterList;
  }
}
