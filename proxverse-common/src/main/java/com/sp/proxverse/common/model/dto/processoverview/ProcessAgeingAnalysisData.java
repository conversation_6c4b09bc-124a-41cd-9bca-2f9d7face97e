package com.sp.proxverse.common.model.dto.processoverview;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/22 10:22
 */
@Setter
@Getter
public class ProcessAgeingAnalysisData {
  private String avgDuration;
  private String fastestCase;
  private String slowestCase;

  public ProcessAgeingAnalysisData() {}

  public ProcessAgeingAnalysisData(String avgDuration, String fastestCase, String slowestCase) {
    this.avgDuration = avgDuration;
    this.fastestCase = fastestCase;
    this.slowestCase = slowestCase;
  }
}
