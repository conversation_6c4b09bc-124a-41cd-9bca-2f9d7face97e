package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("变体输出结果对象DTO")
public class VariantResultDTO {

  @Tolerate
  public VariantResultDTO() {
    // comment empty
  }

  @ApiModelProperty("相关变量值")
  private String varName;

  @ApiModelProperty("此相关变量数据所占百分比")
  private BigDecimal value;

  @ApiModelProperty("变体数量")
  private Long count;

  @ApiModelProperty("变体值")
  private String variant;

  private String pathKpiValue;

  private String variantMd5;

  @ApiModelProperty("变体值")
  private String eventList;

  @ApiModelProperty("事件数据")
  private Integer eventSize;

  @ApiModelProperty("持续时间")
  private BigDecimal caseDuration;

  private Integer skip;

  @ApiModelProperty("此相关变量在kpi中计算得出的值和单位")
  private List<Map<String, String>> valueList;
}
