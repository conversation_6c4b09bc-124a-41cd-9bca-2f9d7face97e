package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("偏差事件请求对象")
public class TopicDeviationRequest {

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty(value = "当前所在主题表格ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  @ApiModelProperty(value = "排序ID，即KPI的ID", required = true)
  private Integer sortKpiId;

  @ApiModelProperty(value = "是否包含常见事件，默认包含（0：不包含，1：包含）v1.1修改")
  private Integer hasMost;
}
