package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR> @create
 */
@Data
@ApiModel("参数与KPI")
public class TopicParamKpiListRes {
  private List<Map<String, String>> varInfos;
  private List<Map<String, String>> kpiInfos;

  public TopicParamKpiListRes() {
    this.varInfos = new ArrayList<>();
    this.kpiInfos = new ArrayList<>();
  }
}
