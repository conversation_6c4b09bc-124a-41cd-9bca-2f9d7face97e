package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("取消案例表请求对象")
public class RemoveTableAttributeRequest {

  @ApiModelProperty(value = "数据模型ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataModelId;

  @ApiModelProperty(value = "案例表的表ID", required = true)
  @NotNull(message = "{400006}")
  private Integer fileId;
}
