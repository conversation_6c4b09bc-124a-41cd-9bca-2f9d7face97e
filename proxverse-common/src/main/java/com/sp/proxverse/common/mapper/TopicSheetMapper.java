package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.dto.DataModelFileDTO;
import com.sp.proxverse.common.model.dto.SheetFileDTO;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TopicSheetMapper extends BaseMapper<TopicSheetPO> {

  DataModelFileDTO getTopicSheetFileBySheetId(Integer sheetId);

  List<SheetFileDTO> getTopicSheetFileBySheetIdList(@Param("ids") List<Integer> ids);
}
