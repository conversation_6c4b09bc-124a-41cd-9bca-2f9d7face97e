package com.sp.proxverse.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("多层流程图对象")
public class TopicMultiProcessDTO {

  @Tolerate
  public TopicMultiProcessDTO() {
    // comment empty
  }

  private Integer id;

  @ApiModelProperty("topicId")
  private Integer topicId;

  /** 流程标题名称 */
  private String processName;

  private String eventNameColumn;

  private String startTimeColumn;

  private String endTimeColumn;

  /** 事件列下标（如第一个事件列，则为 1） */
  private Integer eventIndex;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  // 是否开启多级流程图配置
  private Boolean enableMultiProcess;
}
