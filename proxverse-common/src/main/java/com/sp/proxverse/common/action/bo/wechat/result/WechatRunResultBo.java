package com.sp.proxverse.common.action.bo.wechat.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-06-09 3:32 下午
 */
@Data
public class WechatRunResultBo {

  @JSONField(name = "errcode")
  private Integer errCode;

  @JSONField(name = "errmsg")
  private String errMsg;

  /**
   * 获取是否运行成功
   *
   * @return
   */
  public Boolean getSuccessFlag() {
    if (this.errCode != null && errCode == 0) {
      return true;
    }
    return false;
  }
}
