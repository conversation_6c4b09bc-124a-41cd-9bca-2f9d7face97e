package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("supported func")
public class SupportedFunctionVO {

  @Tolerate
  public SupportedFunctionVO() {}

  public SupportedFunctionVO(String name, String syntax, String desc, String url) {
    this.name = name;
    this.syntax = syntax;
    this.desc = desc;
    this.url = url;
  }

  @ApiModelProperty(value = "name")
  private String name;

  @ApiModelProperty(value = "syntax")
  private String syntax;

  @ApiModelProperty(value = "desc")
  private String desc;

  @ApiModelProperty(value = "url")
  private String url;
}
