package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.dto.task.TaskLogCountDto;
import com.sp.proxverse.common.model.po.TaskLogPo;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 任务日志 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-01-05
 */
@Mapper
public interface TaskLogMapper extends BaseMapper<TaskLogPo> {

  List<TaskLogCountDto> getTaskLogCountByTaskId(
      @Param("taskIds") Collection<Integer> taskIds, @Param("status") Integer status);

  Long checkTaskStatus(@Param("taskIds") Collection<Integer> taskIds);
}
