package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class SheetFileDTO {

  @Tolerate
  public SheetFileDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "事件名称")
  private Integer fileId;

  @ApiModelProperty(value = "发生时间")
  private Integer sheetId;
}
