package com.sp.proxverse.common.model.dto.processoverview;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/22 10:11
 */
@Getter
@Setter
public class KpiWithAnalysis {
  private String analysis;
  private String caseRatio;
  private String caseCount;
  private String kpiName;
  private String kpiValue;
  private String up;
  private String down;
  private String filterExpression;

  public KpiWithAnalysis() {}

  public KpiWithAnalysis(
      String analysis,
      String caseRatio,
      String caseCount,
      String kpiName,
      String kpiValue,
      String up,
      String down,
      String filterExpression) {
    this.analysis = analysis;
    this.caseRatio = caseRatio;
    this.caseCount = caseCount;
    this.kpiName = kpiName;
    this.kpiValue = kpiValue;
    this.up = up;
    this.down = down;
    this.filterExpression = filterExpression;
  }
}
