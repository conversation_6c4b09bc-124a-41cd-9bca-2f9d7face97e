package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.dto.processoverview.DimensionAndKpi;
import com.sp.proxverse.common.model.po.AllowProcessPO;
import com.sp.proxverse.common.model.po.CloudFileInfoPO;
import com.sp.proxverse.common.model.po.ComponentConfigPO;
import com.sp.proxverse.common.model.po.SheetComponentPO;
import com.sp.proxverse.common.model.po.SheetParamPO;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.po.TopicSheetNewPo;
import com.sp.proxverse.common.model.po.TopicSheetVariablePO;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Setter
@Builder
public class TopicSheetExport {
  @Tolerate
  public TopicSheetExport() {
    // comment empty
  }

  private Integer sheetId;

  private String configMap;

  @ApiModelProperty("当前sheet页关联的kpi")
  private List<SheetKpiDTO> sheetKpiList;

  @ApiModelProperty("当前sheet页关联的大组件信息，从json_array里循环处理每一个组件")
  private TopicSheetNewPo newSheet;

  @ApiModelProperty("当前sheet页关联的允许")
  private List<AllowProcessPO> allowProcessList;

  @ApiModelProperty("sheet页的变量--KPI分析选变量计算相关性")
  private List<TopicSheetVariablePO> sheetVariableList;

  private List<DimensionAndKpi> dimensionAndKpiList;

  @ApiModelProperty("bpmn file_code")
  private CloudFileInfoPO cloudFileInfo;

  @ApiModelProperty("组件")
  private List<SheetComponentPO> sheetComponentList;

  @ApiModelProperty("组件配置项")
  private List<ComponentConfigPO> componentConfigList;

  @ApiModelProperty("sheet的参数设置--吞吐时间sheet页的设置项")
  private List<SheetParamPO> sheetParamList;

  @ApiModelProperty("组件过滤")
  private List<TopicFilterPO> componentFilterList;

  @ApiModelProperty("sheet级过滤")
  private List<TopicFilterPO> sheetFilterList;

  private Map<Integer, Integer> componentIdMap;

  public Integer getSheetId() {
    return sheetId;
  }

  public String getConfigMap() {
    return configMap;
  }

  public List<SheetKpiDTO> getSheetKpiList() {
    return sheetKpiList == null ? new ArrayList<>() : sheetKpiList;
  }

  public TopicSheetNewPo getNewSheet() {
    return newSheet;
  }

  public List<AllowProcessPO> getAllowProcessList() {
    return allowProcessList == null ? new ArrayList<>() : allowProcessList;
  }

  public List<TopicSheetVariablePO> getSheetVariableList() {
    return sheetVariableList == null ? new ArrayList<>() : sheetVariableList;
  }

  public List<DimensionAndKpi> getDimensionAndKpiList() {
    return dimensionAndKpiList == null ? new ArrayList<>() : dimensionAndKpiList;
  }

  public CloudFileInfoPO getCloudFileInfo() {
    return cloudFileInfo;
  }

  public List<SheetComponentPO> getSheetComponentList() {
    return sheetComponentList == null ? new ArrayList<>() : sheetComponentList;
  }

  public List<ComponentConfigPO> getComponentConfigList() {
    return componentConfigList == null ? new ArrayList<>() : componentConfigList;
  }

  public List<SheetParamPO> getSheetParamList() {
    return sheetParamList == null ? new ArrayList<>() : sheetParamList;
  }

  public List<TopicFilterPO> getComponentFilterList() {
    return componentFilterList == null ? new ArrayList<>() : componentFilterList;
  }

  public List<TopicFilterPO> getSheetFilterList() {
    return sheetFilterList == null ? new ArrayList<>() : sheetFilterList;
  }

  public Map<Integer, Integer> getComponentIdMap() {
    return componentIdMap == null ? new HashMap<>() : componentIdMap;
  }
}
