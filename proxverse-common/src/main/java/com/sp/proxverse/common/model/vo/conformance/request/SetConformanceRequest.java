package com.sp.proxverse.common.model.vo.conformance.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("设置为一致流程请求对象")
public class SetConformanceRequest {

  @ApiModelProperty(value = "表格sheetID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private Integer topicId;

  private String result;

  private Integer resultNumber;

  @ApiModelProperty(value = "变体", required = true)
  private String variant;

  private Integer reasonType;

  private String conditionName;
}
