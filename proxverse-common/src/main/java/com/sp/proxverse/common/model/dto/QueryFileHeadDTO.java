package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
@ApiModel("查询文件表头 DTO")
public class QueryFileHeadDTO implements Serializable {
  @Tolerate
  public QueryFileHeadDTO() {
    // comment empty
  }

  private String[] names;

  private List<Map<String, String>> list;
}
