package com.sp.proxverse.common.model.dict;

public enum RuleEnum {
  GTEQ(10, ">="),
  GT(11, ">"),
  LTEQ(20, "<="),
  LT(21, "<"),
  EQ(30, "="),
  NQ(31, "!="),
  TIM<PERSON>(41, "时间范围"),
  TIME_START(42, "时间开始"),
  TIME_END(43, "时间结束"),
  TIME_EQ(44, "时间等于");

  private final Integer value;
  private final String name;

  RuleEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
