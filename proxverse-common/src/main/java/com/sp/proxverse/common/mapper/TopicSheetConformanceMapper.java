package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.dto.SheetConformance.SheetConformanceDto;
import com.sp.proxverse.common.model.po.TopicSheetConformancePO;
import java.util.List;

public interface TopicSheetConformanceMapper extends BaseMapper<TopicSheetConformancePO> {

  List<SheetConformanceDto> getSheetConformanceByTopicSheetId(Integer topicSheetId);
}
