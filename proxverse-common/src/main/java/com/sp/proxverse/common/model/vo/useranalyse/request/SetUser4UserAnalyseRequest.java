package com.sp.proxverse.common.model.vo.useranalyse.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("设置员工列请求对象")
public class SetUser4UserAnalyseRequest {

  @ApiModelProperty(value = "表格sheetID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  @ApiModelProperty(value = "选择的员工列ID", required = true)
  @NotNull(message = "{400006}")
  private Integer userFieldId;
}
