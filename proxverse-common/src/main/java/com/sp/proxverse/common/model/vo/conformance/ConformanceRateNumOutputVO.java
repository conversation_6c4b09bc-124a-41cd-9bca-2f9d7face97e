package com.sp.proxverse.common.model.vo.conformance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询流程比例、流程案例数VO对象")
public class ConformanceRateNumOutputVO {
  @Tolerate
  public ConformanceRateNumOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "流程比例")
  private BigDecimal rate;

  @ApiModelProperty(value = "流程案例数")
  private String caseNum;
}
