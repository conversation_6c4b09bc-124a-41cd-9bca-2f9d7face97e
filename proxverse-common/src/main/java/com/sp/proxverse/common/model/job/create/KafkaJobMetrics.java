package com.sp.proxverse.common.model.job.create;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-02-02 17:05
 */
@Data
public class KafkaJobMetrics extends JobMetrics {

  public Long updateRows;

  private String maxTime;

  private String updateFieldName;

  private Long parquetFileSize;

  private String tableName;

  private String updateTableName;

  private String fullTableName;

  private String sourceFullTableName;

  private String dbName;

  public String getMaxTime() {
    return maxTime;
  }

  public void setMaxTime(String maxTime) {
    this.maxTime = maxTime;
  }

  public KafkaJobMetrics() {
    super();
    this.updateRows = 0L;
  }
}
