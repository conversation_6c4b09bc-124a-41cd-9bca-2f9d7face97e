package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-21 13:29
 */
@Data
@ApiModel("更新网关概率请求")
public class UpdateGatewayFrequencyReqVo {

  @ApiModelProperty("方案Id")
  @NotNull(message = "{400006}")
  private Integer programmeId;

  @ApiModelProperty("网关Id")
  @NotNull(message = "{400006}")
  private Integer gatewayId;

  @ApiModelProperty("网关Id")
  @NotNull(message = "{400007}")
  private String eventName;

  @ApiModelProperty("事件元素Id")
  @NotNull(message = "{400006}")
  private String eventElementId;

  @ApiModelProperty("概率")
  @NotNull(message = "{400009}")
  private Double frequency;

  private String expression;

  private String formatting;

  private String format;

  public GatewayInfo getGatewayInfo() {
    GatewayInfo gatewayInfo = new GatewayInfo();
    gatewayInfo.setFormatting(formatting);
    gatewayInfo.setExpression(expression);
    gatewayInfo.setFormat(format);
    gatewayInfo.setValue(frequency);
    return gatewayInfo;
  }
}
