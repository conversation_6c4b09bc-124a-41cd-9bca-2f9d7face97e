package com.sp.proxverse.common.model.dto.process;

import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.vo.processExplorer.EventNode;
import com.sp.proxverse.common.model.vo.processExplorer.LineNode;
import io.swagger.annotations.ApiModel;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/** <AUTHOR> */
@Data
@ApiModel("流程图图响应")
public class ProcessTreeChartOut {

  private List<EventNode> eventNodes = new ArrayList<>();

  private List<LineNode> lineNodes = new ArrayList<>();

  private Integer maxLines;

  private Integer maxEvents;

  private List<KpiPO> eventKpis;

  private List<KpiPO> lineKpis;

  private Boolean limitEnabled = false;

  private String errorMsg;
}
