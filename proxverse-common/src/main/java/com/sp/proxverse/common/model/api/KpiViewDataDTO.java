package com.sp.proxverse.common.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询kpi的二维图数据VO对象")
public class KpiViewDataDTO {
  @Tolerate
  public KpiViewDataDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "纵坐标值")
  private List<KpiVerticalDTO> list;

  @ApiModelProperty(value = "横坐标值")
  private List<String> xdata;

  @ApiModelProperty(value = "kpi计算值")
  private String value;

  @ApiModelProperty(value = "kpi平均值")
  private BigDecimal avgValue;

  @ApiModelProperty(value = "kpi目标值")
  private String target;

  @ApiModelProperty(value = "更新时间")
  private String updateTime;

  @ApiModelProperty(value = "当前时间")
  private String currentTime;

  @ApiModelProperty(value = "kpi单位")
  private String unit;
}
