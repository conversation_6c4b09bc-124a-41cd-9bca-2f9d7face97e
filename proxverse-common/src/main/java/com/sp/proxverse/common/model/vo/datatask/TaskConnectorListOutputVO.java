package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据任务链接列表VO对象")
public class TaskConnectorListOutputVO {
  @Tolerate
  public TaskConnectorListOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据链接ID")
  private Integer dataConnectorId;

  @ApiModelProperty(value = "链接器名称")
  private String dataConnectorName;

  @ApiModelProperty(value = "任务列表")
  private List<TaskConnectorSubTaskOutputVO> taskList;
}
