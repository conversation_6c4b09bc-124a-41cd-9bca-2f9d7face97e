package com.sp.proxverse.common.model.dict;

import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

public class FilterConst {

  public static String process(String event1, String event2, Integer type, String caseEvent) {
    return "process_filter('" + event1 + "','" + event2 + "'," + type + ", " + caseEvent + " )";
  }

  public static String calcTime(
      String event1,
      Integer type1,
      String event2,
      Integer type2,
      Integer num1,
      Integer num2,
      Integer unit,
      String caseEvent,
      String ts) {
    return "calc_time_filter('"
        + event1
        + "',"
        + type1
        + ",'"
        + event2
        + "',"
        + type2
        + ","
        + num1
        + ","
        + num2
        + ","
        + unit
        + ", "
        + caseEvent
        + ", "
        + ts
        + " )";
  }

  public static String rework(String event, Integer num1, Integer num2, String caseEvent) {
    return "rework_num('" + event + "'," + num1 + "," + num2 + ", " + caseEvent + " )";
  }

  public static String event(List<String> events, Integer type, String caseEvent) {
    List<String> collect = events.stream().map(m -> "'" + m + "'").collect(Collectors.toList());
    return "event_filter(array("
        + StringUtils.join(collect, ",")
        + "),"
        + type
        + ", "
        + caseEvent
        + " )";
  }

  /**
   * 参数是一个String 中间用&分隔
   *
   * @return
   */
  public static String timeFiltering(String filter, String column) {
    String[] split = filter.trim().split("&");
    String first = null;
    String second = null;
    // 为字段名添加反引号包围，支持中文字段名
    String quotedColumn = "`" + column + "`";
    if (split.length > 0) {
      if (StringUtils.isNotBlank(split[0])) {
        first = quotedColumn + " >= '" + split[0] + "'";
      }
    }
    if (split.length == 2) {
      if (StringUtils.isNotBlank(split[1])) {
        second = quotedColumn + " <= '" + split[1] + "'";
      }
    }
    if (first == null && second == null) {
      return "true";
    }
    if (first == null) {
      return second;
    }
    if (second == null) {
      return first;
    }
    return first + " and " + second;
  }
}
