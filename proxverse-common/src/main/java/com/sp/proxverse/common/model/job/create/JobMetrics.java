package com.sp.proxverse.common.model.job.create;

import com.sp.proxverse.common.model.job.JobRunStatusEnum;
import com.sp.proxverse.common.model.po.DataModelPO;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-02-02 13:46
 */
@Data
public class JobMetrics implements Serializable {

  private LocalDateTime startTime;

  private String errorMessage;

  private Map<JobRunStatusEnum, LocalDateTime> runStatus;

  private Boolean immediatelyFlag;

  private Integer currentTaskExecutorId;

  private Integer parentTaskExecutorId;

  private List<DataModelPO> dataModelPOS;

  private List<String> transformationNames;

  public JobMetrics() {
    this.startTime = LocalDateTime.now();
    runStatus = new LinkedHashMap<>();
  }

  public void updateRunStatus(JobRunStatusEnum runStatusEnum) {
    runStatus.put(runStatusEnum, LocalDateTime.now());
  }

  public void error(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
