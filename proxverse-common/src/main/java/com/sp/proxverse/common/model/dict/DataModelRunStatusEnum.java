package com.sp.proxverse.common.model.dict;

import java.util.Objects;
import java.util.stream.Stream;

public enum DataModelRunStatusEnum {
  UNLOAD(0, "未加载"),
  LOADING(1, "加载中"),
  LOADED(2, "加载成功"),
  LOAD_FAILED(3, "加载失败"),
  WARNING(4, "警告"),
  ;

  private final Integer value;
  private final String name;

  DataModelRunStatusEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static DataModelRunStatusEnum getStatusByName(String name) {
    if (Objects.isNull(name)) {
      return DataModelRunStatusEnum.LOADED;
    }
    DataModelRunStatusEnum[] values = DataModelRunStatusEnum.values();
    for (DataModelRunStatusEnum value : values) {
      if (Objects.equals(value.name(), name)) {
        return value;
      }
    }
    return DataModelRunStatusEnum.LOADED;
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    DataModelRunStatusEnum[] values = DataModelRunStatusEnum.values();
    for (DataModelRunStatusEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value.getName();
      }
    }
    return "";
  }

  /**
   * 根据文件后缀名得到枚举int值
   *
   * @param f
   * @return
   */
  public static Integer filterFileType(String f) {
    DataModelRunStatusEnum fileTypeEnum =
        Stream.of(values()).filter(e -> Objects.equals(e.getName(), f)).findFirst().orElse(null);
    return Objects.isNull(fileTypeEnum) ? null : fileTypeEnum.getValue();
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
