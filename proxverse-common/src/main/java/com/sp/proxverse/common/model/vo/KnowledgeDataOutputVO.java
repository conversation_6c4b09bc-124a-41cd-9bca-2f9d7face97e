package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("业务知识模型数据列表VO对象")
public class KnowledgeDataOutputVO {
  @Tolerate
  public KnowledgeDataOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "文件ID，查看详情时要传此字段")
  private Integer fileId;

  @ApiModelProperty(value = "变量数")
  private Integer variableNum;

  @ApiModelProperty(value = "名称")
  private String name;
}
