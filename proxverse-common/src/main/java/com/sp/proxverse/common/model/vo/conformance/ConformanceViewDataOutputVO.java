package com.sp.proxverse.common.model.vo.conformance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询一致性流程的二维图数据VO对象")
public class ConformanceViewDataOutputVO {
  @Tolerate
  public ConformanceViewDataOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "一致比例，后台已经乘以100")
  private List<String> ydata;

  @ApiModelProperty(value = "一致案例")
  private List<String> xdata;
}
