package com.sp.proxverse.common.model.vo.dataconnector.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("删除数据链接请求对象")
public class DeleteConnectorRequest {

  @ApiModelProperty(value = "数据链接ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataConnectorId;
}
