package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-06-30 5:15 下午
 */
@Data
@ApiModel("重命名")
public class SaveTopicSheetConfigReqVo {

  @ApiModelProperty(value = "业务主题表格ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  @ApiModelProperty(value = "业务主题表格名称", required = true)
  @NotNull(message = "{400007}")
  private String topicSheetName;

  private String configMap;
}
