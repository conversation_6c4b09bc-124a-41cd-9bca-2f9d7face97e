package com.sp.proxverse.common.model.base;

import com.sp.proxverse.common.model.page.PageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.ArrayList;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("请求返回数据forDTO")
public class PageResp4DTO implements Serializable {

  @Tolerate
  public PageResp4DTO() {
    // comment empty
  }

  private static final long serialVersionUID = -5661206328302037980L;

  @ApiModelProperty("错误码")
  private Integer code;

  @ApiModelProperty("错误信息")
  private String msg;

  @ApiModelProperty("业务数据")
  private PageRespDTO data;

  public static PageResp4DTO success(PageRespDTO body) {
    PageResp4DTO rtn = new PageResp4DTO();
    rtn.setCode(2000);
    rtn.setData(body);
    return rtn;
  }

  public static PageResp4DTO success(Integer pageNum, Integer pageSize, PageRespDTO data) {
    PageResp4DTO rtn = new PageResp4DTO();
    rtn.setCode(2000);
    data.setPageNum(pageNum);
    data.setPageSize(pageSize);
    rtn.setData(data);
    return rtn;
  }

  public static PageResp4DTO success4none(Integer pageNum, Integer pageSize) {
    PageResp4DTO rtn = new PageResp4DTO();
    rtn.setCode(2000);
    rtn.setData(
        PageRespDTO.builder()
            .pageNum(pageNum)
            .pageSize(pageSize)
            .dto(new ArrayList<>())
            .total(0L)
            .build());
    return rtn;
  }
}
