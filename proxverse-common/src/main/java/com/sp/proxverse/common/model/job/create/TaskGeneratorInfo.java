package com.sp.proxverse.common.model.job.create;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-09 11:20
 */
@Data
public class TaskGeneratorInfo {

  protected Integer tenantId;

  protected Integer dataTaskId;

  protected Boolean immediatelyFlag;

  protected Integer taskExecutorId;

  /** 当为true的时候分析血缘 */
  protected Boolean analysisOfBlood;

  public Boolean getAnalysisOfBlood() {
    if (this.analysisOfBlood == null) {
      return false;
    }
    return true;
  }

  public Boolean getImmediatelyFlag() {
    if (immediatelyFlag == null) {
      return false;
    }
    return immediatelyFlag;
  }
}
