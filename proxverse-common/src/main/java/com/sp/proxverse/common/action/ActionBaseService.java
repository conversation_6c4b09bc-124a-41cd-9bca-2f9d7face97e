package com.sp.proxverse.common.action;

import com.sp.proxverse.common.action.bo.ActionBo;
import com.sp.proxverse.common.action.bo.ActionDoParamBo;
import com.sp.proxverse.common.action.enums.ActionTypeEnum;
import com.sp.proxverse.common.model.po.ActionPo;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-05-10 10:24 上午
 */
public interface ActionBaseService {

  /**
   * 获取actionBo
   *
   * @param actionPos
   * @return
   */
  ActionBo getActionBo(List<ActionPo> actionPos);

  /**
   * 执行动作
   *
   * @param actionBo
   * @return
   */
  Object runAction(ActionBo actionBo, Integer userId, Integer tenantId);

  /**
   * 解析参数对象
   *
   * @param actionTypeEnum
   * @param param
   * @return
   */
  ActionDoParamBo analysisActionParam(ActionTypeEnum actionTypeEnum, Object param);
}
