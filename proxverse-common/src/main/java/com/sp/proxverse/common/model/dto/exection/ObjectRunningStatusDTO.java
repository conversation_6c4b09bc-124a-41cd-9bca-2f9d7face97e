package com.sp.proxverse.common.model.dto.exection;

import com.sp.proxverse.common.model.dto.SignalConditionDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ObjectRunningStatusDTO {
  private Integer optimizationObjectId;

  @ApiModelProperty(value = "优化信号条件")
  List<SignalConditionDTO> signalConditions;

  String specialTable;
}
