package com.sp.proxverse.common.model.dto.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@TableName("t_user_group")
public class UserGroup {

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  private String name;

  private Integer tenantId;

  private Integer createUser;

  private Integer deleted;

  private String ssoId;
}
