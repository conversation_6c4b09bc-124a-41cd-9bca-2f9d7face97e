package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-22 14:57
 */
@Data
@ApiModel("设置仿真中事件的执行成本")
public class SettingSimulationRunCostReqVo {

  @ApiModelProperty("方案Id")
  @NotNull(message = "{400006}")
  private Integer programmeId;

  @ApiModelProperty("事件Id")
  @NotNull(message = "{400006}")
  private Integer eventId;

  @ApiModelProperty("执行成本")
  @NotNull(message = "{400009}")
  private Double runCost;

  @ApiModelProperty("表达式")
  private String expression;

  private String formatting;

  private String format;
}
