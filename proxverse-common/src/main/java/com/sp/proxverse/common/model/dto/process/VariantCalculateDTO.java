package com.sp.proxverse.common.model.dto.process;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@ApiModel("变体计算比例输出结果对象DTO")
@Slf4j
public class VariantCalculateDTO {

  @Tolerate
  public VariantCalculateDTO() {
    // comment empty
  }

  @ApiModelProperty("选中了几个变体")
  private Integer variantNum = 0;

  @ApiModelProperty("这些变体的数量")
  private Long variantCount = 0L;

  @ApiModelProperty("案例数量")
  private Long caseCount = 0L;

  @ApiModelProperty("案例比率（后台已乖以100）")
  private Double caseRate = 0.0D;

  public void setCaseCountStr(String variantCount) {
    if (StringUtils.isNotBlank(variantCount)) {
      try {
        this.caseCount = Long.valueOf(variantCount);
      } catch (NumberFormatException e) {
        log.error("setCaseCountStr error", e);
      }
    }
  }
}
