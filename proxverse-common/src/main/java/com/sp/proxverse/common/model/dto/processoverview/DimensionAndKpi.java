package com.sp.proxverse.common.model.dto.processoverview;

import com.sp.proxverse.common.model.dict.ProcessOverviewTitleTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/26 13:46
 */
@Getter
@Setter
public class DimensionAndKpi {

  private Integer sheetId;

  private Integer kpiId;

  /** @see ProcessOverviewTitleTypeEnum */
  @ApiModelProperty("title类型")
  private Integer titleType;

  private String tableName;

  private String columnName;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("kpi名称")
  private String name;

  @ApiModelProperty("kpi单位，1:天、2:时")
  private String unit;

  @ApiModelProperty("1:初始创建，不能删除，0：可以删除")
  private Integer type;

  @ApiModelProperty("公式")
  private String expression;
}
