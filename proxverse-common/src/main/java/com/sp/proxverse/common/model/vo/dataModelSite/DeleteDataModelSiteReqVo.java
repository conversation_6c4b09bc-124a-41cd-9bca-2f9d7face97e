package com.sp.proxverse.common.model.vo.dataModelSite;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-04-21 2:09 下午
 */
@Getter
@Setter
@ApiModel("删除数据模型位置信息")
public class DeleteDataModelSiteReqVo {

  @ApiModelProperty(value = "数据模型ID")
  @NotNull(message = "{400006}")
  private Integer dataModelId;

  @ApiModelProperty(value = "文件Id")
  @NotNull(message = "{400006}")
  private Integer fileId;
}
