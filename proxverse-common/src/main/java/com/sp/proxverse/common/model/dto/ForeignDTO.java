package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
@ApiModel("外键节点DTO")
public class ForeignDTO implements Serializable {

  private static final long serialVersionUID = 1788816420025414677L;

  @Tolerate
  public ForeignDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "文件ID")
  private Integer fileId;

  @ApiModelProperty(value = "该节点下挂载的子节点")
  private List<ForeignDTO> nextForeignList;

  @ApiModelProperty(value = "该节点的父节点")
  private ForeignDTO preForeign;

  @ApiModelProperty(value = "是否是主节点")
  private Boolean active;

  @ApiModelProperty(value = "该节点当前挂载的数据")
  private List<Map<String, String>> fileDataList;
}
