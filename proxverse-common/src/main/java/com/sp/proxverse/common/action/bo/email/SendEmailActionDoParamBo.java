package com.sp.proxverse.common.action.bo.email;

import com.sp.proxverse.common.action.bo.ActionDoParamBo;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-05-31 1:41 下午
 */
@Data
public class SendEmailActionDoParamBo extends ActionDoParamBo {

  /** 端口 （前端不用填写) */
  private String host;

  /** 端口 （前端不用填写) */
  private String port;

  /** 使用tls加密标识 （前端不用填写) */
  private Boolean tlsFlag;

  /** 发送账户（前端不用填写) */
  private String sendEmailAccount;

  /** 发件人姓名 */
  private String sendEmailName;

  /** 发送账户密码 （前端不用填写) */
  private String sendEmailPassword;

  /** 邮件主题 */
  private String subject;

  /** email内容 */
  private EmailContent emailContent;

  /** Email收件人地址 */
  private List<EmailRecipientBo> emailRecipients;

  /** 连接Id */
  private Integer connectId;

  private Boolean sslEnable;

  private Map<String, String> options;
}
