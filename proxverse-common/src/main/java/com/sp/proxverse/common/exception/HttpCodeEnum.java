package com.sp.proxverse.common.exception;

/**
 * <AUTHOR>
 * @create 2022-05-05 12:10 下午
 */
public enum HttpCodeEnum implements BaseErrorCode {

  /** 成功 */
  HTTP_SUCCESS(200, "success"),
  ;

  HttpCodeEnum(Integer code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  private final Integer code;
  private final String desc;

  @Override
  public Integer getCode() {
    return code;
  }

  @Override
  public String getDesc() {
    return desc;
  }
}
