package com.sp.proxverse.common.model.dto.dataauth;

import java.util.Map;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
public class UserDataAuth {

  @Tolerate
  public UserDataAuth() {
    // comment empty
  }

  private Set<Integer> topicIdAuthList;

  private Set<Integer> poolIdAuthList;

  private Map<Integer, Integer> topicAuthMap;

  private Map<Integer, Integer> poolAuthMap;
}
