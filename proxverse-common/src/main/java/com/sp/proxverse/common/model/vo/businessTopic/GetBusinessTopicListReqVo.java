package com.sp.proxverse.common.model.vo.businessTopic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-04-14 1:49 下午
 */
@Data
@ApiModel("获取topicInfo")
public class GetBusinessTopicListReqVo {

  @NotNull(message = "userId不能为空")
  private String userId;

  @ApiModelProperty(value = "是否快照")
  private Boolean snapshotFlag;

  /** BusinessTopicTypeEnum */
  @ApiModelProperty(
      value =
          " OVERALL(100, \"全景图\"),\n"
              + "    OVERALL_BUSINESS(101, \"业务全景图\"),\n"
              + "    BUSINESS_TOPIC(200, \"业务主题\"),\n"
              + "    BUSINESS_TREE(201, \"业务分析图谱\"),\n"
              + "    BUSINESS_KNOWLEDGE(202, \"业务知识模型\"),\n"
              + "    FLOW_ANALYSIS(203, \"流程分析\"),")
  private List<Integer> businessTopicType;
}
