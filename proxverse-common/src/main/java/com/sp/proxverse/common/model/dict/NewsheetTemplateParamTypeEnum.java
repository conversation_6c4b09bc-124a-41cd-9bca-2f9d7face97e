package com.sp.proxverse.common.model.dict;

public enum NewsheetTemplateParamTypeEnum {
  EXPRESSION(1, "表达式"),
  KPI(2, "引用kpi"),
  PARAM(3, "引用变量"),
  TEMP_TABLE(4, "临时表"),
  ;

  private final Integer value;
  private final String name;

  NewsheetTemplateParamTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static boolean isKpi(Integer value) {
    return KPI.value.equals(value);
  }

  public static boolean isParam(Integer value) {
    return PARAM.value.equals(value);
  }

  public static boolean isTempTable(Integer value) {
    return TEMP_TABLE.value.equals(value);
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
