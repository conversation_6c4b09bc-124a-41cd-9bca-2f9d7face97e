package com.sp.proxverse.common.model.vo.commandModule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-04-21 4:23 下午
 */
@Setter
@Getter
@ApiModel
public class GetOverallListReqVo {

  @ApiModelProperty(value = "用户Id")
  @NotNull(message = "{400006}")
  public Integer userId;

  @ApiModelProperty(value = "是否快照")
  @NotNull(message = "{400010}")
  private Boolean snapshotFlag;
}
