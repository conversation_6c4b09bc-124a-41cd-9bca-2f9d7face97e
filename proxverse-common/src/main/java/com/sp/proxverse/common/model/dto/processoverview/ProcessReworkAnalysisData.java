package com.sp.proxverse.common.model.dto.processoverview;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/22 10:11
 */
@Getter
@Setter
public class ProcessReworkAnalysisData {
  private String reworkRatio;
  private String maxReworkEvent;
  private String maxReworkEventRatio;
  private String mostOccurrence;

  public ProcessReworkAnalysisData() {}

  public ProcessReworkAnalysisData(
      String reworkRatio,
      String maxReworkEvent,
      String maxReworkEventRatio,
      String mostOccurrence) {
    this.reworkRatio = reworkRatio;
    this.maxReworkEvent = maxReworkEvent;
    this.maxReworkEventRatio = maxReworkEventRatio;
    this.mostOccurrence = mostOccurrence;
  }
}
