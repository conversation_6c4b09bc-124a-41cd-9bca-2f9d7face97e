package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class KpiParserParamDTO implements Serializable {

  private static final long serialVersionUID = -3647780657061477385L;

  @Tolerate
  public KpiParserParamDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "KPI")
  private KpiPO kpiPO;

  @ApiModelProperty(value = "文件ID，用以在mongo中查询数据")
  private Integer fileId;

  @ApiModelProperty(value = "事件名称")
  private String event;

  @ApiModelProperty(value = "聚合参数名称")
  private String aggregationParam;

  @ApiModelProperty(value = "聚合参数名称，因为数仓查询需要这个")
  private Integer aggregationParamFileId;

  @ApiModelProperty(value = "主题ID，用来底层kpi查询过滤项的")
  private Integer topicId;

  private Integer dataModelId;

  @ApiModelProperty(value = "表格ID")
  private Integer sheetId;

  @ApiModelProperty(value = "变体ID集合")
  private List<Integer> variantIdList;

  private DataModelFileDTO dataModelFileDTO;

  private List<TopicFilterPO> addFilterList;

  @ApiModelProperty(value = "kpi执行业务的时间范围")
  private String updateTime;

  @ApiModelProperty(value = "kpi执行业务的时间聚合维度")
  private Integer groupTimeColumnId;
}
