package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("相关变量的KPI计算结果DTO")
public class VariableKpiDTO {

  @Tolerate
  public VariableKpiDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "参数项名称")
  private String variable;

  private String caseCountNum;

  @ApiModelProperty(value = "kpi针对此parameter项所计算出的值")
  private String value;

  @ApiModelProperty(value = "kpi单位")
  private String unit;

  @ApiModelProperty(value = "参数项名称")
  private String source;

  @ApiModelProperty(value = "参数项名称")
  private String target;
}
