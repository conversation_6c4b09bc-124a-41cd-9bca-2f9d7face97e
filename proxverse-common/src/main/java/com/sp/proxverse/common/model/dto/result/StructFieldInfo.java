package com.sp.proxverse.common.model.dto.result;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.apache.spark.common.model.DataTypeEnum;

@Builder
@Data
@ApiModel
public class StructFieldInfo implements Serializable {

  @Tolerate
  public StructFieldInfo() {
    // comment empty
  }

  private String wordKind;

  private String columnName;

  private String originColumn;

  private Integer columnType;

  private String expressionLhs;

  public void setColumnTypeInt(String str) {
    DataTypeEnum dataTypeEnumByValue = DataTypeEnum.transformationDataTypeEnumByName(str);
    this.columnType = dataTypeEnumByValue.getValue();
  }
}
