package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("相关系数DTO对象")
public class RelatedDTO {

  @Tolerate
  public RelatedDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "变量名称")
  private String variantName;

  @ApiModelProperty(value = "case数量")
  private Long num;

  private BigDecimal sortRatio;

  private BigDecimal containRate;

  private BigDecimal notContainRate;
}
