package com.sp.proxverse.common.action.enums;

/**
 * <AUTHOR>
 * @create 2022-05-31 1:48 下午
 */
public enum SupperEmailEnum {
  /** 支持的邮箱 */
  TENCENT("tencent", 1000, "腾讯邮箱", "smtp.qq.com"),
  GOOGLE("google", 2000, "谷歌邮箱", "smtp.gmail.com"),
  NETEASE("网易163", 3000, "网易邮箱", "smtp.163.com"),
  YAHOO("yahoo", 4000, "雅虎邮箱", "smtp.mail.yahoo.com"),
  ;

  private Integer code;

  private String name;

  private String describe;

  private String host;

  SupperEmailEnum(String name, Integer code, String describe, String host) {
    this.code = code;
    this.name = name;
    this.describe = describe;
    this.host = host;
  }

  public Integer getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

  public String getDescribe() {
    return describe;
  }

  public String getHost() {
    return host;
  }
}
