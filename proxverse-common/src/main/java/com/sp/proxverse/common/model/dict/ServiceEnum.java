package com.sp.proxverse.common.model.dict;

public enum ServiceEnum {
  BUSINESS_TOPIC(100, "工作台"),
  DATA_MODEL(200, "数据模型"),
  EXECUTION(300, "执行计划"),
  ;

  private final Integer value;
  private final String name;

  ServiceEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
