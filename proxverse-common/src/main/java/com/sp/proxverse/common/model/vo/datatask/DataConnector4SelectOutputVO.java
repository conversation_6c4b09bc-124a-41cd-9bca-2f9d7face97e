package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据链接列表（select框）VO对象")
public class DataConnector4SelectOutputVO {
  @Tolerate
  public DataConnector4SelectOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据链接ID")
  private Integer dataConnectorId;

  @ApiModelProperty(value = "数据名称")
  private String name;
}
