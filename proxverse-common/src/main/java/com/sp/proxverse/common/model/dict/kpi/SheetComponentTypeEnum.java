package com.sp.proxverse.common.model.dict.kpi;

/** 组件类型 */
public enum SheetComponentTypeEnum {
  PROCESS_VIEW(10, "流程视图"),
  VARIANT_VIEW(20, "变体视图"),
  OLAP(30, "OLAP表单"),
  OLAP_LINE(31, "拆线图"),
  OLAP_BAR(32, "柱状图"),
  OLAP_PIE(33, "饼图"),
  OLAP_POINT(34, "点状图"),
  OLAP_PIVOT(35, "透视表"),
  WORD_CLOUD(36, "词云"),
  KPI(40, "KPI"),
  KPI_BOARD(41, "KPI仪表盘"),
  NUMBER_NEW(42, "新数字"),
  TEXT(50, "文字"),
  PICTURE(51, "图片"),
  CALC_TIME(60, "吞吐时间"),
  VARIABLE_FILTER(70, "变量过滤"),
  TIME_FILTER(71, "时间过滤"),
  PROCESS_CUT(80, "流程裁剪"),
  ECHARTS(90, "ECHARTS"),
  VARIABLE_INPUT(100, "变量输入"),
  BUTTON(110, "按钮"),
  PULL_DOWN(120, "下拉"),

  DIM_CONFIG(121, "维度设置"),
  CALC_TIME_PIVOT(122, "吞吐时间透视"),
  PROCESS_CONCENTRATION_COM(123, "流程集中度"),
  EVENT_FREQUENCY_COM(124, "事件发生频率"),
  UNKNOWN(0, "未知"),
  ;

  private final Integer value;
  private final String name;

  SheetComponentTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
