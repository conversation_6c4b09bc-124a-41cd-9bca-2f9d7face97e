package com.sp.proxverse.common.model.vo.processai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询事件列表时案例数量与占比VO对象")
public class CalEventRateOutputVO {
  @Tolerate
  public CalEventRateOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "案例数量")
  private String caseNum;

  @ApiModelProperty(value = "案例占比")
  private String caseRate;
}
