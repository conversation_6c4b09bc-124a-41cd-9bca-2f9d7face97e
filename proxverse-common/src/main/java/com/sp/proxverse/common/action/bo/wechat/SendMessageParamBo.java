package com.sp.proxverse.common.action.bo.wechat;

import com.alibaba.fastjson.annotation.JSONField;
import com.sp.proxverse.common.action.bo.wechat.param.WechatRunParamBo;
import lombok.Data;
import org.codehaus.jackson.annotate.JsonIgnore;

/**
 * 发送消息
 *
 * <AUTHOR>
 * @create 2022-06-06 6:17 下午
 */
@Data
public class SendMessageParamBo extends WechatRunParamBo {

  /** token */
  @JsonIgnore private String accessToken;

  /** 指明用户 */
  @JSONField(name = "touser")
  private String toUser;

  /** 消息类型 */
  @JSONField(name = "msgtype")
  private String msgType;

  /** 代理Id */
  @JSONField(name = "agentid")
  private Integer agentId;

  /** 表示是否是保密消息，0表示可对外分享，1表示不能分享且内容显示水印，默认为0 */
  @JSONField(name = "safe")
  private Integer safe;

  @JSONField(name = "text")
  public MessageContentBo message;
}
