package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class TopicScriptFilter {

  @Tolerate
  public TopicScriptFilter() {
    // comment empty
  }

  @ApiModelProperty(value = "表达式")
  private String expression;

  private String formatting;

  private String format;
}
