package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("参数kpi数据请求对象")
public class ParameterKpiDataRequest extends PageRequest {

  @ApiModelProperty(value = "当前主题表格ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private Integer componentId;

  @ApiModelProperty(value = "参数项名称")
  private String variable;

  @ApiModelProperty(value = "参数项名称所属fileId，此参数与variable如果有值都有值 ")
  private Integer variableFileId;

  @ApiModelProperty(value = "参数项名称所属fieldId，此参数与variable如果有值都有值 ")
  private Integer variableFieldId;

  @ApiModelProperty(value = "是否自带KPI函数信息")
  private boolean existKpiBoFlag;

  @ApiModelProperty(value = "自带KPI函数信息")
  private List<KpiFunctionDataReq> kpiFunctionDataList;

  @ApiModelProperty(value = "排序（现在暂时只有一个）")
  private List<KpiVariableSortDTO> sortList;

  @ApiModelProperty(value = "对字段搜索")
  private List<FieldSearchRequest> searchList;

  @ApiModelProperty(value = "行数规则（1：滚动，2：限制，3：前N行），注：OLAP表单可传1和2，柱状图和饼图可传2、3")
  private Integer rule;

  @ApiModelProperty(value = "限制行数，当rule=2、3时，此字段必填")
  private Integer limit;
}
