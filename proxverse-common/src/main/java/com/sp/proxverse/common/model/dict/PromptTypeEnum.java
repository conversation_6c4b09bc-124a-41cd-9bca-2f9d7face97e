package com.sp.proxverse.common.model.dict;

import java.util.Objects;

/** AI提示词全局导出 */
public enum PromptTypeEnum {
  PIE_CHART("cake-com", "pie-chart", "饼状图"),
  OLAP_CHART("table-com", "olap-chart", "OLAP表"),
  LINE_CHART("line-com", "line-chart", "折线图"),
  BAR_CHART("column-com", "bar-chart", "柱状图"),
  DOT_CHART("dot-com", "dot-chart", "点状图"),
  CALC_TIME_CHART("throughput-com", "calc-time-chart", "吞吐时间"),
  PROCESS_CHART("process-com", "process-chart", "流程视图"),
  VARIANT_CHART("variant-com", "variant-chart", "变体视图"),
  ;

  public static String getDescByCode(String value) {
    if (Objects.isNull(value)) {
      return "";
    }
    PromptTypeEnum[] promptTypeEnum = PromptTypeEnum.values();
    for (PromptTypeEnum values : promptTypeEnum) {
      if (Objects.equals(values.getValue(), value)) {
        return values.getPromptName();
      }
    }
    return "";
  }

  private final String value;
  private final String name;
  private final String promptName;

  PromptTypeEnum(String value, String name, String promptName) {
    this.value = value;
    this.name = name;
    this.promptName = promptName;
  }

  public String getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  public String getPromptName() {
    return promptName;
  }
}
