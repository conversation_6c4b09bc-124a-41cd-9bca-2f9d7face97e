package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-20 18:32
 */
@Data
@ApiModel("创建或者更新流程图Kpi")
public class UpdateTopicProcessKpiReqVo {

  @ApiModelProperty("流程图KPIId，如果为空则为创建")
  private Integer id;

  @ApiModelProperty("topicId")
  @NotNull(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty("流程图名字")
  @NotNull(message = "{400007}")
  private String name;

  @ApiModelProperty("icon")
  private String icon;

  @ApiModelProperty("事件颜色设置")
  private List<ColourInfo> eventColourInfo;

  @ApiModelProperty("连线颜色设置")
  private List<ColourInfo> lineColourInfo;

  @ApiModelProperty("绑定事件颜色KPI名字")
  private String eventColourKpiName;

  @ApiModelProperty("绑定连线颜色KPI名字")
  private String lineColourKpiName;

  @ApiModelProperty("孩子KPI信息")
  private List<UpdateTopicProcessDetailsKpiReqVo> updateTopicProcessDetailsKpiReqVos;

  @ApiModelProperty("需要删除的KPI")
  private Set<Integer> removeKpiByKpiId;
}
