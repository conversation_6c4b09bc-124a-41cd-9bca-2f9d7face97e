package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("获取数据模型详情(两个关联表即使要关联)请求对象")
public class Query4DataModelRelatedRequest {

  @ApiModelProperty(value = "数据模型ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataModelId;

  @ApiModelProperty(value = "两表ID", required = true)
  @NotEmpty(message = "{400006}")
  private List<Integer> fileIdList;
}
