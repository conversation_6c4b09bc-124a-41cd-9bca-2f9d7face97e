package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("创建数据任务关联数据提取请求对象")
public class SaveDataTaskExtractorRequest {

  @ApiModelProperty(value = "数据任务 ID", required = true)
  @NotNull(message = "{400006}")
  private Integer taskId;

  @ApiModelProperty(value = "数据提取ID", required = true)
  @NotNull(message = "{400006}")
  private Integer extractorId;

  public SaveDataTaskExtractorRequest(Integer taskId, Integer extractorId) {
    this.taskId = taskId;
    this.extractorId = extractorId;
  }
}
