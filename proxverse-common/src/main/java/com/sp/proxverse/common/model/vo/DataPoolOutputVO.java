package com.sp.proxverse.common.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Builder
@ApiModel("数据池列表VO对象")
public class DataPoolOutputVO {
  @Tolerate
  public DataPoolOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据池ID")
  private Integer dataId;

  @ApiModelProperty(value = "数据池名称")
  private String name;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "更新时间")
  private Date updateTime;

  @ApiModelProperty(value = "更新时间转换成常规格式")
  private String updateTimeZh;

  @ApiModelProperty(value = "0:数据模型，1：数据链接，2：数据提取，3：数据任务")
  private Integer type;

  private Integer authorityValue;

  private List<Integer> authorityValues;
}
