package com.sp.proxverse.common.model.page;

import com.sp.proxverse.common.model.dto.result.StructFieldInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/** <AUTHOR> */
@Data
@Builder
@ApiModel(value = "分页出参")
public class PageRespList<T> implements Serializable {

  @Tolerate
  public PageRespList() {
    // comment empty
  }

  private static final long serialVersionUID = 7451247139863093367L;

  @ApiModelProperty(value = "listObj")
  private transient List<T> list;

  @ApiModelProperty(value = "总条数", example = "100")
  private Long total;

  private StructFieldInfo[] metadata;

  @ApiModelProperty(value = "当前页码，不传默认为1", example = "1")
  private Integer pageNum = 1;

  @ApiModelProperty(value = "每页条数，不传默认为10", example = "1000")
  private Integer pageSize = 10;
}
