package com.sp.proxverse.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.dto.domain.Admin;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("oauth2")
public interface AdminMapper extends BaseMapper<Admin> {
  @InterceptorIgnore(tenantLine = "on")
  Admin getAdminSso(@Param("userName") String userName);

  @InterceptorIgnore(tenantLine = "on")
  Admin getAdmin(@Param("userName") String userName);

  @InterceptorIgnore(tenantLine = "on")
  List<Admin> getSapAdminList();

  @InterceptorIgnore(tenantLine = "on")
  List<Admin> getSapAdminAllList();

  @InterceptorIgnore(tenantLine = "on")
  Admin getAdminById(@Param("userId") Integer userId);

  @InterceptorIgnore(tenantLine = "on")
  Integer getAdminCount();

  @InterceptorIgnore(tenantLine = "on")
  List<Integer> getAdminIdList(int limit);

  @InterceptorIgnore(tenantLine = "on")
  List<Integer> getAdminIdListAll();
}
