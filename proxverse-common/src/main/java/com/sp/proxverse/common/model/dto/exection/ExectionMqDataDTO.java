package com.sp.proxverse.common.model.dto.exection;

import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
public class ExectionMqDataDTO {

  @Tolerate
  public ExectionMqDataDTO() {
    // comment empty
  }

  private Integer optimizationObjectId;

  private Integer signalId;

  private Map<String, String> data;
}
