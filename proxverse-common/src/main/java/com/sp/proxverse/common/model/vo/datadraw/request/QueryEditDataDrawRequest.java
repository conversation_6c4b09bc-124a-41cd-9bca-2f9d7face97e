package com.sp.proxverse.common.model.vo.datadraw.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("获取编辑数据提取回为信息请求对象")
public class QueryEditDataDrawRequest {

  @ApiModelProperty(value = "jdbc数据提取ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataExtractorId;
}
