package com.sp.proxverse.common.action;

import com.sp.proxverse.common.action.bo.connect.CheckConnectParamBo;
import com.sp.proxverse.common.action.bo.connect.CheckConnectResultBo;

/**
 * <AUTHOR>
 * @create 2022-06-15 1:05 下午
 */
public interface CheckConnectService {

  /**
   * 检查连接状态
   *
   * @param checkConnectParamBo
   * @return
   */
  CheckConnectResultBo doCheck(CheckConnectParamBo checkConnectParamBo);
}
