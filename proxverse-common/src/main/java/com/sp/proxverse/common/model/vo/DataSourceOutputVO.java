package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("数据模型列表VO对象")
public class DataSourceOutputVO {
  @Tolerate
  public DataSourceOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据源ID（dataModel的ID或者业务知识模型的ID）")
  private Integer dataId;

  @ApiModelProperty(value = "数据源名称")
  private String name;

  @ApiModelProperty(value = "数据池ID")
  private Integer poolId;

  @ApiModelProperty(value = "数据池名称")
  private String poolName;

  @ApiModelProperty(value = "状态（0：未加载，1：加载中，2：加载成功）")
  private Integer status;
}
