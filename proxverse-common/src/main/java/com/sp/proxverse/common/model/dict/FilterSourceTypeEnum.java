package com.sp.proxverse.common.model.dict;

import java.util.Objects;

public enum FilterSourceTypeEnum {
  SCRIPT_FILTER(1, "脚本过滤来的"),
  SHEET_FILTER(2, "sheet过滤来的"),
  CUSTOME_FILTER(3, "自定义过滤来的"),
  EVENT_LIST_FILTER(4, "事件列表过滤来的"),
  ;

  private final Integer value;
  private final String name;

  FilterSourceTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    FilterSourceTypeEnum[] values = FilterSourceTypeEnum.values();
    for (FilterSourceTypeEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value.getName();
      }
    }
    return "";
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
