package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("参数项sub")
public class RelatedVariableSubDTO {

  @Tolerate
  public RelatedVariableSubDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "参数项名称")
  private String variable;

  @ApiModelProperty(value = "字段ID")
  private Integer fieldId;

  @ApiModelProperty(value = "参数项数据类型")
  private Integer fieldType;

  @ApiModelProperty(value = "参数项是否是三要素（1:caseId,2:event,3:time，null:苍）")
  private Integer caseEventTimeType;

  @ApiModelProperty(value = "文件ID（即表ID）要删除")
  private Integer fileId;

  @ApiModelProperty(value = "文件名（即表名）要删除")
  private String fileName;

  private String refParam;

  @ApiModelProperty("1:local，2：知识模型")
  private Integer origin;
}
