package com.sp.proxverse.common.model.dict;

import java.util.Objects;

/** 连接器类型 */
public enum DataConnectorTypeEnum {
  JDBC(1, "jdbc"),
  KAFKA(2, "kafka"),
  INCREMENTAL_FILE(3, "FILE链接"), // bug修复#2961 显示类型incremental_file替换为FILE链接
  UNKNOWN(-1, "unknown"),
  ;

  private final Integer value;
  private final String name;

  DataConnectorTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static DataConnectorTypeEnum getDataConnectorTypeEnumByValue(Integer value) {
    for (DataConnectorTypeEnum dataConnectorTypeEnum : DataConnectorTypeEnum.values()) {
      if (Objects.equals(dataConnectorTypeEnum.getValue(), value)) {
        return dataConnectorTypeEnum;
      }
    }
    return UNKNOWN;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
