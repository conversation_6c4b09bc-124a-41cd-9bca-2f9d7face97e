package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询事件列表（事件过滤）请求对象")
public class QueryEventListRequest {
  @Tolerate
  public QueryEventListRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty(value = "1：跳过过滤项，0：不跳过过滤项")
  private Integer skipFilter;

  private Integer prikey;

  @ApiModelProperty(value = "字符排序标识")
  private Boolean charsSortFlag;

  public Boolean getCharsSortFlag() {
    if (this.charsSortFlag == null) {
      return false;
    }
    return this.charsSortFlag;
  }

  // 操作：2-页面查询、3-功能查询、不传默认事件查询
  public Integer eventIndex;
}
