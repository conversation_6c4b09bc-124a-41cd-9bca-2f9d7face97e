package com.sp.proxverse.common.model.vo.simulation;

import com.sp.proxverse.common.model.enums.Interval;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-20 14:47
 */
@Data
@ApiModel("创建流程仿真运行频率")
public class CreateOrUpdateSimulationRunReqVo {

  @ApiModelProperty("方案Id")
  @NotNull(message = "{400006}")
  private Integer programmeId;

  @ApiModelProperty(value = "Id 当不为空是更新")
  public Integer id;

  @ApiModelProperty("周")
  public List<Interval.IntervalEnum> intervalEnums;

  @ApiModelProperty("仿真ID")
  private Integer simulationId;

  @ApiModelProperty("开始时间")
  private String startTime;

  @ApiModelProperty("结束时间")
  private String endTime;

  @ApiModelProperty("频率：每天触发多少个")
  private String frequency;

  private String expression;

  private String formatting;

  private String format;
}
