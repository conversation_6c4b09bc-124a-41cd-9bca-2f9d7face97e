package com.sp.proxverse.common.model.dict;

public class PermissionIndexConst {

  // 指挥仓
  public static final int COMMAND = 1000;
  // 指挥仓-查看权限
  public static final int COMMAND_VIEW = 1000;
  // 指挥仓-分享权限
  public static final int COMMAND_SHARE = 1002;

  // 工作台
  public static final int WORKBENCH = 2000;
  // 工作台-查看权限
  public static final int WORKBENCH_VIEW = 2000;
  // 工作台-创建业务分析
  public static final int WORKBENCH_CREATE_BUSINESS = 2001;
  // 工作台-权限设置
  public static final int WORKBENCH_PERMISSION_SETTING = 2002;
  // 工作台-发布
  public static final int WORKBENCH_RELEASE = 2003;
  // 工作台-下载
  public static final int WORKBENCH_DOWNLOAD = 2004;
  // 工作台-创建执行动作
  public static final int WORKBENCH_CREATE_ACTION = 2100;
  // 工作台-创建知识模型
  public static final int WORKBENCH_CREATE_KNOWLEDGE = 2200;
  // 工作台-创建流程仿真
  public static final int WORKBENCH_CREATE_SIMULATION = 2300;

  // 执行优化-优化信号
  public static final int SIGNAL = 3000;

  // 流程管理
  public static final int PROCESS_MANAGER = 4000;
  // 流程管理-查看
  public static final int PROCESS_MANAGER_VIEW = 4001;
  // 流程管理-创建一级流程
  public static final int PROCESS_MANAGER_CREATE_SUPER = 4002;
  // 流程管理-发布
  public static final int PROCESS_MANAGER_RELEASE = 4003;
  // 流程管理-下载BPMN
  public static final int PROCESS_MANAGER_DOWNLOAD_BPMN = 4004;
  // 流程管理-下载SVG
  public static final int PROCESS_MANAGER_DOWNLOAD_SVG = 4005;

  // 数据融合
  public static final int DATA_MERGE = 5000;
  // 数据融合-查看
  public static final int DATA_MERGE_VIEW = 5001;
  // 数据融合-导入数据
  public static final int DATA_MERGE_IMPORT = 5002;
  // 数据融合-查看数据链接
  public static final int DATA_MERGE_CONNECTOR_VIEW = 5100;
  // 数据融合-查看数据模型
  public static final int DATA_MERGE_MODEL_VIEW = 5200;

  // 用户管理
  public static final int USER_MANAGER = 6000;
  // 用户管理-创建用户
  public static final int USER_MANAGER_CREATE = 6001;
  // 用户管理-修改用户
  public static final int USER_MANAGER_UPDATE = 6002;
  // 用户管理-启用用户
  public static final int USER_MANAGER_START = 6003;
  // 用户管理-停用用户
  public static final int USER_MANAGER_PAUSE = 6004;
  // 用户管理-删除用户
  public static final int USER_MANAGER_DELETE = 6005;
}
