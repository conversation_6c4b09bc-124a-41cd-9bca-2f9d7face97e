package com.sp.proxverse.common.model.vo.dataconnector;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取任务历史已执行列表VO对象")
public class HistoryTaskExecOutputVO {
  @Tolerate
  public HistoryTaskExecOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "开始时间")
  private String startTime;

  @ApiModelProperty(value = "数据任务名称")
  private String taskName;

  @ApiModelProperty(value = "执行时间")
  private String execTime;

  @ApiModelProperty(value = "状态（1：完成，2：运行中，3：失败）")
  private Integer status;

  @ApiModelProperty(value = "数据池名称")
  private String poolName;
}
