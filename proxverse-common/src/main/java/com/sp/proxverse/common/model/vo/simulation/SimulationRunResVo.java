package com.sp.proxverse.common.model.vo.simulation;

import com.sp.proxverse.common.model.enums.Interval;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-22 10:34
 */
@Data
@ApiModel("流程仿真运行响应")
public class SimulationRunResVo {

  @ApiModelProperty("开始事件名称")
  private String startEvent;

  @ApiModelProperty(" 开始时间")
  private String startTime;

  @ApiModelProperty("运行Id")
  private Integer runId;

  @ApiModelProperty(" 结束时间")
  private String endTime;

  @ApiModelProperty("频率：每天触发多少个")
  private String frequency;

  @ApiModelProperty("周几触发")
  private List<Interval.IntervalEnum> week;

  private String expression;

  private String formatting;

  private String format;
}
