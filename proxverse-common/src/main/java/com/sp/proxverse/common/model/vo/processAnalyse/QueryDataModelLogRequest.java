package com.sp.proxverse.common.model.vo.processAnalyse;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-04-25 1:18 下午
 */
@Getter
@Setter
@ApiModel("获取数据模型日志")
public class QueryDataModelLogRequest extends PageRequest {
  @ApiModelProperty(value = "数据模型ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataModelId;
}
