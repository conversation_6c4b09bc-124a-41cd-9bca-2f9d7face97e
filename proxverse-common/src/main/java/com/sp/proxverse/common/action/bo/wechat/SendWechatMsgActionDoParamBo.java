package com.sp.proxverse.common.action.bo.wechat;

import com.sp.proxverse.common.action.bo.ActionDoParamBo;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-06-09 5:48 下午
 */
@Data
public class SendWechatMsgActionDoParamBo extends ActionDoParamBo {

  /** 执行动作连接Id */
  private Integer connectId;

  /** 收件人Id */
  private List<String> recipientId;

  /** 消息正文 */
  private String content;
}
