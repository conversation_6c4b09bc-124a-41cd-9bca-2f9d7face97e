package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.po.ActionLogPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 执行动作日志 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Mapper
public interface ActionLogMapper extends BaseMapper<ActionLogPo> {

  /**
   * 获取最新的日志
   *
   * @param actionFlowId
   * @return
   */
  List<ActionLogPo> getNewestList(Integer actionFlowId);
}
