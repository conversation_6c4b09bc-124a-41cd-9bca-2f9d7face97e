package com.sp.proxverse.common.model.dto.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_group_contact")
public class GroupContactPO {

  @Tolerate
  public GroupContactPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("公司Id")
  private Integer groupId;

  @ApiModelProperty(value = "数据来源，：1，延长有效期，2，升级版本", required = true)
  @NotNull(message = "数据来源")
  private Integer source;

  @ApiModelProperty("状态：0 未处理，1已处理")
  private Integer state;

  @ApiModelProperty("备注")
  private String remark;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;
}
