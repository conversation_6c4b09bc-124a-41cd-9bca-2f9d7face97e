package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("一致性探索kpi数据DTO")
public class ConformanceKipDTO {

  @Tolerate
  public ConformanceKipDTO() {
    // comment empty
  }

  @ApiModelProperty("kpiId")
  private Integer kpiId;

  @ApiModelProperty("kpi名称")
  private String kpiName;

  @ApiModelProperty("kpi单位")
  private String unit;

  @ApiModelProperty("此偏差事件在此kpi中计算的值")
  private String kpiValue;

  @ApiModelProperty("此偏差事件在此kpi中计算的值与全量kpi相比是上升还是下降（1：向上，2：向下），为null则不展示箭头了")
  private Integer upDown;

  @ApiModelProperty("与全量kpi相比的差值")
  private BigDecimal upDownValue;

  private String formatting;

  private String format;

  private String columnType;
}
