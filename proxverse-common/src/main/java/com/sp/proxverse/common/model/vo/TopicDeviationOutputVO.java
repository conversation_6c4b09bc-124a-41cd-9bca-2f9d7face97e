package com.sp.proxverse.common.model.vo;

import com.sp.proxverse.common.model.dto.DeviationKipDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("业务主题下的偏差事件VO对象")
public class TopicDeviationOutputVO implements Serializable {

  private static final long serialVersionUID = 1L;

  @Tolerate
  public TopicDeviationOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "当前业务分析图谱这个主题的ID")
  private Integer topicId;

  @ApiModelProperty(value = "事件名称")
  private String eventName;

  @ApiModelProperty(value = "当前事件占全量事件的比例，结果不带百分号，比如55.2%给出的值为55.2")
  private BigDecimal rate;

  @ApiModelProperty(value = "当前事件有多少个")
  private Integer value;

  private String filterExpression;

  @ApiModelProperty(value = "偏差事件中的kpi数据")
  private List<DeviationKipDTO> deviationKipDTOList;
}
