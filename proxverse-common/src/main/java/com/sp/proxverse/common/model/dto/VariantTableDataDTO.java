package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("可变表头的列表数据前端渲染DTO")
public class VariantTableDataDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  @Tolerate
  public VariantTableDataDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "事件名称")
  private List<Lable> headerData;

  @ApiModelProperty(value = "发生时间")
  private List<Map<String, String>> tableData;

  @ApiModelProperty(value = "文件名称")
  private String fileName;
}
