package com.sp.proxverse.common.model.vo.dataconnector.request;

import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.dict.DataConnectorJDBCTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
@ApiModel("创建jdbc请求对象")
public class SaveJdbcConnectorRequest {

  @ApiModelProperty(value = "数据池ID", required = true)
  @NotNull(message = "{400006}")
  private Integer poolId;

  @ApiModelProperty(value = "数据链接名称", required = true)
  @NotBlank(message = "{400007}")
  private String name;

  @ApiModelProperty(value = "IP", required = true)
  private String ip;

  @ApiModelProperty(value = "端口", required = true)
  private Integer port;

  @ApiModelProperty(value = "库名称", required = true)
  private String dbname;

  @ApiModelProperty(value = "实例号（HANA DB）", required = true)
  private String instanceNumber;

  @ApiModelProperty(value = "schema名称", required = true)
  private String schemaName;

  @ApiModelProperty(value = "数据库用户名", required = true)
  private String username;

  @ApiModelProperty(value = "数据库密码", required = true)
  private String password;

  @ApiModelProperty(value = "1：标准链接，2：自定义链接", required = true)
  @NotNull(message = "链接类型不能为空")
  private Integer bizType;

  @ApiModelProperty(
      value = "jdbc类型（1：mysql,2:oracle,3:db2,4:hive,5:postgreSql,6:impala），当bizType=1时此字段不能为空")
  private Integer jdbcType;

  @ApiModelProperty(value = "自定义链接，当bizType=2时此字段不能为空")
  private String connectLink;

  public void check() {
    if (Objects.equals(bizType, 1)) {
      if (StringUtils.isBlank(ip) || Objects.isNull(port) || StringUtils.isBlank(dbname)) {
        throw new BizException(5000, "IP、端口、数据库名称不能为空");
      }
      if (Objects.equals(jdbcType, DataConnectorJDBCTypeEnum.DB2.getValue())) {
        if (StringUtils.isBlank(schemaName)) {
          throw new BizException(5000, "DB2数据库请输入schema名称");
        }
      }
      if (!Objects.equals(jdbcType, DataConnectorJDBCTypeEnum.HIVE.getValue())) {
        if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
          throw new BizException(5000, "用户名和密码不能为空");
        }
      }
    } else {
      if (StringUtils.isBlank(connectLink)) {
        throw new BizException(5000, "自定义链接不能为空");
      }
      if (Objects.isNull(jdbcType)) {
        throw new BizException(5000, "数据链接类型不能为空");
      }
    }
  }
}
