package com.sp.proxverse.common.model.dict;

/** event和case维度 */
public enum DimensionEnum {
  EVENT(1, "event维度"),
  CASE(2, "case维度"),
  ;

  private final Integer value;
  private final String name;

  DimensionEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
