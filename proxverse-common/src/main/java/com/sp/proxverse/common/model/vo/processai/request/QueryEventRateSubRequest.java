package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询事件列表时案例数量与占比（事件过滤）sub请求对象")
public class QueryEventRateSubRequest {
  @Tolerate
  public QueryEventRateSubRequest() {
    // comment empty
  }

  @ApiModelProperty(
      value = "过滤项类型（100：流程通过，101：通过任意，200：流程不通过，201：不通过任意，300：流程开始于，400：流程结束于",
      required = true)
  @NotNull(message = "{400008}")
  private Integer type;

  @ApiModelProperty(value = "事件列表", required = true)
  @NotEmpty(message = "{400009}")
  private List<String> eventList;
}
