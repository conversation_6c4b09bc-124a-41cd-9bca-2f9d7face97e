package com.sp.proxverse.common.model.dict;

import io.swagger.annotations.ApiModel;

@ApiModel("运行状态枚举类")
public enum RunningStatusEnum {
  SUSPEND(1, "暂停"),
  IN_OPERATION(0, "运行中"),
  ;

  private final Integer value;
  private final String name;

  RunningStatusEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
