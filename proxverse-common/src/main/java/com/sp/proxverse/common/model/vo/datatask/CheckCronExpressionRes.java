package com.sp.proxverse.common.model.vo.datatask;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/17 10:26
 */
@Data
public class CheckCronExpressionRes {

  private boolean isCheckPassed = true;

  /** 错误信息 */
  private String message;

  /** 下一次执行时间 */
  private String nextExecutionTime;

  private Date nextExecutionTimeDate;

  private String nextNextExecutionTime;

  private Date nextNextExecutionTimeDate;

  /** 运行间隔 */
  private Long runningInterval;
}
