package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("选三要素DTO,v1.1新增")
public class TableDataDTO {

  @Tolerate
  public TableDataDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "字段名称")
  private String propName;

  @ApiModelProperty(value = "v1.1字段类型（1:int,2:float,3:bool,4:string,5:date,6:time,7:datetime）")
  private Integer fieldType;

  @ApiModelProperty(
      value = "时间格式，当fieldType=5/6/7时，此字段有值，此值在前端可编辑，然后提交时（setFieldType接口）带过来，1.6.3改动")
  private String dateFormat;

  @ApiModelProperty(value = "v1.1字段ID，创建知识模型选择三要素字段时，原先传下标现在传这个ID")
  private Integer fieldId;

  @ApiModelProperty(value = "字段名称")
  private String field;

  @ApiModelProperty(value = "数据列表")
  private List<ValueDTO> list;

  private String originalColumnName;
}
