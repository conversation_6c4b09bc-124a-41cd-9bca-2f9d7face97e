package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("获取数据模型文件请求对象")
public class DeleteDataModelRequest {

  @ApiModelProperty(value = "数据模型ID,v1.1修改", required = true)
  @NotNull(message = "{400006}")
  private Integer dataModelId;

  @ApiModelProperty(value = "所要删除的文件ID,v1.1修改", required = true)
  @NotNull(message = "{400006}")
  private Integer fileId;
}
