package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class StoreFileDTO {

  @Tolerate
  public StoreFileDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "模型ID")
  private Integer poolId;

  @ApiModelProperty(value = "文件ID")
  private Integer fileId;

  private String encoding;
}
