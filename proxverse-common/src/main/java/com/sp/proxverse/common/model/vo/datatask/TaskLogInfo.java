package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-16 17:40
 */
@Data
public class TaskLogInfo {

  private String time;

  private String name;

  private Long runTime;

  @ApiModelProperty(" 运行类型 0 ： 自动运行， 1手动运行 默认为0；")
  private Integer runType;

  /** @see TaskEnum */
  @ApiModelProperty("任务类型：数据提取：1， 模型刷新：2，ELT：3")
  private Integer taskType;

  @ApiModelProperty("状态: 0，暂停，1，开启，2，已经执行，3 正在执行，5 失败")
  private Integer state;

  private Object info;

  private Integer id;
}
