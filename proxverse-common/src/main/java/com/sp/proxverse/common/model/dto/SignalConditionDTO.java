package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * 优化信号条件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-09 19:07:25
 */
@Builder
@Data
@ApiModel("优化信号条件数据层")
public class SignalConditionDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  @Tolerate
  public SignalConditionDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "优化信号id")
  private Integer signalId;

  @ApiModelProperty(value = "数据模型id")
  private Integer dataModelId;

  @ApiModelProperty(value = "文件id（表）")
  private Integer fileId;

  @ApiModelProperty(value = "列id")
  private Integer columnId;

  @ApiModelProperty(
      value = "字段类型规则",
      notes = "规则（=  !=），（<  <=  =  >=  >），（BETWEEN ... AND）",
      dataType = "com.sp.execution.model.dict.RuleEnum")
  private Integer rule;

  @ApiModelProperty(value = "匹配值")
  private List<String> matchValue;
}
