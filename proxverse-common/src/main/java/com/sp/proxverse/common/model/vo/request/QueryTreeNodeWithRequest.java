package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.util.ExpressionBuilderUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询业务图谱某个事件悬浮显示数据请求对象")
public class QueryTreeNodeWithRequest {

  @ApiModelProperty(value = "topicId", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  private Integer sheetId;

  @ApiModelProperty(value = "流程图中需要过滤的属性")
  public List<String> filterEvents;

  @ApiModelProperty(value = "事件名称", required = true)
  @NotNull(message = "{400007}")
  private String event;

  private String columnName;

  @ApiModelProperty(value = "分组信息")
  private LinkedHashMap<String, Set<String>> groupInfo;

  public boolean getGroupInfoFlag() {
    return ExpressionBuilderUtil.checkGroup(groupInfo);
  }

  public List<String> getFilterEvents() {
    if (this.filterEvents == null) {
      return new ArrayList<>();
    }
    return this.filterEvents;
  }
}
