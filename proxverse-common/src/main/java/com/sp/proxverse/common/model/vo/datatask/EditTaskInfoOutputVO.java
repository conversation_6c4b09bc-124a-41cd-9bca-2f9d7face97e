package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据提取编辑信息VO对象")
public class EditTaskInfoOutputVO {
  @Tolerate
  public EditTaskInfoOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据提取ID")
  private Integer taskId;

  @ApiModelProperty(value = "数据名称")
  private String name;

  @ApiModelProperty(value = "任务频率（1：每小时，2：每天，3：每周）")
  private Integer rateType;

  @ApiModelProperty(value = "分钟，此字段必填，0，15，30，45")
  private Integer minute;

  @ApiModelProperty(value = "小时（0-23），当rateType=2、3时，此字段必填")
  private Integer hour;

  @ApiModelProperty(value = "周1-周日（1、2、3、4、5、6、7），当rateType=3时，此字段必填")
  private Integer week;
}
