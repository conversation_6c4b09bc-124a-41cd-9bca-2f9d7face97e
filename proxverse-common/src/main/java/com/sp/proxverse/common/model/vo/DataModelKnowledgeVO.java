package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@ApiModel("业务知识")
public class DataModelKnowledgeVO {

  private Integer id;

  @ApiModelProperty("所属数据模型ID")
  private Integer dataModelId;

  @ApiModelProperty("知识文件大小")
  private Integer sentenceSize;

  @ApiModelProperty("知识文件字节")
  private byte[] contentBytes;

  @ApiModelProperty("知识文件内容")
  private String content;

  @ApiModelProperty("知识文件名")
  private String filename;

  @ApiModelProperty("知识文件")
  private String knowledgeBaseName;

  @ApiModelProperty("知识文件描述")
  private String description;

  @ApiModelProperty("状态")
  private Integer status;

  @ApiModelProperty("知识文件后缀")
  private String fileExt;
}
