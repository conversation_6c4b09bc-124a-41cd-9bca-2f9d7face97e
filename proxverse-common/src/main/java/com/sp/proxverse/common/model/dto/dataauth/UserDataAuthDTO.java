package com.sp.proxverse.common.model.dto.dataauth;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
public class UserDataAuthDTO implements Serializable {

  @Tolerate
  public UserDataAuthDTO() {
    // comment empty
  }

  private Integer id;

  @ApiModelProperty("10:用户，20：用户组")
  private Integer idType;

  private String userName;

  private String name;

  private String level;
}
