package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.dto.TopicDataDTO;
import com.sp.proxverse.common.model.po.BusinessTopicPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BusinessTopicMapper extends BaseMapper<BusinessTopicPO> {

  List<TopicDataDTO> getTopicDataDtoByModelId(
      @Param("modelId") Integer modelId, @Param("ids") List<Integer> topicIdList);
}
