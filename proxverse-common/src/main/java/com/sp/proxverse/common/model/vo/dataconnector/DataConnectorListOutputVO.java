package com.sp.proxverse.common.model.vo.dataconnector;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据池链接列表VO对象")
public class DataConnectorListOutputVO {
  @Tolerate
  public DataConnectorListOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "连接器ID")
  private Integer connectorId;

  @ApiModelProperty(value = "链接名称")
  private String connectorName;

  @ApiModelProperty(value = "类型（1：JDBC，2：KAFKA）")
  private Integer jdbcType;

  private String jdbcTypeName;

  @ApiModelProperty(value = "状态（0：不可用，1：可用）")
  private Integer status;

  @ApiModelProperty(value = "数据提取")
  private String drawList;
}
