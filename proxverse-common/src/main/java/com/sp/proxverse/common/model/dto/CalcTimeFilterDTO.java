package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
public class CalcTimeFilterDTO {

  @Tolerate
  public CalcTimeFilterDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "事件1")
  private String event1;

  //    @ApiModelProperty(value = "事件1的类型,(10：直接跟随，20：跟随，30：不直接跟随，40：不跟随)")
  //    private Integer type1;

  @ApiModelProperty(value = "事件2")
  private String event2;
}
