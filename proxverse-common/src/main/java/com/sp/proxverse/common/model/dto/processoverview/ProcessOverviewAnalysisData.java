package com.sp.proxverse.common.model.dto.processoverview;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/22 10:11
 */
@Getter
@Setter
public class ProcessOverviewAnalysisData {
  private String totalCaseNum;
  private String totalEventNum;
  private String totalVariantNum;
  private String avgCaseNum;
  private String avgEventNum;
  private String avgDuration;
  private String avgFTE;

  public ProcessOverviewAnalysisData() {}

  public ProcessOverviewAnalysisData(
      String totalCaseNum,
      String totalEventNum,
      String totalVariantNum,
      String avgCaseNum,
      String avgEventNum,
      String avgDuration,
      String avgFTE) {
    this.totalCaseNum = totalCaseNum;
    this.totalEventNum = totalEventNum;
    this.totalVariantNum = totalVariantNum;
    this.avgCaseNum = avgCaseNum;
    this.avgEventNum = avgEventNum;
    this.avgDuration = avgDuration;
    this.avgFTE = avgFTE;
  }
}
