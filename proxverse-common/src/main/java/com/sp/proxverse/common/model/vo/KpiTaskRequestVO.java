package com.sp.proxverse.common.model.vo;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("kpi任务VO请求对象")
public class KpiTaskRequestVO extends PageRequest {
  @Tolerate
  public KpiTaskRequestVO() {
    // comment empty
  }

  @ApiModelProperty(value = "kpi关联主键id")
  private Integer kpiRelationId;
}
