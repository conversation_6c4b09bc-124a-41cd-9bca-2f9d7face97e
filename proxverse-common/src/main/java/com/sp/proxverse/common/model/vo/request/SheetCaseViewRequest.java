package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("表格sheet案例数据请求对象")
public class SheetCaseViewRequest extends PageRequest {

  @ApiModelProperty(value = "业务主题表格Id", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private List<FieldSearchRequest> searchList;
}
