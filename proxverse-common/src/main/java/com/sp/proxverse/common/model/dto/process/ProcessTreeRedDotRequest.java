package com.sp.proxverse.common.model.dto.process;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2023/12/12 11:27
 */
@Getter
@Setter
@Builder
public class ProcessTreeRedDotRequest {

  @Tolerate
  public ProcessTreeRedDotRequest() {
    // comment empty
  }

  private Integer sheetId;

  private String event;

  private Integer limit;
}
