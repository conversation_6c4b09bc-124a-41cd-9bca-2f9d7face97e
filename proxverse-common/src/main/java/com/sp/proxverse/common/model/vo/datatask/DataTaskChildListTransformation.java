package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-11 11:39
 */
@Data
@ApiModel("数据任务孩子列表数据转换")
public class DataTaskChildListTransformation {

  private Integer transformationId;

  private Integer dataTaskChildId;

  private String name;

  private Integer dataTaskId;

  @ApiModelProperty(" 0运行，1，暂停")
  private Integer state;
}
