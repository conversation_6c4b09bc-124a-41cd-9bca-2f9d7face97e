package com.sp.proxverse.common.model.dto.process;

import com.sp.proxverse.common.model.enums.ProcessPathKpiType;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.vo.request.ProcessTreeBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/19 16:07
 */
@Data
@ApiModel(value = "树变体请求对象")
public class ProcessTreeVariantRequest extends ProcessTreeBase {

  @ApiModelProperty(value = "排序规则 ：0 降序，1 升续")
  private Integer sortingRules;

  private String showPathKpiName;

  private String sortingPathKpiName;

  private ProcessPathKpiType sortingPathKpiType;

  private ProcessPathKpiType showPathKpiType;

  @ApiModelProperty(value = "变体列表中，是否显示'其他'变体")
  private Boolean variantOtherFlag;

  @ApiModelProperty(value = "是否显示'其他'变体")
  private Boolean processtreeonly;

  private List<TopicFilterPO> addFilterList;

  @ApiModelProperty(value = "变体数量")
  private Integer variantNumber;

  private Boolean mergeFlag;

  private Boolean variantAllFlag;

  public Integer getVariantNumber() {
    if (variantNumber == null) {
      return 9;
    }
    return this.variantNumber;
  }

  public Boolean getVariantOtherFlag() {
    if (this.variantOtherFlag == null) {
      return false;
    }
    return this.variantOtherFlag;
  }

  public Integer getSortingRules() {
    if (this.sortingRules == null) {
      return 0;
    }
    return this.sortingRules;
  }

  public ProcessPathKpiType getSortingPathKpiType() {
    if (sortingPathKpiType == null) {
      return ProcessPathKpiType.CASE_NUMBER;
    }
    return this.sortingPathKpiType;
  }

  public ProcessPathKpiType getShowPathKpiType() {
    if (showPathKpiType == null) {
      return ProcessPathKpiType.CASE_NUMBER;
    }
    return this.showPathKpiType;
  }
}
