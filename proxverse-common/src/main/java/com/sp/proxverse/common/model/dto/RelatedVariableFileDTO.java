package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("参数项关联file")
public class RelatedVariableFileDTO {

  @Tolerate
  public RelatedVariableFileDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "文件ID（即表ID）")
  @NotNull(message = "文件ID不能为空")
  private Integer fileId;

  @ApiModelProperty(value = "文件名（即表名）")
  private String fileName;

  @ApiModelProperty(value = "1：表示是table，2：表示是kpi，3：表示是变量")
  private Integer type;

  @ApiModelProperty(value = "文件中文名")
  private String remark;

  @ApiModelProperty(value = "文件名（即表名）")
  private List<RelatedVariableSubDTO> variableSubDTOList;

  public void setType(Integer type) {
    this.type = type;
  }

  public void setType(Type type) {
    this.type = type.ordinal() + 1;
  }

  public enum Type {
    TABLE,
    KPI,
    VARIABLE,
    TEMPVIEW
  }
}
