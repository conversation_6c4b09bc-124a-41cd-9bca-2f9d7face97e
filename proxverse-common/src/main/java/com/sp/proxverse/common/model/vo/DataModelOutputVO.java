package com.sp.proxverse.common.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Builder
@ApiModel("数据模型列表VO对象")
public class DataModelOutputVO {
  @Tolerate
  public DataModelOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据源ID（dataModel或者业务知识模型）")
  private Integer dataId;

  @ApiModelProperty(value = "数据源名称")
  private String name;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "更新时间")
  private LocalDateTime updateTime;

  @ApiModelProperty(value = "更新时间转换成常规格式")
  private String updateTimeZh;

  private Integer status;

  private Integer currentVersion;

  private Integer versionNumberLimit;
}
