package com.sp.proxverse.common.model.dto.topicSheet;

import com.sp.proxverse.common.model.po.KpiParamPO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class TopicKpiDto {
  @Tolerate
  public TopicKpiDto() {
    // comment empty
  }

  private Integer kpiId;

  private String kpiName;

  private String expression;

  @ApiModelProperty("1:local，2：知识模型")
  private Integer origin;

  @ApiModelProperty("1:有重名，需要显示！ 名称相同，请修改名称之后再使用，2：没有重名不显示！")
  private Integer duplication;

  private List<KpiParamPO> paramList;

  private String formatting;

  private String format;

  private String columnType;
}
