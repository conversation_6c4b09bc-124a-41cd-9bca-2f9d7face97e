package com.sp.proxverse.common.model.base;

import com.sp.proxverse.common.model.page.PageRespList;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel("请求返回数据")
public class PageResp implements Serializable {
  private static final long serialVersionUID = -5661206328302037980L;

  @ApiModelProperty("错误码")
  private Integer code;

  @ApiModelProperty("错误信息")
  private String msg;

  @ApiModelProperty("业务数据")
  private PageRespList data;

  public PageResp() {
    // comment empty
  }

  public static PageResp success(PageRespList body) {
    PageResp rtn = new PageResp();
    rtn.setCode(2000);
    rtn.setData(body);
    return rtn;
  }
}
