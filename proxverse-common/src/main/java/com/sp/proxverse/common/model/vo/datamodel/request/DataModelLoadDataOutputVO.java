package com.sp.proxverse.common.model.vo.datamodel.request;

import com.sp.proxverse.common.model.dto.DataModelLoadingDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据模型加载的数据列表VO对象")
public class DataModelLoadDataOutputVO {
  @Tolerate
  public DataModelLoadDataOutputVO() {
    // comment empty
  }

  /** @see DataModelRunStatusEnum */
  @ApiModelProperty(value = "加载状态（0：未加载，1：加载中，2：已加载，3,加载失败,4 警告）")
  private Integer status;

  @ApiModelProperty(value = "正在加载的表")
  private List<DataModelLoadingDTO> loadingList;

  @ApiModelProperty(value = "加载成功的表")
  private List<DataModelLoadingDTO> loadingSuccList;
}
