package com.sp.proxverse.common.common;

import java.util.List;

public class LocalParameter {

  public static ThreadLocal<List<String>> componentFilterThreadLocal = new ThreadLocal<>();

  /** topicFilter,sheetFilter,componentFilter 是否生效 */
  public static ThreadLocal<Boolean> disableFilterThreadLocal = new ThreadLocal<>();

  /** startTimeThreadLocal 每一个请求的开始时间 */
  public static ThreadLocal<Long> startTimeThreadLocal = new ThreadLocal<>();
}
