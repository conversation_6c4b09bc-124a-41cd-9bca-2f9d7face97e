package com.sp.proxverse.common.model.job;

import com.sp.proxverse.common.model.po.FileFieldPO;
import com.sp.proxverse.common.model.po.FilePO;
import com.sp.proxverse.common.model.vo.datamodel.request.SetFieldTypeListRequest;
import java.util.List;

public class IncFileArgs {
  private FilePO filePO;
  private String path;
  private SetFieldTypeListRequest request;
  private List<FileFieldPO> fieldPOS;
  private String primaryKey;
  private String updateTime;

  public IncFileArgs(
      FilePO filePO, String path, SetFieldTypeListRequest request, List<FileFieldPO> fieldPOS) {
    this.filePO = filePO;
    this.path = path;
    this.request = request;
    this.fieldPOS = fieldPOS;
  }

  public IncFileArgs(
      FilePO filePO,
      String path,
      SetFieldTypeListRequest request,
      List<FileFieldPO> fieldPOS,
      String primaryKey,
      String updateTime) {
    this.filePO = filePO;
    this.path = path;
    this.request = request;
    this.fieldPOS = fieldPOS;
    this.primaryKey = primaryKey;
    this.updateTime = updateTime;
  }

  // Getter 和 Setter 方法
  public String getPrimaryKey() {
    return primaryKey;
  }

  public void setPrimaryKey(String primaryKey) {
    this.primaryKey = primaryKey;
  }

  public String getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(String updateTime) {
    this.updateTime = updateTime;
  }

  public void setUpdate_time(String update_time) {
    this.updateTime = updateTime;
  }

  public FilePO getFilePO() {
    return filePO;
  }

  public void setFilePO(FilePO filePO) {
    this.filePO = filePO;
  }

  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public SetFieldTypeListRequest getRequest() {
    return request;
  }

  public void setRequest(SetFieldTypeListRequest request) {
    this.request = request;
  }

  public List<FileFieldPO> getFieldPOS() {
    return fieldPOS;
  }

  public void setFieldPOS(List<FileFieldPO> fieldPOS) {
    this.fieldPOS = fieldPOS;
  }

  // toString 方法，用于打印类内容
  @Override
  public String toString() {
    return "IncFileArgs{"
        + "filePO="
        + filePO
        + ", path='"
        + path
        + '\''
        + ", request="
        + request
        + ", fieldPOS="
        + fieldPOS
        + ", primaryKey='"
        + primaryKey
        + '\''
        + ", updateTime='"
        + updateTime
        + '\''
        + '}';
  }
}
