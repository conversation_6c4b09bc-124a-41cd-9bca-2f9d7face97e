package com.sp.proxverse.common.model.dto.conformance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class ConformanceGeneralData {
  @Tolerate
  public ConformanceGeneralData() {
    // comment empty
  }

  @ApiModelProperty(value = "一致比例（包含允许流程），后台已经乘以100")
  private String conformanceRate;

  @ApiModelProperty(value = "一致案例（包含允许流程）")
  private String conformanceCaseNum;

  @ApiModelProperty(value = "不一致案例")
  private String unConformanceCaseNum;

  @ApiModelProperty(value = "允许流程")
  private String allowVariantNum;

  @ApiModelProperty(value = "不允许流程")
  private String unAllowVariantNum;
}
