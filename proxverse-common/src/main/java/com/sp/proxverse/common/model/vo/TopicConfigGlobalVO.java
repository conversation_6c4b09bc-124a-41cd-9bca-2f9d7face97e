package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("全局过滤组件VO对象")
public class TopicConfigGlobalVO {

  @Tolerate
  public TopicConfigGlobalVO() {
    // comment empty
  }

  private Integer id;

  private Integer topicId;

  private Integer tenantId;

  private Integer deleted;

  private String description;

  private String eventColumn;

  private String format;

  private String formatting;

  private String extension;

  private Integer prikeyId;

  private String startTimeValue;

  private String endTimeValue;

  private String param2;

  private String value2;

  private String orgValue;

  private String channelValue;
}
