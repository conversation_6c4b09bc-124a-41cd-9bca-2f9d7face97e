package com.sp.proxverse.common.action.enums;

/**
 * <AUTHOR>
 * @create 2022-06-01 12:22 下午
 */
public enum FilterRuleEnum {
  /** 过滤规则枚举 */

  /** 等于（ = ） */
  EQUAL("=", true, true, true, "等于"),

  /** 不等于 */
  NOT_EQUAL("!=", true, true, true, "不等于"),

  /** 大于（ > ） */
  MORE_THAN(">", true, false, true, "大于"),

  /** 大于（ >= ） */
  MORE_THAN_EQUAL(">=", true, false, false, "大于等于"),

  /** 小于（ < ） */
  LESS_THAN("<", true, false, true, "小于"),

  /** 小于等于（ <= ） */
  LESS_THAN_EQUAL("<=", true, false, false, "小于等于"),
  ;

  private String symbol;
  private Boolean num;
  private Boolean string;
  private Boolean time;
  private String describe;

  FilterRuleEnum(String symbol, Boolean num, Boolean string, Boolean time, String describe) {
    this.symbol = symbol;
    this.num = num;
    this.string = string;
    this.time = time;
    this.describe = describe;
  }

  public String getSymbol() {
    return symbol;
  }

  public Boolean getNum() {
    return num;
  }

  public Boolean getString() {
    return string;
  }

  public Boolean getTime() {
    return time;
  }

  public String getDescribe() {
    return describe;
  }
}
