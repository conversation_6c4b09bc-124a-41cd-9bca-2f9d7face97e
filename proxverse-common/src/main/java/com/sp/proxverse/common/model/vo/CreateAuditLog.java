package com.sp.proxverse.common.model.vo;

import com.alibaba.fastjson.JSON;
import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/29 13:30
 */
@Data
public class CreateAuditLog {

  /** 接口地址 */
  private String path;

  /** 用户名称 */
  private String userName;

  /** 用户Id */
  private Integer userId;

  /** IP地址 */
  private String ip;

  private Integer tenantId;

  private Map<String, Object> parameters = new HashMap<>();

  public CreateAuditLog addParameters(String attrName, Object attrValue) {
    parameters.put(attrName, attrValue);
    return this;
  }

  public String getParameterStr() {
    if (parameters.isEmpty()) {
      return "";
    }
    StringBuilder parameter = new StringBuilder();
    for (Map.Entry<String, Object> stringObjectEntry : parameters.entrySet()) {
      parameter.append(stringObjectEntry.getKey());
      parameter.append(" : ");
      if (stringObjectEntry.getValue() == null) {
        parameter.append("null");
      } else {
        Object value = stringObjectEntry.getValue();
        parameter.append(objToString(value));
      }
      parameter.append("|");
    }
    parameter.deleteCharAt(parameter.length() - 1);
    if (parameter.length() <= 4000) {
      return parameter.toString();
    } else {
      return parameter.substring(0, 4000);
    }
  }

  public static Object objToString(Object obj) {
    if (obj instanceof String) {
      return obj;
    }
    if (isWrapperType(obj)) {
      return obj.toString();
    }
    if (obj instanceof Collection
        || obj instanceof Array
        || obj instanceof Map
        || obj instanceof Enum
        || obj instanceof Date) {
      return JSON.toJSONString(obj);
    }
    return obj.toString();
  }

  public static boolean isWrapperType(Object obj) {
    return obj instanceof Integer
        || obj instanceof Long
        || obj instanceof Float
        || obj instanceof Double
        || obj instanceof Boolean
        || obj instanceof Byte
        || obj instanceof Short
        || obj instanceof Character;
  }
}
