package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("case数据字段详情（案例视图outputVO对象字段）")
public class TableFieldDTO {

  @Tolerate
  public TableFieldDTO() {
    // comment empty
  }

  // 这里要看下前端怎么展示带有父子关系的字段

  @ApiModelProperty(value = "列名称")
  private String field;

  @ApiModelProperty(value = "列名称别名")
  private String fieldAlias;

  /** @see DataTypeEnum */
  @ApiModelProperty(value = "列字段类型")
  private Integer fieldType;

  @ApiModelProperty(value = "是否已经被选中（0：否，1：是）如果是编辑时的话要回显之前选中的字段")
  private Integer hasSelected;

  @ApiModelProperty(value = "是否是主键（0：否，1：是）")
  private Integer hasPriField;

  @ApiModelProperty(value = "是否是增量时间列（0：否，1：是）")
  private Integer hasUpdateTimeField;

  @ApiModelProperty(value = "表达式")
  private String expression;
}
