package com.sp.proxverse.common.model.dict;

public enum BusinessTopicTypeEnum {

  /** business类型 */
  OVERALL(100, "全景图"),
  OVERALL_BUSINESS(101, "业务全景图"),
  BUSINESS_TOPIC(200, "业务主题"),
  BUSINESS_TREE(201, "业务分析图谱"),
  BUSINESS_KNOWLEDGE(202, "业务知识模型"),
  FLOW_ANALYSIS(203, "流程分析"),
  ACTION_FLOW(204, "执行流程"),
  SIMULATION(205, "流程仿真"),
  PROGRAMME(206, "流程仿真方案"),
  ;

  private final Integer value;
  private final String name;

  BusinessTopicTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
