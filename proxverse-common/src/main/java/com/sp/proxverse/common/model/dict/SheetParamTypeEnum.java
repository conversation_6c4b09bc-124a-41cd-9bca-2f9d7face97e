package com.sp.proxverse.common.model.dict;

import java.util.Objects;

public enum SheetParamTypeEnum {
  CALC_TIME_UNIT(10, "吞吐时间-瓶颈分析-时间单位"),
  CASE_EXPLORE(20, "案例探索"),
  PROCESS_OVERVIEW_CALC_TIME_UNIT(11, "流程概览-瓶颈分析-时间单位"),
  PROCESS_OVERVIEW_TIME_SCOPE(30, "流程概览-时间过滤"),
  PROCESS_OVERVIEW_REWORK(40, "流程概览-返工"),
  CONFORMANCE_TIME_FILTER(50, "一致性页面时间过滤");

  private final Integer value;
  private final String name;

  SheetParamTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    SheetParamTypeEnum[] values = SheetParamTypeEnum.values();
    for (SheetParamTypeEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value.getName();
      }
    }
    return "";
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
