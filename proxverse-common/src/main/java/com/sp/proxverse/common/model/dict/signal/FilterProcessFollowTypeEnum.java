package com.sp.proxverse.common.model.dict.signal;

public enum FilterProcessFollowTypeEnum {
  PROCESS_FOLLOW_DIRECT(10, "直接跟随"),
  PROCESS_FOLLOW(20, "跟随"),
  PROCESS_FOLLOW_INDIRECT(30, "不直接跟随"),
  PROCESS_FOLLOW_NON(40, "不跟随");

  private final Integer value;
  private final String name;

  FilterProcessFollowTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
