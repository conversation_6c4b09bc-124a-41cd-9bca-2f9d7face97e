package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-20 18:32
 */
@Data
@ApiModel("创建或者更新流程图详细Kpi")
public class UpdateTopicProcessDetailsKpiReqVo {

  @ApiModelProperty("KPIId，如果为空则为创建")
  private Integer kpiId;

  @ApiModelProperty("KPI和流程图KPI关系Id，如果为空则为创建")
  private Integer processTreeKpiRelationId;

  @NotNull(message = "{400006}")
  @ApiModelProperty("流程图KpiId")
  private Integer processTreeKpiId;

  @ApiModelProperty("KPI公式")
  private String expression;

  private String kpiName;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("类型 0：事件KPI， 1，连线KPI,2. 路径KPI")
  private Integer type;

  private String formatting;

  private String format;
}
