package com.sp.proxverse.common.model.dict.signal;

public enum ExecutionActionTypeEnum {
  MANUAL(1, "手动"),
  AUTOMATE(2, "自动"),
  ;

  private final Integer value;
  private final String name;

  ExecutionActionTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
