package com.sp.proxverse.common.model.listener.service;

import com.sp.proxverse.common.model.listener.RefreshLicenseEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class LicensePublishService {
  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  @Async
  public void publishLicenseActiveEvent() {
    applicationEventPublisher.publishEvent(new RefreshLicenseEvent(1));
  }
}
