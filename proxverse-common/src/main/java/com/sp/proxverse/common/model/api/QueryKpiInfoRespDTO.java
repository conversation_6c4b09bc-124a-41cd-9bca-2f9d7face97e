package com.sp.proxverse.common.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
@ApiModel("api查询kpi信息返回DTO")
public class QueryKpiInfoRespDTO {

  @Tolerate
  public QueryKpiInfoRespDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "kpi关联Id")
  private Integer kpiRelationId;

  @ApiModelProperty(value = "kpiId")
  private Integer kpiId;

  @ApiModelProperty(value = "kpi名称")
  private String name;

  @ApiModelProperty(value = "单位")
  private String unit;

  @ApiModelProperty(value = "更新时间")
  private String updateTime;

  @ApiModelProperty(value = "计算值")
  private BigDecimal value;

  @ApiModelProperty(value = "目标值")
  private BigDecimal target;
}
