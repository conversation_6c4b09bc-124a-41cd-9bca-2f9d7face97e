package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("设置字段类型返回VO对象")
public class SetFieldOutputVO {
  @Tolerate
  public SetFieldOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "表ID")
  private Integer fileId;

  @ApiModelProperty(value = "表名称")
  private String fileName;
}
