package com.sp.proxverse.common.common;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class AllowedValuesValidator implements ConstraintValidator<AllowedValues, String> {

  private Set<String> values = new HashSet<>();

  @Override
  public void initialize(AllowedValues constraintAnnotation) {
    ConstraintValidator.super.initialize(constraintAnnotation);
    this.values.addAll(Arrays.asList(constraintAnnotation.values()));
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    return value != null && values.contains(value);
  }
}
