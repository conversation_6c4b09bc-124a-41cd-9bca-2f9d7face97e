package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("case数据字段详情（案例视图outputVO对象字段）")
public class TopicDataDTO {

  @Tolerate
  public TopicDataDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据源ID")
  private Integer topicId;

  @ApiModelProperty(value = "主题名称")
  private String topicName;

  @ApiModelProperty(value = "数据源ID")
  private Integer dataId;
}
