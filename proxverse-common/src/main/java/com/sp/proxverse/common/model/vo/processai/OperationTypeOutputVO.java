package com.sp.proxverse.common.model.vo.processai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("运算符号类型VO对象")
public class OperationTypeOutputVO {
  @Tolerate
  public OperationTypeOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据类型枚举值")
  private Integer operationType;

  @ApiModelProperty(value = "数据类型描述")
  private String desc;
}
