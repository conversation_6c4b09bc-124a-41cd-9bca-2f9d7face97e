package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.dict.NewsheetTemplateParamTypeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class NewsheetTemplateParameterInfo {

  @Tolerate
  public NewsheetTemplateParameterInfo() {
    // comment empty
  }

  private String originRefer;

  private String originReferExpression;

  private String originExpression;

  private String parameter;

  private String introduction;

  private String configExpression;

  /** @see NewsheetTemplateParamTypeEnum */
  private Integer type;

  private String kpiParam;

  private String formatting;

  private String format;

  private String columnType;
}
