package com.sp.proxverse.common.model.dict;

import java.util.Objects;

public enum PermissionQueryTypeEnum {
  ALL(0, "全部"),
  USER(1, "用户"),
  USER_GROUP(2, "用户组"),
  ;

  private final Integer value;
  private final String name;

  public static boolean queryAll(Integer type) {
    return Objects.equals(ALL.value, type);
  }

  public static boolean queryUser(Integer type) {
    return Objects.equals(USER.value, type);
  }

  public static boolean queryGroup(Integer type) {
    return Objects.equals(USER_GROUP.value, type);
  }

  PermissionQueryTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
