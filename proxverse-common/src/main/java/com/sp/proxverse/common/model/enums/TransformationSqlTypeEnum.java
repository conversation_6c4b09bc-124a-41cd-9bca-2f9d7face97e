package com.sp.proxverse.common.model.enums;

import java.util.Objects;

/**
 * 数据转换sql类型
 *
 * <AUTHOR>
 * @create 2022-10-21 17:27
 */
public enum TransformationSqlTypeEnum {
  /** 插入 */
  INSERT,
  SELECT,
  CREATE,
  DROP_TABLE,
  SET,
  FOR_LOOP,
  UNKNOWN,
  ;

  public Boolean isUpdateTableSize() {
    return this.equals(TransformationSqlTypeEnum.INSERT)
        || this.equals(TransformationSqlTypeEnum.CREATE);
  }

  public static TransformationSqlTypeEnum stringToEnum(String name) {
    for (TransformationSqlTypeEnum value : TransformationSqlTypeEnum.values()) {
      if (Objects.equals(value.name(), name)) {
        return value;
      }
    }
    return TransformationSqlTypeEnum.UNKNOWN;
  }
}
