package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("变体对象DTO")
public class VariantDTO implements Serializable {

  private static final long serialVersionUID = 4401847303241767644L;

  @Tolerate
  public VariantDTO() {
    // comment empty
  }

  @ApiModelProperty("变体名称")
  private String variant;

  @ApiModelProperty("此变体数量")
  private Integer count;

  @ApiModelProperty("此变体数量所占百分比")
  private BigDecimal rate;

  @ApiModelProperty(value = "变体ID", hidden = true)
  private String variantId;

  public String getVariant() {
    return variant;
  }

  public Integer getCount() {
    return count;
  }

  public BigDecimal getRate() {
    return rate;
  }
}
