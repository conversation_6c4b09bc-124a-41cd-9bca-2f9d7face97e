package com.sp.proxverse.common.model.vo.conformance.request;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询非一致流程列表请求对象")
public class QueryUnConformanceListRequest extends PageRequest {

  @ApiModelProperty(value = "表格sheetID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private Integer topicId;

  @ApiModelProperty(value = "开始时间", hidden = true)
  private String startTime;

  @ApiModelProperty(value = "结束时间", hidden = true)
  private String endTime;
}
