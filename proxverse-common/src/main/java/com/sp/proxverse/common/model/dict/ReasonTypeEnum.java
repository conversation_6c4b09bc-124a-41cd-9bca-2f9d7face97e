package com.sp.proxverse.common.model.dict;

import java.util.Objects;

public enum ReasonTypeEnum {
  PETRINET_REASON(1, "petri net 不一致原因"),
  CONDITION_REASON(2, "condition 不一致原因"),
  ;

  private final Integer value;
  private final String name;

  ReasonTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static boolean isConditionReason(Integer value) {
    return Objects.equals(CONDITION_REASON.value, value);
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
