package com.sp.proxverse.common.model.vo.conformance.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询一致性流程的kpi列表(概览页面)请求对象")
public class QueryConformanceKpi4GeneralRequest {

  @ApiModelProperty(value = "表格sheetID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private Integer topicId;

  @ApiModelProperty(value = "开始时间")
  private String startTime;

  @ApiModelProperty(value = "结束时间")
  private String endTime;
}
