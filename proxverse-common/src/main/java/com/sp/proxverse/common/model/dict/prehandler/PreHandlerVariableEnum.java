package com.sp.proxverse.common.model.dict.prehandler;

import org.apache.spark.common.model.DataTypeEnum;

/** 主表预处理字段 */
public enum PreHandlerVariableEnum {
  CASE_ID("preCaseId", "CaseID", DataTypeEnum.STR.getValue()),
  VARIANT_ID("variantId", "变体ID", DataTypeEnum.INT.getValue()),
  CASE_DURATION("caseDuration", "case时长", DataTypeEnum.INT.getValue()),
  EVENT_NUM("eventNum", "事件数量", DataTypeEnum.INT.getValue()),
  CASE_TIME("caseTime", "case时间", DataTypeEnum.DATE_TIME.getValue()),
  START_EVENT("startEvent", "开始事件", DataTypeEnum.STR.getValue()),
  END_EVENT("endEvent", "结束事件", DataTypeEnum.STR.getValue()),
  REWORK_EVENT_NUM("reworkEventNum", "返工事件数", DataTypeEnum.INT.getValue()),
  START_TIME("startTime", "case起始时间", DataTypeEnum.DATE_TIME.getValue()),
  SORTED_EVENT_LIST("sortedEventList", "排序好的 event list", DataTypeEnum.STR.getValue()),
  SORTED_TS_LIST("sortedTSList", "排序好的 timestamp list", DataTypeEnum.DATE_TIME.getValue()),
  PRE_CASE_ID("pre_caseId", "复制一份caseId用来过滤", DataTypeEnum.STR.getValue()),
  PRE_TIME("pre_time", "复制一份time用来过滤", DataTypeEnum.DATE_TIME.getValue()),
  VARIANT("variant", "变体", DataTypeEnum.STR.getValue()),
  ;

  private final String value;
  private final String name;
  private final Integer type;

  PreHandlerVariableEnum(String value, String name, Integer type) {
    this.value = value;
    this.name = name;
    this.type = type;
  }

  public String getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  public Integer getType() {
    return type;
  }
}
