package com.sp.proxverse.common.action.bo;

import com.sp.proxverse.common.action.enums.FilterRelationEnum;
import com.sp.proxverse.common.action.enums.FilterRuleEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 过滤参数
 *
 * <AUTHOR>
 * @create 2022-06-01 10:45 上午
 */
@Data
@ApiModel("过滤参数")
public class FilterParamBo {

  @ApiModelProperty("过滤枚举")
  private FilterRuleEnum filterRuleEnum;

  @ApiModelProperty("第一个参数")
  private String paramFirst;

  @ApiModelProperty("后面一个参数")
  private String paramLast;

  @ApiModelProperty("过滤条件之间的关联关系")
  private FilterRelationEnum relation;
}
