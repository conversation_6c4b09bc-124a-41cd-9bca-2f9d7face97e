package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询在业务分析里创建KPI是否能选用知识模型里的kpi请求对象")
public class QueryHasKnowledgeKpiRequest {

  @ApiModelProperty(value = "当前所在主题ID的父主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;
}
