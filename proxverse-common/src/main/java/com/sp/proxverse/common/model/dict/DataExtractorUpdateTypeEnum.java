package com.sp.proxverse.common.model.dict;

/** 数据提取的更新类型 */
public enum DataExtractorUpdateTypeEnum {
  INCREMENT(1, "增量更新"),
  FULL(2, "全量更新"),
  ;

  private final Integer value;
  private final String name;

  DataExtractorUpdateTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
