package com.sp.proxverse.common.model.vo.fileUpload;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-07 1:50 下午
 */
@Data
@ApiModel("文件合并响应")
public class FileMergeResVo {

  @ApiModelProperty("成功标识")
  private boolean state;

  @ApiModelProperty("文件路径")
  private String filePath;

  @ApiModelProperty("文件名称")
  private String fileName;

  @ApiModelProperty("文件大小")
  private Long fileSize;

  private String encoding;
}
