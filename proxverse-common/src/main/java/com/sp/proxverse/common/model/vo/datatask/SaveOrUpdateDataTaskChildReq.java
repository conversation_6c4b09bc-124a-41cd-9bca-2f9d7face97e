package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-11 10:37
 */
@Data
@ApiModel("创建或者保存数据任务请求")
public class SaveOrUpdateDataTaskChildReq {
  @NotNull(message = "{400006}")
  private Integer poolId;

  @NotNull(message = "{400006}")
  private Integer dataTaskId;

  @ApiModelProperty("数据任务孩子Id，为空时新建")
  private Integer dataTaskChildId;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("数据任务孩子类型，0，数据提取，1，数据转换")
  @NotNull(message = "{400008}")
  private Integer childType;

  @ApiModelProperty("数据提取状态， 0运行，1，暂停")
  @NotNull(message = "{400009}")
  private Integer state;

  @ApiModelProperty("当childType 类型为 数据提取时传递此参数")
  private Integer dataExtractorID;
}
