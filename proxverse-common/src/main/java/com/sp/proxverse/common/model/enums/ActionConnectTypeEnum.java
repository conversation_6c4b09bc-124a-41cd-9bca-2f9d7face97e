package com.sp.proxverse.common.model.enums;

/**
 * 执行动作连接类型
 *
 * <AUTHOR>
 * @create 2022-06-08 3:20 下午
 */
public enum ActionConnectTypeEnum {
  /** 执行动作连接类型 */
  EMAIL("邮件(SMTP)", 1000, "邮件发送连接"),
  WECHAT("企业微信", 2000, "企业微信发送消息"),
  SMS("短信", 3000, "短信发送消息");

  public String actionConnectName;

  public String describe;

  public Integer code;

  ActionConnectTypeEnum(String actionConnectName, Integer code, String describe) {
    this.actionConnectName = actionConnectName;
    this.describe = describe;
    this.code = code;
  }

  public static ActionConnectTypeEnum getActionConnectTypeEnum(Integer code) {
    for (ActionConnectTypeEnum value : ActionConnectTypeEnum.values()) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return null;
  }

  public String getActionConnectName() {
    return actionConnectName;
  }

  public String getDescribe() {
    return describe;
  }

  public Integer getCode() {
    return code;
  }
}
