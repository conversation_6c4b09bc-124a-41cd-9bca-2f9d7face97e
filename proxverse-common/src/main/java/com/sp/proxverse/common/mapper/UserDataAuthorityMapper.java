package com.sp.proxverse.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.dto.UserDataAuthorityInfoDto;
import com.sp.proxverse.common.model.dto.dataauth.UserDataAuthDTO;
import com.sp.proxverse.common.model.dto.domain.UserDataAuthority;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户数据权限 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Mapper
@DS("oauth2")
public interface UserDataAuthorityMapper extends BaseMapper<UserDataAuthority> {

  /**
   * 条件查询用户数据权限信息
   *
   * @param dataAuthorityId
   * @param userId
   * @return
   */
  List<UserDataAuthorityInfoDto> selectListDataAuthority(
      @Param("dataAuthorityId") String dataAuthorityId, @Param("userId") Integer userId);

  /**
   * 条件查询用户数据权限信息分页
   *
   * @param dataAuthorityId
   * @param userId
   * @param startNum
   * @param pageSize
   * @return
   */
  List<UserDataAuthorityInfoDto> selectListDataAuthorityPage(
      @Param("dataAuthorityId") String dataAuthorityId,
      @Param("userId") Integer userId,
      @Param("startNum") Long startNum,
      @Param("pageSize") Long pageSize);

  /**
   * 条件查询用户数据权限信息数量
   *
   * @param dataAuthorityId
   * @param userId
   * @return
   */
  Long selectListDataAuthorityCount(
      @Param("dataAuthorityId") String dataAuthorityId, @Param("userId") Integer userId);

  String getUserTopicFilter(@Param("userId") Integer userId, @Param("dataId") Integer dataId);

  List<UserDataAuthDTO> selectUserPage(
      @Param("id") Integer id,
      @Param("name") String name,
      @Param("states") List<Integer> states,
      @Param("authorityId") Integer authorityId,
      @Param("startNum") Integer startNum,
      @Param("pageSize") Integer pageSize);

  List<UserDataAuthDTO> selectGroupPage(
      @Param("name") String name,
      @Param("authorityId") Integer authorityId,
      @Param("startNum") Integer startNum,
      @Param("pageSize") Integer pageSize);

  int selectUserCount(@Param("name") String name);

  int selectGroupCount(@Param("name") String name);
}
