package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
public class TopicColorTemplateResponse {
  @Tolerate
  public TopicColorTemplateResponse() {
    // comment empty
  }

  private String colorTemplate;

  @ApiModelProperty("1：使用中，0：未设置")
  private Integer active;
}
