package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class KpiResultDTO implements Serializable {

  private static final long serialVersionUID = 5309935201134816729L;

  @Tolerate
  public KpiResultDTO() {
    // comment empty
  }

  private Integer kpiId;

  @ApiModelProperty(value = "变量值")
  private String variable;

  @ApiModelProperty(value = "单位")
  private String unit;

  @ApiModelProperty(value = "此变量值对应的kpi计算值")
  private BigDecimal value;

  @ApiModelProperty(value = "此变量值对应的kpi计算值")
  private String valueStr;
}
