package com.sp.proxverse.common.model.vo.processai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询变量的值列表（事件过滤）VO对象")
public class VariableValueListOutputVO {
  @Tolerate
  public VariableValueListOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "变量值")
  private String value;

  @ApiModelProperty(value = "案例数量")
  private Integer caseNum;

  @ApiModelProperty(value = "是否取把，0：否，1：是")
  private Integer invert;
}
