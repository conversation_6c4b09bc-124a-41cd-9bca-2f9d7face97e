package com.sp.proxverse.common.model.vo.datadraw;

import com.sp.proxverse.common.model.dto.TableFieldDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据提取字段列表回显VO对象")
public class EditDataDrawFieldOutputVO {
  @Tolerate
  public EditDataDrawFieldOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "表名")
  private String tableName;

  @ApiModelProperty(value = "where规则")
  private String whereRule;

  private Integer updateType;

  @ApiModelProperty(value = "字段列表")
  private List<TableFieldDTO> fieldDTOList;
}
