package com.sp.proxverse.common.exception;

public enum ErrorCode implements BaseErrorCode {

  // 成功 2000 开头
  SUCCESS(2000, "Success"),

  // 业务相关 3000 开头
  BUSINESS_ERROR(3000, "Business exception"),

  TOKEN_EXPIRE(3001, "Token expire"),

  OPERATION_NOT_ALLOWED(3003, "不允许删除，该kpi绑定了报表"),

  ACTION_RUN_ERROR(3004, "执行动作运行错误"),

  ACTION_START_PARAM_ERROR(3005, "执行启动动作参数错误"),

  ACTION_NODE_PARAM_ERROR(3006, "执行动作节点信息不完善"),

  VERSIFY_ERROR(3007, "Code error"),
  COMPANY_EXPIRE(3101, "公司过期"),

  SA_TOKEN_SERIALIZE_ERROR(3200, "SaToken序列化异常"),

  SA_TOKEN_INIT_REFRESH_THREAD(3201, "SaToken异步刷新异常"),

  // 系统错误 5000 开头
  ERROR_SYSTEM(5000, "系统错误"),

  ERROR_PARAM(5001, "Parameter error"),

  ERROR_DATA(5002, "Data error"),

  ERROR_PERMISSION(5003, "Permission denied"),

  SYSTEM_ERROR(101001, "System error"),

  DATA_ERROR(200006, "Data error"),

  // License相关 6000 开头
  LICENSE_ERROR(6000, "许可证异常"),

  NO_VALID_LICENSE(6001, "没有激活的许可证"),

  LICENSE_NOT_VALID(6002, "许可证不合法"),

  LICENSE_EXPIRE(6003, "许可证已过期"),

  LICENSE_NOT_FOUND(6004, "未找到许可证文件"),

  CONSUMER_AMOUNT_OVER_LIMIT(6005, "用户授权数已达上限"),

  LICENSE_TOKEN_EXPIRE(10000, "LICENSE失效,请重新上传LICENSE");

  ErrorCode(Integer code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  private final Integer code;
  private final String desc;

  @Override
  public Integer getCode() {
    return code;
  }

  @Override
  public String getDesc() {
    return desc;
  }
}
