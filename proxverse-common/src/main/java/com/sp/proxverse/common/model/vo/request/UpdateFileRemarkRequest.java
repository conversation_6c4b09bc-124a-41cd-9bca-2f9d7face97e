package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-07-18
 * @description 更新文件备注请求对象
 */
@Data
@ApiModel("更新文件备注请求对象")
public class UpdateFileRemarkRequest {

  @ApiModelProperty(value = "文件ID", required = true)
  @NotNull(message = "{400006}")
  private Integer fileId;

  @ApiModelProperty(value = "文件中文备注")
  @Size(max = 10, message = "{中文备注长度不超过10}")
  private String remark;
}
