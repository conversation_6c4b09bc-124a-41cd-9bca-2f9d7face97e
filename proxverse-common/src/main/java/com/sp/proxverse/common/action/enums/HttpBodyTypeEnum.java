package com.sp.proxverse.common.action.enums;

/**
 * <AUTHOR>
 * @create 2022-05-09 4:09 下午
 */
public enum HttpBodyTypeEnum {
  /** http body类型 */
  NONE("none", "none"),
  FORM_DATA("form_data", "formData"),
  APPLICATION("application/x-www-form-urlencoded", "application"),
  RAW("raw", "raw"),
  ;
  public String displayName;
  public String value;

  HttpBodyTypeEnum(String value, String displayName) {
    this.displayName = displayName;
    this.value = value;
  }

  public enum ParamTypeEnum {
    /** 请求类型参数 */
    TEXT(RAW, "Text", "text/plain"),
    JAVASCRIPT(RAW, "JavaScript", "application/javascript"),
    JSON(RAW, "Json", "application/json; charset=utf-8"),
    HTML(RAW, "HTML", "text/html"),
    XML(RAW, "XML", "text/xml"),
    APPLICATION_XML(RAW, "application_xml", "application/xml"),
    ;

    public HttpBodyTypeEnum parentType;
    public String paramTypeName;
    public String mediaType;

    ParamTypeEnum(HttpBodyTypeEnum parentType, String paramTypeName, String mediaType) {
      this.paramTypeName = paramTypeName;
      this.parentType = parentType;
      this.mediaType = mediaType;
    }
  }
}
