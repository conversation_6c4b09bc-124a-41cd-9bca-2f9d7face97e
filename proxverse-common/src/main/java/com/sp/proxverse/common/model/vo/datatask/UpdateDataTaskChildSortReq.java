package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-11 11:07
 */
@Data
@ApiModel("调整数据任务孩子顺序")
public class UpdateDataTaskChildSortReq {

  @NotNull(message = "{400009}")
  @ApiModelProperty("调整类型，0 上移，1，下移，2，指定孩子互换")
  private Integer type;

  @NotNull(message = "{400006}")
  @ApiModelProperty("数据任务孩子Id")
  private Integer baseDataTaskChildId;

  @ApiModelProperty("互换的数据任务孩子Id，如果type为2，则不能为空")
  private Integer swapDataTaskChildId;
}
