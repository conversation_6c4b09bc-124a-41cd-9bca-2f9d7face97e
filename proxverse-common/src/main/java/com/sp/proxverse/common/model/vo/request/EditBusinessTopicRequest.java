package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("编辑业务主题请求实体类")
public class EditBusinessTopicRequest {

  @ApiModelProperty(value = "业务主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty(value = "业务主题名称")
  private String name;

  @ApiModelProperty(value = "选择的数据源类型，目前只有在创建业务分析图谱时需要传值（1：data_model，2：knowledge_model）")
  private Integer dataSourceType;

  @ApiModelProperty(value = "选择的数据源ID，目前最多有一个元素")
  private List<Integer> dataId;
}
