package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class SheetKpiDTO implements Serializable {

  @Tolerate
  public SheetKpiDTO() {
    // comment empty
  }

  /** KpiId */
  private Integer id;

  /** sheetKpiId */
  private Integer sheetKpiId;

  @ApiModelProperty("kpi名称")
  private String name;

  @ApiModelProperty("kpi单位，1:天、2:时")
  private String unit;

  @ApiModelProperty("1:越大越优，2：越小越优")
  private Integer type;

  @ApiModelProperty("基线值")
  private String baseLine;

  @ApiModelProperty("公式")
  private String expression;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("是否来自于知识模型（0：否，1：是）")
  private Integer fromKnowledge;

  @ApiModelProperty("1:初始创建，不能删除，0：可以删除")
  private Integer initType;

  private String formatting;

  private String format;

  private String columnType;
}
