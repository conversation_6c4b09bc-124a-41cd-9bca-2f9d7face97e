package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-27 16:52
 */
@Data
@ApiModel("获取运行流程仿真结果")
public class GetRunSimulationResultReqVo {

  @ApiModelProperty("方案Id")
  private Integer programmeId;

  @ApiModelProperty("对比标识")
  private boolean contrastFlag;

  @ApiModelProperty("仿真Id")
  @NotNull(message = "{400006}")
  private Integer simulationId;
}
