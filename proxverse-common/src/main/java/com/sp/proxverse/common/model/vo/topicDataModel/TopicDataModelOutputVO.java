package com.sp.proxverse.common.model.vo.topicDataModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("业务主题数据模型列表VO对象")
public class TopicDataModelOutputVO {
  @Tolerate
  public TopicDataModelOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "此条数据在数据库中的主键ID，调删除接口时传入此ID")
  private Integer dataId;

  @ApiModelProperty(value = "数据模型ID")
  private Integer dataModelId;

  @ApiModelProperty(value = "主题ID")
  private Integer topicId;

  @ApiModelProperty(value = "数据模型名称")
  private String name;

  @ApiModelProperty(value = "数据池ID")
  private Integer poolId;

  @ApiModelProperty(value = "数据池名称")
  private String poolName;

  @ApiModelProperty(value = "该数据源使用中的业务主题")
  private String using;

  @ApiModelProperty(value = "时间")
  private String createTimeZh;
}
