package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询知识模型父列表请求对象")
public class QueryKnowledgeSourceRequest {

  @ApiModelProperty(value = "当前所在主题ID的父主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;
}
