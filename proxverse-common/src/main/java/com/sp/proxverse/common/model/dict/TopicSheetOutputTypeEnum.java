package com.sp.proxverse.common.model.dict;

public enum TopicSheetOutputTypeEnum {
  CASE_EXP(3, "案例探索"),
  KPI_ANALYSIS(4, "KPI分析"),
  ROUTING(5, "路径探索"),
  CONSISTENCY_CHECK(6, "一致性检查"),
  REWORK_EXP(7, "返工探索"),
  NEW_PAGE(8, "新页面"),
  THROUGHPUT_EXP(9, "吞吐时间探索"),
  PROCESS_COMPARISON(10, "流程对比"),
  PROCESS_EXP(11, "流程探索"),
  NEW_PAGE_AI(12, "新页面-流程AI助手"),
  PROCESS_OVERVIEW(13, "流程概览"),
  ROOT_CAUSE_ANALYSIS(14, "根因分析"),
  ;

  private final Integer value;
  private final String name;

  TopicSheetOutputTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
