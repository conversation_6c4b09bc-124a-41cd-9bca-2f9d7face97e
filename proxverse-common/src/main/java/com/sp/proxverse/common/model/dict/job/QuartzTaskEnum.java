package com.sp.proxverse.common.model.dict.job;

import lombok.Getter;

public enum QuartzTaskEnum {
  PUSH_ACTION_FLOW_TASK(
      "signalRuleBizService",
      "pushActionFlow",
      "com.sp.proxverse.job.quartz.task.PushActionFlowJob",
      "",
      "增量数据推送信号到动作流程"),
  ;

  @Getter private final String beanName;
  @Getter private final String methodName;
  @Getter private final String jobClassName;
  @Getter private final String cron;
  @Getter private final String desc;

  QuartzTaskEnum(String value, String name, String jobClassName, String cron, String desc) {
    this.beanName = value;
    this.methodName = name;
    this.jobClassName = jobClassName;
    this.cron = cron;
    this.desc = desc;
  }

  public static String buildParameter(QuartzTaskEnum taskEnum) {
    return taskEnum.getBeanName() + "." + taskEnum.getMethodName();
  }
}
