package com.sp.proxverse.common.model.dto.result;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Builder
@Data
public class Result implements Serializable {

  @Tolerate
  public Result() {
    // comment empty
  }

  private String[] tables;

  private StructFieldInfo[] metadata;

  private String[][] data;

  private String[] unit;

  private Long overallCount;

  private Long offset;

  private Long count;

  private String error;

  public String getSingle() {
    if (this.getData() == null || this.getData().length == 0) {
      return null;
    }
    String[] dataSub = this.getData()[0];
    if (dataSub == null || dataSub.length == 0) {
      return null;
    }
    return this.getData()[0][0];
  }

  public String[] getVerticalData() {
    if (this.getData() == null || this.getData().length == 0) {
      return null;
    }
    return this.getData()[0];
  }

  public <T> List<T> getList(Class<T> t)
      throws IllegalAccessException, InstantiationException, InvocationTargetException,
          NoSuchMethodException {
    if (this.getData() == null || this.getData().length == 0) {
      return new ArrayList<>();
    }
    List<T> list = new ArrayList<>();
    Map<String, Field> map = new HashMap<>();
    Constructor<T>[] constructors = (Constructor<T>[]) t.getConstructors();

    Optional<Constructor<T>> constructorOptional =
        Stream.of(constructors).filter(f -> f.getParameterCount() == 0).findFirst();
    if (constructorOptional.isPresent()) {
      Constructor<T> constructor = constructorOptional.get();
      List<String> absentList = new ArrayList<>();
      for (String[] arr : this.getData()) {
        T instance = constructor.newInstance();
        try {
          for (int i = 0; i < this.getMetadata().length; i++) {
            String columnName = this.getMetadata()[i].getColumnName();
            if (absentList.contains(columnName)) {
              continue;
            }
            Field field = map.get(columnName);
            if (field == null) {
              field = t.getDeclaredField(columnName);
              if (field == null) {
                absentList.add(columnName);
                continue;
              }
              field.setAccessible(true);
              map.put(columnName, field);
            }
            if (!Objects.equals(arr[i], "NULL")) {
              Object value = field.getType().getConstructor(String.class).newInstance(arr[i]);

              field.set(instance, value);
            }
          }
        } catch (Exception e) {
          log.error("newInstance error", e);
          continue;
        }
        list.add(instance);
      }
    }

    return list;
  }
}
