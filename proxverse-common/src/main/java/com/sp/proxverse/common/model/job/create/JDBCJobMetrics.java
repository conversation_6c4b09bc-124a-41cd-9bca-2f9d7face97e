package com.sp.proxverse.common.model.job.create;

import com.sp.proxverse.common.model.bo.Message;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-02-02 17:05
 */
@Data
public class JDBCJobMetrics extends JobMetrics {

  private Message message;

  private Integer fileId;

  private Long parquetFileSize;

  public JDBCJobMetrics() {
    super();
  }

  public Message getMessage() {
    if (this.message == null) {
      return new Message();
    }
    return this.message;
  }
}
