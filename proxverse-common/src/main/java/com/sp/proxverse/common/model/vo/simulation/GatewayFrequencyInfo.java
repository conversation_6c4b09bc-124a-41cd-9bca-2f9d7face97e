package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-21 13:10
 */
@Data
@ApiModel("网关频率信息响应信息")
public class GatewayFrequencyInfo {

  @ApiModelProperty("事件Id")
  private String eventElementId;

  @ApiModelProperty("事件名称")
  private String eventName;

  @ApiModelProperty("事件概率")
  private Double frequency;

  private String expression;

  private String formatting;

  private String format;
}
