package com.sp.proxverse.common.model.vo.conformance.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询一致性流程的kpi列表请求对象")
public class QueryConformanceKpiRequest {

  @ApiModelProperty(value = "表格sheetID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private Integer topicId;

  @ApiModelProperty(value = "变体", required = true)
  private String variant;

  private String reason;

  private Integer reasonType;
}
