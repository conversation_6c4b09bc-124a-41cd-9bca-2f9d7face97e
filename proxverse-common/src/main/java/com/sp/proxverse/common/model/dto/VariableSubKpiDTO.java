package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("相关变量的KPI计算结果DTO")
public class VariableSubKpiDTO {

  @Tolerate
  public VariableSubKpiDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "kpi名称")
  private String kpiName;

  @ApiModelProperty(value = "kpi针对此parameter项所计算出的值")
  private BigDecimal value;

  @ApiModelProperty(value = "kpi单位")
  private String unit;
}
