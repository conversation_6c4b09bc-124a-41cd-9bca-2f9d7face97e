package com.sp.proxverse.common.model.dict;

import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.util.I18nUtil;
import java.util.Objects;

public enum ModelRecordTypeEnum {
  SVAE(100, "100320", "创建"),
  UPDATE(200, "100321", "修改"),
  LOAD(300, "100322", "加载"),
  ;

  private final Integer value;
  private final String code;
  private final String name;

  ModelRecordTypeEnum(Integer value, String code, String name) {
    this.value = value;
    this.code = code;
    this.name = name;
  }

  public static String getDescyType(Integer type) {
    ModelRecordTypeEnum[] values = values();
    for (ModelRecordTypeEnum typeEnum : values) {
      if (Objects.equals(typeEnum.getValue(), type)) {
        return I18nUtil.getMessage(typeEnum.getCode());
      }
    }
    throw new BizException(ErrorCode.ERROR_SYSTEM.getCode(), "type error");
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  public String getCode() {
    return code;
  }
}
