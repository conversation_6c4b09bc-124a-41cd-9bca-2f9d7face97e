package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("保存参数项选中的过滤条件请求对象")
public class SaveVariableParamRequest {

  @ApiModelProperty(value = "当前所在主题表格ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  @ApiModelProperty(value = "变量名", required = true)
  @NotNull(message = "{400007}")
  private String variable;

  @ApiModelProperty(value = "变量值", required = true)
  @NotNull(message = "{400007}")
  private String variableValue;

  @ApiModelProperty(value = "fileId不能为空", required = true)
  @NotNull(message = "{400006}")
  private Integer fileId;
}
