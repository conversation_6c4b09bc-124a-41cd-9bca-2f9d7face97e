package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-22 11:21
 */
@Data
@ApiModel("创建或者更新流程仿真方案")
public class CreateOrUpdateSimulationProgrammeReqVo {

  private Integer id;

  @ApiModelProperty("仿真方案Id")
  @NotNull(message = "{400006}")
  private Integer simulationId;

  @ApiModelProperty("仿真方案名称")
  @NotNull(message = "{400007}")
  private String programmeName;
}
