package com.sp.proxverse.common.model.dict;

public enum BookMarkTypeEnum {
  STANDARD(0, "工作台创建的bookmark发布时需要覆盖删除"),
  CUSTOM(1, "指挥舱创建的bookmark发布时不需要覆盖删除"),
  ;

  private final Integer value;
  private final String name;

  BookMarkTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
