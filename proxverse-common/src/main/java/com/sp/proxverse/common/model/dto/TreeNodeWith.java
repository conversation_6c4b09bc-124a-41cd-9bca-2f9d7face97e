package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class TreeNodeWith {

  @Tolerate
  public TreeNodeWith() {
    // comment empty
  }

  @ApiModelProperty("当前悬浮节点数量")
  private Integer with;

  @ApiModelProperty("当前悬浮节点数量所占比率")
  private BigDecimal withRate;

  @ApiModelProperty("总数量减去with的数量")
  private Integer without;

  @ApiModelProperty("总数量减去with的数量所占比率")
  private BigDecimal withoutRate;

  @ApiModelProperty("以此事件开始的案例数量")
  private Integer start;

  @ApiModelProperty("以此事件开始的案例数量所占比率")
  private BigDecimal startRate;

  @ApiModelProperty("以此事件结束的案例数量")
  private Integer end;

  @ApiModelProperty("以此事件结束的案例数量所占比率")
  private BigDecimal endRate;

  @ApiModelProperty("事件发生率")
  private BigDecimal eventRate;
}
