package com.sp.proxverse.common.model.vo.datamodel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据池数据链接VO对象")
public class QueryDataPoolDetailOutputVO {
  @Tolerate
  public QueryDataPoolDetailOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据ID（请求的是数据链接，则此字段就是数据链接ID，同理也是数据提取ID、数据任务ID，数据模型ID）")
  private Integer dataId;

  @ApiModelProperty(value = "对应名称")
  private String name;

  @ApiModelProperty(value = "如果是数据链接，则此字段有值（1：jdbc，2：kafka）")
  private Integer connectorType;
}
