package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("业务主题表格KPI VO对象")
public class TopicSheetKpiOutputVO {
  @Tolerate
  public TopicSheetKpiOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "当前业务分析图谱这个主题表格的ID")
  private Integer topicSheetId;

  @ApiModelProperty(value = "KPI的ID")
  private Integer kpiId;

  @ApiModelProperty(value = "kpi名称")
  private String name;

  @ApiModelProperty(value = "当前kpi计算出的结果值")
  private String value;

  @ApiModelProperty(value = "当前kpi计算出的结果值")
  private String valueRight;

  @ApiModelProperty(value = "单位")
  private String unit;

  private String originalName;

  private String originalUnit;

  @ApiModelProperty("基线值")
  private String baseLine;

  @ApiModelProperty("公式")
  private String expression;

  @ApiModelProperty("1:越大越优，2：越小越优")
  private Integer kpiType;

  @ApiModelProperty("创建kpi的方式（1：从知识模型中导入，2：自定义）")
  private Integer saveType;

  private String formatting;

  private String format;

  private String columnType;
}
