package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
@ApiModel("数据模型加载列表DTO")
public class DataModelLoadingDTO implements Serializable {

  @Tolerate
  public DataModelLoadingDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "文件ID")
  private Integer fileId;

  @ApiModelProperty(value = "开始时间")
  private String startDateZh;

  @ApiModelProperty(value = "结束时间")
  private String endDateZh;

  @ApiModelProperty(value = "信息（预留字段，没啥用）")
  private String message;

  /** DataModelRunStatusEnum */
  @ApiModelProperty(value = "文件加载状态（0：未完成，1：加载中，2：已完成）")
  private Integer loadStatus;

  private List<String> errorMsgList;
}
