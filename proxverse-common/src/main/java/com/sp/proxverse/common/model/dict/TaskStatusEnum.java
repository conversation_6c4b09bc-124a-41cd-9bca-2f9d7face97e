package com.sp.proxverse.common.model.dict;

import java.util.Objects;
import java.util.stream.Stream;

public enum TaskStatusEnum {
  PAUSE(0, "任务暂停"),
  START(1, "任务开启"),
  ;

  private final Integer value;
  private final String name;

  TaskStatusEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    TaskStatusEnum[] values = TaskStatusEnum.values();
    for (TaskStatusEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {

        return value.getName();
      }
    }
    return "";
  }

  /**
   * 根据文件后缀名得到枚举int值
   *
   * @param f
   * @return
   */
  public static Integer filterFileType(String f) {
    TaskStatusEnum fileTypeEnum =
        Stream.of(values()).filter(e -> Objects.equals(e.getName(), f)).findFirst().orElse(null);
    return Objects.isNull(fileTypeEnum) ? null : fileTypeEnum.getValue();
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
