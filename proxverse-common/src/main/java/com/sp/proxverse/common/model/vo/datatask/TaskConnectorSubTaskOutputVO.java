package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据任务链接子任务列表VO对象")
public class TaskConnectorSubTaskOutputVO {
  @Tolerate
  public TaskConnectorSubTaskOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据链接ID")
  private Integer dataTaskId;

  @ApiModelProperty(value = "数据名称")
  private String dataTaskName;
}
