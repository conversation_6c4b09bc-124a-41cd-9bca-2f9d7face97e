package com.sp.proxverse.common.model.dict.prehandler;

public enum PreTableEnum {
  CASE_TABLE("case_table", "case_table"),
  CASE_TIME_GAP("case_time_gap", "case_time_gap"),
  VARIANT_TABLE("variant_table", "variant_table"),

  ACTIVE_BUCKET("active_bucket", "active_bucket"),

  CASE_BUCKET("case_bucket", "case_bucket");

  private final String value;
  private final String name;

  PreTableEnum(String value, String name) {
    this.value = value;
    this.name = name;
  }

  public String getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
