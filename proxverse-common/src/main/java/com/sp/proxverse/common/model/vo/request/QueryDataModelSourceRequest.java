package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("查询数据模型请求对象")
public class QueryDataModelSourceRequest extends PageRequest {

  @ApiModelProperty(value = "当前所在主题的父ID，即：当前如果是一级主题则不传，当前如果是二级主题则传一级主题的topicId", required = true)
  private Integer topicId;

  private String name;

  private Integer simulationId;
}
