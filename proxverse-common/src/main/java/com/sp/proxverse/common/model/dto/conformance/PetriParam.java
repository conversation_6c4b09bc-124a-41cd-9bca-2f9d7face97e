package com.sp.proxverse.common.model.dto.conformance;

import java.util.List;
import java.util.Set;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.apache.spark.unsafe.types.UTF8String;
import scala.Tuple2;

@Data
@Builder
public class PetriParam {

  @Tolerate
  public PetriParam() {
    // comment empty
  }

  private Set<String> startList;

  private Set<String> endList;

  private Set<Tuple2<UTF8String, Integer>> transitionList;

  private Set<String> placesList;

  private List<Edges> edgesList;
}
