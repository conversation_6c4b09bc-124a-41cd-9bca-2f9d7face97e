package com.sp.proxverse.common.action.bo;

import com.sp.proxverse.common.action.enums.HttpBodyTypeEnum;
import com.sp.proxverse.common.action.enums.HttpTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-05-09 4:08 下午
 */
@Data
public class HttpActionDoParamBo extends ActionDoParamBo {

  private String url;

  private HttpTypeEnum httpTypeEnum;

  private Map<String, String> header;

  private Map<String, String> parameter;

  private HttpBodyTypeEnum httpBodyTypeEnum;

  private HttpBodyTypeEnum.ParamTypeEnum bodyParamTypeEnum;

  @ApiModelProperty("请求体，raw类型时使用")
  private String content;

  @ApiModelProperty("请求体，表单时使用")
  private Map<String, String> contents;
}
