package com.sp.proxverse.common.model.dict;

import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.util.I18nUtil;
import java.util.Objects;

public enum TopicSheetTypeEnum {
  PROCESS_AI(1, "200221"),
  PROCESS_VARIANT(2, "200222"),
  CASE_VIEW(3, "200223"),
  BUSINESS_VIEW(4, "200224"),
  PROCESS_VIEW(11, "200225"),
  PROCESS_CONFORMANCE(6, "200226"),
  PROCESS_NEWSHEET(8, "200227"),
  PROCESS_REWORK(7, "200228"),
  THROUGHPUT_TIME(9, "200229"),
  PROCESS_COMPARISON(10, "200230"),
  PROCESS_PATH(5, "200231"),
  PROCESS_AI_NEWSHEET(12, "200232"),
  PROCESS_OVERVIEW(13, "200233"),
  ROOT_CAUSE_ANALYSIS(14, "200234"),
  ;

  public static String getTypeByType(Integer type) {
    TopicSheetTypeEnum[] values = values();
    for (TopicSheetTypeEnum typeEnum : values) {
      if (Objects.equals(typeEnum.getValue(), type)) {
        return I18nUtil.getMessage(typeEnum.getName());
      }
    }
    throw new BizException(ErrorCode.ERROR_SYSTEM.getCode(), "type error");
  }

  private final Integer value;
  private final String name;

  TopicSheetTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
