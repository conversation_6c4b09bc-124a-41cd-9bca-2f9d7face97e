package com.sp.proxverse.common.model.job.create;

import com.sp.proxverse.common.model.po.DataModelPO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-02-22 18:46
 */
@Data
public class GenTaskResult {

  /**
   * 血缘关系
   *
   * <p>key：from
   *
   * <p>value:
   */
  private Map<String, String> lineageRelation;

  /** 任务Id */
  private List<Integer> taskExecutorIds;

  private List<DataModelPO> dataModelPOS;

  public GenTaskResult() {
    this.lineageRelation = new HashMap<>();
    this.taskExecutorIds = new ArrayList<>();
  }
}
