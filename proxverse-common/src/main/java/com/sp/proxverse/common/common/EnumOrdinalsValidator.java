package com.sp.proxverse.common.common;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class EnumOrdinalsValidator implements ConstraintValidator<EnumOrdinals, Integer> {

  private int enumLength;

  @Override
  public void initialize(EnumOrdinals constraintAnnotation) {
    ConstraintValidator.super.initialize(constraintAnnotation);
    enumLength = constraintAnnotation.enumClass().getEnumConstants().length;
  }

  @Override
  public boolean isValid(Integer ordinal, ConstraintValidatorContext context) {
    return ordinal >= 0 && ordinal < enumLength;
  }
}
