package com.sp.proxverse.common.model.vo.conformance.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询一致流程列表请求对象")
public class QueryConformanceListRequest {

  @ApiModelProperty(value = "表格sheetID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;
}
