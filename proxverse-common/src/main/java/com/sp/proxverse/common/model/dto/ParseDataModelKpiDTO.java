package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.vo.request.KpiVariableSortDTO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2024/3/8 10:08
 */
@Data
@Builder
public class ParseDataModelKpiDTO implements Serializable {
  @Tolerate
  public ParseDataModelKpiDTO() {
    // comment empty
  }

  private Integer dataModelId;

  private List<KpiPO> dimensionColumnList;

  private List<KpiVariableSortDTO> sortList;

  private Integer limit;

  @ApiModelProperty(value = "取行还是取列，为0或者空，默认取行，为1：取列（目前echarts用到）")
  private Integer type;

  @ApiModelProperty(value = "行数规则（1：滚动，2：限制，3：前N行）")
  private Integer rule;

  @ApiModelProperty(value = "当前页码，不传默认为1", example = "1")
  private Integer pageNum = 1;

  @ApiModelProperty(value = "每页条数，不传默认为10", example = "10")
  private Integer pageSize = 10;
}
