package com.sp.proxverse.common.model.dto.topicSheet;

import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class TopicParamDto {

  public enum TopicParamType {
    TEXT_REPLACEMENT,
    STATIC;

    public static TopicParamType from(int ordinal) {
      Preconditions.checkArgument(ordinal > -1 && ordinal < TopicParamType.values().length);
      return TopicParamType.values()[ordinal];
    }
  }

  @Tolerate
  public TopicParamDto() {
    // comment empty
  }

  private Integer id;

  private String name;

  private String value;

  private Integer type;

  @ApiModelProperty("1:local，2：知识模型")
  private Integer origin;

  @ApiModelProperty("1:有重名，需要显示！ 名称相同，请修改名称之后再使用，2：没有重名不显示！")
  private Integer duplication;

  private String formatting;

  private String format;

  private String columnType;
}
