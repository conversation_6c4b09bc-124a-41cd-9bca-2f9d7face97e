package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.dto.domain.License;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface LicenseMapper extends BaseMapper<License> {

  @InterceptorIgnore(tenantLine = "on")
  void installLicense(License bean);

  @InterceptorIgnore(tenantLine = "on")
  void activate(License bean);

  @InterceptorIgnore(tenantLine = "on")
  void inactivate(License bean);

  @InterceptorIgnore(tenantLine = "on")
  void inactivateAll();

  @InterceptorIgnore(tenantLine = "on")
  void deleteLicense(License bean);

  @InterceptorIgnore(tenantLine = "on")
  License getLicense(String sn);

  @InterceptorIgnore(tenantLine = "on")
  License getValidLicense();

  @InterceptorIgnore(tenantLine = "on")
  List<License> listLicenses();
}
