package com.sp.proxverse.common.model.dto.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;

/**
 * 管理员密码历史记录
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@Builder
@TableName("t_admin_password_history")
public class AdminPasswordHistoryPO {

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("租户ID")
  private Integer tenantId;

  @ApiModelProperty("管理员ID")
  private Integer adminId;

  @ApiModelProperty("密码哈希值")
  private String passwordHash;

  @ApiModelProperty("创建时间")
  private LocalDateTime createTime;

  /** @see com.sp.proxverse.common.model.dict.DeletedEnum */
  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;
}
