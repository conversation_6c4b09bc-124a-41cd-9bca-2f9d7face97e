package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("一致性分析的二维图数据列表DTO")
public class CaseTimeDataDTO {

  @Tolerate
  public CaseTimeDataDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "caseTime")
  private String caseTime;

  @ApiModelProperty(value = "caseId")
  private Long count;
}
