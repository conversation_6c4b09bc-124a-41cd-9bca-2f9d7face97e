package com.sp.proxverse.common.model.dto.future;

import com.sp.proxverse.common.model.dto.KpiResultDTO;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class CategoryKpiFutureDTO {

  @Tolerate
  public CategoryKpiFutureDTO() {
    // comment empty
  }

  private String param;

  private CompletableFuture<List<KpiResultDTO>> future;
}
