package com.sp.proxverse.common.model.job;

import org.sparkproject.guava.base.Objects;

/**
 * <AUTHOR>
 * @create 2022-12-12 15:26
 */
public enum TaskEnum {
  /** 数据提取 */
  DATA_EXTRACTION("dataExtraction", 1),

  /** 数据转换 */
  ETL("etl", 2),

  REFRESH_MODEL("refreshModel", 3),

  SIMULATION("simulation", 4),

  KAFKA("KAFKA", 5),
  INCREMENTAL_FILE("INCREMENTAL_FILE", 6),

  UNKNOWN("unknown", -1);

  private String name;

  private Integer value;

  TaskEnum(String name, Integer value) {
    this.name = name;
    this.value = value;
  }

  public static TaskEnum getTaskEnum(String enumStr) {
    for (TaskEnum value : TaskEnum.values()) {
      if (Objects.equal(value.name(), enumStr)) {
        return value;
      }
    }
    return UNKNOWN;
  }

  public Integer getValue() {
    return this.value;
  }

  // 根据优先级返回顺序
  public static int getPriority(TaskEnum taskEnum) {
    switch (taskEnum) {
      case DATA_EXTRACTION:
      case INCREMENTAL_FILE:
      case KAFKA:
        return 1;
      case ETL:
        return 2;
      case REFRESH_MODEL:
        return 3;
      default:
        return 4;
    }
  }

  public static Integer getTaskType(String taskEnum) {
    switch (taskEnum) {
      case "DATA_EXTRACTION":
        return 0;
      case "ETL":
        return 1;
      default:
        return -1;
    }
  }
}
