package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.experimental.Tolerate;

@Builder
public class EventCountDTO {

  @Tolerate
  public EventCountDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "此事件占的数量")
  private Integer count;

  @ApiModelProperty(value = "事件值")
  private String event;

  private String variable;

  @ApiModelProperty(value = "结束值")
  private String source;

  @ApiModelProperty(value = "开始值")
  private String target;

  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public String getTarget() {
    return target;
  }

  public void setTarget(String target) {
    this.target = target;
  }

  public Integer getCount() {
    return count;
  }

  public void setCount(Integer count) {
    this.count = count;
  }

  public String getVariable() {
    return variable;
  }

  public void setVariable(String variable) {
    this.variable = variable;
  }

  public String getEvent() {
    return event;
  }

  public void setEvent(String event) {
    this.event = event;
  }
}
