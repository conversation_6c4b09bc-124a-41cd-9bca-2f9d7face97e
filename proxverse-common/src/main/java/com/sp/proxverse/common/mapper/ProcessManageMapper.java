package com.sp.proxverse.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.po.ProcessManagePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Mapper
public interface ProcessManageMapper extends BaseMapper<ProcessManagePo> {

  @DS("oauth2")
  @InterceptorIgnore(tenantLine = "on")
  List<ProcessManagePo> getProcessManageDatumLibrary(
      @Param("datumLibraryProcessIdList") List<Integer> datumLibraryProcessIdList);
}
