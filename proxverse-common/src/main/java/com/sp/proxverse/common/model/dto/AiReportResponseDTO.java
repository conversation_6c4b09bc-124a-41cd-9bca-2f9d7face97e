package com.sp.proxverse.common.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiReportResponseDTO {

  /** 存 report数据库表的 ID */
  private Integer reportId;

  /** 任务ID:python 任务返回的 id */
  private String taskId;

  /** 状态 */
  private String status;

  /** 消息 */
  private String message;
}
