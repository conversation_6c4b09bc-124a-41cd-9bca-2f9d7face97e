package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class JdbcDTO {

  @Tolerate
  public JdbcDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "事件名称")
  private String ip;

  @ApiModelProperty(value = "发生时间")
  private Integer port;

  @ApiModelProperty(value = "时间间隔")
  private String dbname;

  @ApiModelProperty(value = "时间显示单位")
  private String username;

  @ApiModelProperty(value = "时间显示单位")
  private String password;

  @ApiModelProperty(value = "schema名称", required = true)
  private String schemaName;

  @ApiModelProperty(value = "实例号（HANA DB）", required = true)
  private String instanceNumber;

  @ApiModelProperty(value = "1：标准链接，2：自定义链接", required = true)
  private Integer bizType;

  @ApiModelProperty(
      value = "jdbc类型（1：mysql,2:oracle,3:db2,4:hive,5:postgreSql,6:impala），当bizType=1时此字段不能为空")
  private Integer jdbcType;

  @ApiModelProperty(value = "自定义链接，当bizType=2时此字段不能为空")
  private String connectLink;
}
