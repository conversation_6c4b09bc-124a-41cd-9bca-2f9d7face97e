package com.sp.proxverse.common.model.dto.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;
import scala.Serializable;

/**
 * 数据权限
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
@TableName("t_data_authority")
public class DataAuthority implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 数据类型 topic */
  public static final Integer DATA_TYPE_TOPIC = 1;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 数据Id */
  private Integer dataId;

  /** 数据类型，1：topic */
  private Integer dataType;

  /** 0：未删除，1：已删除 */
  private Integer deleted;

  /** 创建人Id */
  private Integer createUser;

  private Integer parentId;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;
}
