package com.sp.proxverse.common.model.dto;

import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class Lable implements Serializable {

  private static final long serialVersionUID = 1L;

  @Tolerate
  public Lable() {
    // comment empty
  }

  private String lable;
  private String prop;
  private Integer columnId;
  private Integer columnType;
  /** 标识此字段是否为流程时长（前端要展示时分秒单位） */
  private Integer hasDuration;

  /** 列类型，1：变量，2：kpi */
  private Integer propType;
}
