package com.sp.proxverse.common.model.vo.duration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("事件吞吐列表请求对象")
public class EventDurationListRequest {

  @ApiModelProperty(value = "数据任务ID", required = true)
  @NotNull(message = "{400006}")
  private Integer sheetId;

  private Integer componentId;

  @ApiModelProperty(value = "排序字段")
  private String orderColumn;

  @ApiModelProperty(value = "正序asc，倒序desc")
  private String orderRule;

  private String startTime;

  private String endTime;
}
