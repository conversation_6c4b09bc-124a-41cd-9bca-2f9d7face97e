package com.sp.proxverse.common.model.vo;

import com.sp.proxverse.common.model.page.PageRequest;
import com.sp.proxverse.common.util.DateTimeUtil;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/6 13:54
 */
@Data
@Slf4j
public class AuditLogReq extends PageRequest {
  private String startTime;

  private String endTime;

  private String interfacePath;

  private String param;

  private String userName;

  private Integer userId;

  private List<String> auditLogHideRoles;

  public Date getStartTimeDate() {
    return DateTimeUtil.stringTodate(startTime);
  }

  public Date getEndTimeDate() {
    return DateTimeUtil.stringTodate(endTime);
  }
}
