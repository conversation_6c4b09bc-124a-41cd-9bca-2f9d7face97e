package com.sp.proxverse.common.model.vo.datamodel;

import com.sp.proxverse.common.model.dto.FieldLineDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据类型详情表列表VO对象")
public class DataModelDetailOutputVO {
  @Tolerate
  public DataModelDetailOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "表ID（即fileId）")
  private Integer fileId;

  @ApiModelProperty(value = "表名")
  private String fileName;

  @ApiModelProperty(value = "是否是主表（0：否，1：是，2：次表）")
  private Integer hasActive;

  @ApiModelProperty(value = "文件中文备注")
  private String remark;

  @ApiModelProperty(value = "与该表有连线的表集合")
  private List<Integer> lineFileIdList;

  @ApiModelProperty(value = "字段信息及其连线信息")
  private List<FieldLineDTO> fieldLineList;
}
