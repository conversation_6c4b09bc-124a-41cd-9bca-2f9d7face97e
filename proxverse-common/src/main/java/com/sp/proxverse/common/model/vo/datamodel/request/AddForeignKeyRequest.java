package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("添加外键请求对象")
public class AddForeignKeyRequest {

  @ApiModelProperty(value = "数据模型ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataModelId;

  @ApiModelProperty(value = "文件ID", required = true)
  @NotNull(message = "{400006}")
  private Integer fileIdLeft;

  @ApiModelProperty(value = "文件ID", required = true)
  @NotNull(message = "{400006}")
  private Integer fileIdRight;

  @ApiModelProperty(value = "外键连接列表")
  //    @NotEmpty(message = "外键连接列表不能为空")
  private List<AddForeignKeySubRequest> list;
}
