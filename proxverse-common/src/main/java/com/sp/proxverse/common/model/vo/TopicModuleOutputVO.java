package com.sp.proxverse.common.model.vo;

import com.sp.proxverse.common.model.dto.TopicKpiDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("指挥舱业务主题列表VO对象")
public class TopicModuleOutputVO {
  @Tolerate
  public TopicModuleOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "名称")
  private String name;

  @ApiModelProperty(value = "更新时间")
  private String updateTimeZh;

  @ApiModelProperty(value = "表格sheetId")
  private Integer topicSheetId;

  @ApiModelProperty(value = "sheet类型（1：流程AI，2：流程变体，3：案例视图，4：业务视图，5：流程视图）")
  private Integer topicSheetType;

  @ApiModelProperty(value = "主题ID")
  private Integer topicId;

  private Integer parentId;

  @ApiModelProperty(value = "主题类型（100:全景图，101：业务全景图，200：业务主题，201:业务分析图谱，202：业务知识模型）")
  private Integer topicType;

  private List<Integer> sheetIdList;

  @ApiModelProperty(value = "kpi集合")
  List<TopicKpiDTO> kpiDTOList;
}
