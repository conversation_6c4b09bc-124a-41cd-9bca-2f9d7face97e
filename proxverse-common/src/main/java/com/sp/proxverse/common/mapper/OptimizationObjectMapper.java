package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.po.OptimizationObjectPO;
import org.apache.ibatis.annotations.Param;

public interface OptimizationObjectMapper extends BaseMapper<OptimizationObjectPO> {

  Integer queryTenantId(
      @Param("tableName") String tableName,
      @Param("field") String field,
      @Param("value") String value,
      @Param("tenantId") Integer tenantId);
}
