package com.sp.proxverse.common.model.dict;

public enum UserTypeEnum {
  USER(10, "用户"),
  USER_GROUP(20, "用户组"),
  ;

  public static boolean isUser(Integer value) {
    return USER.value.equals(value);
  }

  public static boolean isUserGroup(Integer value) {
    return USER_GROUP.value.equals(value);
  }

  private final Integer value;
  private final String name;

  UserTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
