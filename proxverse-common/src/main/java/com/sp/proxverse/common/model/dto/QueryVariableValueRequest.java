package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询变量的值列表（事件过滤）接口分页请求对象")
public class QueryVariableValueRequest extends PageRequest {

  @Tolerate
  public QueryVariableValueRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "当前所在主题ID不能为空")
  private Integer topicId;

  private Integer sheetId;

  @ApiModelProperty(value = "字段", required = true)
  private Integer fieldId;

  private String expression;

  @ApiModelProperty(value = "字段名称", required = true)
  //    @NotNull(message = "字段不能为空")
  private String field;

  @ApiModelProperty(value = "排序（0：正序，1：倒序）默认为0")
  private Integer order = 0;

  @ApiModelProperty(value = "运算符号类型（10：>=，20：<=，30：=，40：time类型）(事件过滤此字段不填)，type=700时此字段可填可不填")
  private Integer operationType;

  @ApiModelProperty(value = "变量值列表（operationType=40时，传值比如：2021-10-10&2021-10-12）", required = true)
  private String operationValue;

  private Integer prikey;

  @ApiModelProperty(value = "有组件ID，则是newsheet里来的,为了处理组件过滤")
  private Integer componentId;

  private Integer likeDisabled;
}
