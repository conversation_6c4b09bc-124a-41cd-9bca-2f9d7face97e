package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
@ApiModel("查询文件表头 DTO")
public class QueryFileHeadRequest implements Serializable {
  @Tolerate
  public QueryFileHeadRequest() {
    // comment empty
  }

  private Integer fileId;

  private String fileName;

  private String uniqFileName;

  private String separatorMark;

  private String encoding;

  private Integer limitRow;

  public Integer getLimitRow() {
    if (limitRow == null) {
      return 50;
    }
    return limitRow;
  }
}
