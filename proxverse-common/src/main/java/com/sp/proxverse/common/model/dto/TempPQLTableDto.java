package com.sp.proxverse.common.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sp.proxverse.common.common.AllowedValues;
import com.sp.proxverse.common.model.dict.KpiSaveTypeEnum;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class TempPQLTableDto {

  private Integer id;

  @NotBlank private String name;

  @Valid @NotNull private List<NamedColumn> columns;

  private List<String> filters = new ArrayList<>();

  @Valid private List<Sort> sorts = new ArrayList<>();;

  private Integer limit = -1;

  private KpiSaveTypeEnum kpiSaveTypeEnum;

  @JsonIgnore private Map<String, String> joins = new HashMap<>();

  public void addColumn(String expr, String name) {
    if (columns == null) {
      columns = new ArrayList<>();
    }
    columns.add(new NamedColumn(name, expr));
  }

  @Data
  public static class NamedColumn {
    @NotBlank private String name;

    @NotBlank private String expr;

    /** @see org.apache.spark.common.model.DataTypeEnum */
    private Integer type;

    private boolean joinKey = false;

    public NamedColumn() {}

    public NamedColumn(String name, String expr) {
      this.name = name;
      this.expr = expr;
    }

    public NamedColumn(String name, String expr, Integer type, boolean joinKey) {
      this.name = name;
      this.expr = expr;
      this.type = type;
      this.joinKey = joinKey;
    }

    public String namedExpr() {
      return expr + " as `" + name + "`";
    }
  }

  @Data
  public static class Sort {

    @NotBlank private String column;

    @AllowedValues(values = {"ASC", "DESC", "asc", "desc"})
    private String direction;

    public Sort() {}

    public Sort(String column, String direction) {
      this.column = column;
      this.direction = direction;
    }
  }

  @Data
  public static class TableDef {
    private List<NamedColumn> columns;

    private List<String> filters;

    private List<Sort> sorts;

    private Integer limit;

    // join keys on model table
    private Map<String, String> joins = new HashMap<>();

    public TableDef() {
      // comment empty
    }
  }
}
