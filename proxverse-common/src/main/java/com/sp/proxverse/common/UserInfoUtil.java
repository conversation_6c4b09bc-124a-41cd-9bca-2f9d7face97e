package com.sp.proxverse.common;

import cn.dev33.satoken.exception.NotWebContextException;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.spring.SpringMVCUtil;
import cn.dev33.satoken.stp.StpUtil;
import com.sp.proxverse.common.mapper.AdminMapper;
import com.sp.proxverse.common.model.dto.domain.Admin;
import com.sp.proxverse.common.model.enums.AuthorityClientEnum;
import com.sp.proxverse.common.model.oauth2.UserDetailsToken;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.oidc.user.DefaultOidcUser;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2023-03-15 17:22
 */
@Service
@Slf4j
public class UserInfoUtil {

  public static final String USER_INFO_KEY = "userInfo";

  @Value("${spring.ldap.groupId:1}")
  private Integer groupId;

  @Value("${spring.ldap.ldapEnabled:false}")
  private String ldapEnabled;

  @Value("${prx.login.loginToken:}")
  private String loginToken;

  private static final String IS_TEST = "isTest";
  private static final String FALSE = "false";
  private static final String TRUE = "true";
  public static ThreadLocal<UserDetailsToken> authInfo =
      new ThreadLocal() {
        @Override
        protected Object initialValue() {
          if (System.getProperty(IS_TEST, FALSE).equals(TRUE)) {
            UserDetailsToken userDetailsToken = new UserDetailsToken();
            userDetailsToken.setUserId(1);
            userDetailsToken.setUserName("<EMAIL>");
            userDetailsToken.setLevel("ROOT");
            userDetailsToken.setGroupId(1);
            return userDetailsToken;
          } else {
            return null;
          }
        }
      };

  @Autowired AdminMapper adminMapper;

  public static void updateUserDetails(Integer groupId) {
    if (groupId == null || groupId <= 0) {
      return;
    }
    UserDetailsToken userDetailsToken;
    if (authInfo.get() == null) {
      userDetailsToken = new UserDetailsToken();
    } else {
      userDetailsToken = authInfo.get();
    }
    userDetailsToken.setGroupId(groupId);
    authInfo.set(userDetailsToken);
  }

  public boolean userLogout(Integer userId) {
    if (System.getProperty(IS_TEST, FALSE).equals(TRUE)) {
      authInfo.remove();
      return true;
    }

    if (!SpringMVCUtil.isWeb()) {
      if (authInfo.get() != null) {
        authInfo.remove();
        return true;
      } else {
        return false;
      }
    }
    if (StpUtil.isLogin(userId)) {
      StpUtil.logout(userId);
      return true;
    }
    return false;
  }

  public Integer getGroupId() {
    if (TRUE.equalsIgnoreCase(ldapEnabled) || StringUtils.isNotBlank(loginToken)) {
      return groupId;
    }
    return getUserInfo().getGroupId();
  }

  public UserDetailsToken getUserInfo() {
    if (System.getProperty(IS_TEST, FALSE).equals(TRUE)) {
      return authInfo.get();
    }

    UserDetailsToken userDetailsToken = new UserDetailsToken();
    // LDAP 环境
    if (TRUE.equalsIgnoreCase(ldapEnabled)) {
      userDetailsToken.setGroupId(groupId);
    }
    if (!SpringMVCUtil.isWeb()) {
      if (authInfo.get() != null) {
        return authInfo.get();
      } else {
        return userDetailsToken;
      }
    }

    try {
      if (StpUtil.isLogin()) {
        return (UserDetailsToken) StpUtil.getSession().get(USER_INFO_KEY);
      }
    } catch (NotWebContextException e) {
      log.error("getUserInfo error:", e);
    }

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    UserDetailsToken userDetails = new UserDetailsToken();

    if (authentication == null) {
      return null;
    }
    if (authentication instanceof OAuth2AuthenticationDetails) {
      return userDetails;
    }

    if (authentication instanceof OAuth2Authentication) {
      OAuth2Authentication oauth2Authentication = (OAuth2Authentication) authentication;
      OAuth2AuthenticationDetails oAuth2AuthenticationDetails =
          (OAuth2AuthenticationDetails) oauth2Authentication.getDetails();

      userDetailsToken = (UserDetailsToken) oAuth2AuthenticationDetails.getDecodedDetails();
      userDetails.setUserName(userDetailsToken.getUserName());
      userDetails.setUserId(userDetailsToken.getUserId());
      userDetails.setGroupId(userDetailsToken.getGroupId());
      userDetails.setLevel(userDetailsToken.getLevel());
      return userDetails;
    }
    if (authentication instanceof UserDetailsToken) {
      userDetailsToken = (UserDetailsToken) authentication;
      userDetails.setUserName(userDetailsToken.getUserName());
      userDetails.setUserId(userDetailsToken.getUserId());
      userDetails.setGroupId(userDetailsToken.getGroupId());
      userDetails.setLevel(userDetailsToken.getLevel());
      return userDetails;
    }
    if (authentication.getPrincipal() instanceof DefaultOidcUser) {
      DefaultOidcUser oidcUser = ((DefaultOidcUser) authentication.getPrincipal());

      Admin admin = adminMapper.getAdminSso(oidcUser.getName());
      if (admin == null) {
        return userDetails;
      }
      userDetails.setUserName(admin.getUserName());
      userDetails.setUserId(admin.getId());
      userDetails.setGroupId(admin.getTenantId());
      userDetails.setLevel(admin.getLevel());
      return userDetails;
    }

    if (authentication.getPrincipal() instanceof DefaultOAuth2User) {
      String registrationId =
          ((OAuth2AuthenticationToken) authentication).getAuthorizedClientRegistrationId();
      if (AuthorityClientEnum.BYTEDANCE.value.equals(registrationId.toUpperCase(Locale.ROOT))) {
        return bytedanceUser(authentication);
      } else {
        DefaultOAuth2User user = ((DefaultOAuth2User) authentication.getPrincipal());
        String name = user.getName();
        Integer groupIdLocal = Integer.valueOf(user.getAttribute("groupId").toString());
        String level = user.getAttribute("level").toString();
        Integer id = Integer.valueOf(user.getAttribute("id").toString());

        userDetails.setUserName(name);
        userDetails.setUserId(id);
        userDetails.setGroupId(groupIdLocal);
        userDetails.setLevel(level);

        return userDetails;
      }
    }

    return userDetails;
  }

  private UserDetailsToken bytedanceUser(Authentication authentication) {
    DefaultOAuth2User oAuth2User = ((DefaultOAuth2User) authentication.getPrincipal());
    LinkedHashMap<String, String> result =
        (LinkedHashMap<String, String>) oAuth2User.getAttributes().get("data");
    String enName = result.get("en_name");
    String email = result.get("email");
    String userName;
    if (StringUtils.isNotBlank(email)) {
      userName = email;
    } else {
      userName = enName;
    }

    Admin admin = adminMapper.getAdmin(userName);

    UserDetailsToken userDetails = new UserDetailsToken();
    userDetails.setUserName(userName);
    if (admin == null) {
      return userDetails;
    } else {
      userDetails.setUserName(userName);
      userDetails.setUserId(admin.getId());
      userDetails.setGroupId(admin.getTenantId());
      userDetails.setLevel(admin.getLevel());
      return userDetails;
    }
  }

  public void refreshUserSessionInfo(Admin admin) {
    SaSession sessionByLoginId = StpUtil.getSessionByLoginId(admin.getId(), false);
    if (sessionByLoginId == null) {
      return;
    }
    UserDetailsToken userDetailsToken = new UserDetailsToken();
    userDetailsToken.setUserName(admin.getUserName());
    userDetailsToken.setUserId(admin.getId());
    userDetailsToken.setGroupId(admin.getTenantId());
    userDetailsToken.setLevel(admin.getLevel());
    sessionByLoginId.set(UserInfoUtil.USER_INFO_KEY, userDetailsToken);
  }

  public void kickout(List<Integer> userIds) {
    for (Integer userId : userIds) {
      StpUtil.kickout(userId);
    }
  }
}
