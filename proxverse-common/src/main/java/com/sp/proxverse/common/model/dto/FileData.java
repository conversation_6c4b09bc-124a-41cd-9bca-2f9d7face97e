package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.Builder;
import lombok.experimental.Tolerate;

@Builder
public class FileData {

  @Tolerate
  public FileData() {
    // comment empty
  }

  @ApiModelProperty(value = "mongodb主键ID")
  private String id;

  @ApiModelProperty(value = "自定义文件唯一ID")
  private String fileUuid;

  @ApiModelProperty(value = "mysql中的fileId")
  private Integer fileId;

  @ApiModelProperty(value = "caseId")
  private String caseId;

  @ApiModelProperty(value = "不定长的字段数据")
  private Map<String, String> dataMap;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getFileUuid() {
    return fileUuid;
  }

  public void setFileUuid(String fileUuid) {
    this.fileUuid = fileUuid;
  }

  public Integer getFileId() {
    return fileId;
  }

  public void setFileId(Integer fileId) {
    this.fileId = fileId;
  }

  public String getCaseId() {
    return caseId;
  }

  public void setCaseId(String caseId) {
    this.caseId = caseId;
  }

  public Map<String, String> getDataMap() {
    return dataMap;
  }

  public void setDataMap(Map<String, String> dataMap) {
    this.dataMap = dataMap;
  }

  public Integer getDeleted() {
    return deleted;
  }

  public void setDeleted(Integer deleted) {
    this.deleted = deleted;
  }
}
