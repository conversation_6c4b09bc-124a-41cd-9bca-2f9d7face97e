package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-11 11:37
 */
@Data
@ApiModel("数据任务孩子列表")
public class DataTaskChildListRes {

  private List<DataTaskChildListTransformation> transformations;

  private List<DataTaskChildListExtract> extracts;

  public DataTaskChildListRes() {
    this.transformations = new ArrayList<>();
    this.extracts = new ArrayList<>();
  }
}
