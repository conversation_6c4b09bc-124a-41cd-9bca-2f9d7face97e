package com.sp.proxverse.common.model.dto.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_admin")
public class Admin extends BaseEntity {

  private static final long serialVersionUID = 1L;

  /** 用户名 */
  private String userName;

  /** 姓名 */
  private String name;

  private String password;

  /** @see AuthorityClientEnum */
  private String userSource;

  /** USER,ANALYST,ROOT */
  private String level;

  /** 0：未删除，1：已删除 */
  private Integer deleted;

  private Date createTime;

  private Date updateTime;

  /**
   * 1：正常，2：停用
   *
   * @see com.sp.proxverse.common.model.enums.AdminStatusEnum
   */
  private Integer status;

  private Date authorizationDate;

  private String authorizationCode;
  private String ssoId;
  private Integer tenantId;
  private Integer roleExpire;

  @ApiModelProperty("密码更改间隔月数,默认6个月")
  private Integer loginFailNum;

  @ApiModelProperty("密码更改间隔月数,默认6个月")
  private LocalDateTime lastChangePwdDate;
}
