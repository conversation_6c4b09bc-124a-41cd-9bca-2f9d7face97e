package com.sp.proxverse.common.model.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
public class QueryDataByColumnsRespDTO {

  @Tolerate
  public QueryDataByColumnsRespDTO() {
    // comment empty
  }

  private Integer count;

  private Integer pageNum;
  private Integer pageSize;

  private VariantTableDataDTO data;
}
