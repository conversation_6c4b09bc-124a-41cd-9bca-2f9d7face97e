package com.sp.proxverse.common.model.dict;

import com.sp.proxverse.common.exception.BizException;
import java.util.Objects;
import java.util.stream.Stream;

public enum TaskRateTypeEnum {
  HANDLE(0, "手动执行"),
  <PERSON><PERSON><PERSON>(1, "每小时"),
  <PERSON>A<PERSON>(2, "每天"),
  WEE<PERSON>(3, "每周"),
  CRON(4, "cron"),
  ;

  private final Integer value;
  private final String name;

  TaskRateTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static TaskRateTypeEnum getEnumByCode(Integer code) {
    if (Objects.isNull(code)) {
      throw new BizException(5000, "TaskRateTypeEnum  code is null ");
    }
    TaskRateTypeEnum[] values = TaskRateTypeEnum.values();
    for (TaskRateTypeEnum value : values) {
      if (value.getValue().equals(code)) {
        return value;
      }
    }
    throw new BizException(5000, "TaskRateTypeEnum code does not exist  code: " + code);
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    TaskRateTypeEnum[] values = TaskRateTypeEnum.values();
    for (TaskRateTypeEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value.getName();
      }
    }
    return "";
  }

  /**
   * 根据文件后缀名得到枚举int值
   *
   * @param f
   * @return
   */
  public static Integer filterFileType(String f) {
    TaskRateTypeEnum fileTypeEnum =
        Stream.of(values()).filter(e -> Objects.equals(e.getName(), f)).findFirst().orElse(null);
    return Objects.isNull(fileTypeEnum) ? null : fileTypeEnum.getValue();
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
