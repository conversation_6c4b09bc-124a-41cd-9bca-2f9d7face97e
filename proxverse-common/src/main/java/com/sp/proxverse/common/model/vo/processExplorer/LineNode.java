package com.sp.proxverse.common.model.vo.processExplorer;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @create 2023-05-09 10:05
 */
@Data
@Builder
public class LineNode implements Serializable {

  private static final long serialVersionUID = 1L;

  @Tolerate
  public LineNode() {
    // comment empty
  }

  @ApiModelProperty(value = "起始事件名称")
  private String source;

  @ApiModelProperty(value = "目标事件名称")
  private String target;

  @ApiModelProperty(value = "节点之间显示的数据")
  private String second;

  @ApiModelProperty(value = "线的数量")
  private Long number;

  @ApiModelProperty(value = "线宽")
  private Double penWidth;

  @ApiModelProperty(value = "颜色")
  private String color;

  private Boolean defaultLine;

  @ApiModelProperty("当前事件所在的流程中占比")
  private String lineCaseRatio;

  private Boolean isStartOrEnd;

  private Integer gradient;

  @ApiModelProperty(value = "KPI结果集")
  private List<String> kpiValues;

  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public String getTarget() {
    return target;
  }

  public void setTarget(String target) {
    this.target = target;
  }

  public String getSecond() {
    return second;
  }

  public void setSecond(String second) {
    this.second = second;
  }

  public Long getNumber() {
    return number;
  }

  public void setNumber(Long number) {
    this.number = number;
  }

  public String getColor() {
    return color;
  }

  public void setColor(String color) {
    this.color = color;
  }

  public List<String> getKpiValues() {
    return kpiValues;
  }

  public void setKpiValues(List<String> kpiValues) {
    this.kpiValues = kpiValues;
  }

  public Boolean getDefaultLine() {
    return defaultLine;
  }

  public void setDefaultLine(Boolean defaultLine) {
    this.defaultLine = defaultLine;
  }

  public Integer getGradient() {
    return gradient;
  }

  public void setGradient(Integer gradient) {
    this.gradient = gradient;
  }

  public Boolean getStartOrEnd() {
    return isStartOrEnd;
  }

  public void setStartOrEnd(Boolean startOrEnd) {
    isStartOrEnd = startOrEnd;
  }

  public String getLineCaseRatio() {
    return lineCaseRatio;
  }

  public void setLineCaseRatio(String lineCaseRatio) {
    this.lineCaseRatio = lineCaseRatio;
  }
}
