package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.po.FilePO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FileMapper extends BaseMapper<FilePO> {

  @InterceptorIgnore(tenantLine = "on")
  List<FilePO> getList();

  @InterceptorIgnore(tenantLine = "on")
  List<FilePO> getList4Tenant(@Param("tenantId") Integer tenantId);

  void updateFileSpace(@Param("fileId") Integer fileId, @Param("size") Long size);
}
