package com.sp.proxverse.common.model.dict;

import java.util.Map;
import java.util.Objects;

public enum CaseEventTimeFixEnum {
  CASEID("case:concept:name", "案例ID", "caseId"),
  EVENT("concept:name", "事件名称", "label"),
  TIME("time:timestamp", "时间", "时间戳"),
  ;

  private final String value;
  private final String alias;
  private final String name;

  public static String variableTrans(String variable, Map<String, String> caseEventMap) {
    for (Map.Entry<String, String> entry : caseEventMap.entrySet()) {
      if (Objects.equals(variable, entry.getValue())) {
        if (Objects.equals(entry.getKey(), CASEID.getValue())) {
          return CASEID.getAlias();
        }
        if (Objects.equals(entry.getKey(), EVENT.getValue())) {
          return EVENT.getAlias();
        }
        if (Objects.equals(entry.getKey(), TIME.getValue())) {
          return TIME.getAlias();
        }
      }
    }
    return variable;
  }

  CaseEventTimeFixEnum(String value, String alias, String name) {
    this.value = value;
    this.alias = alias;
    this.name = name;
  }

  public String getValue() {
    return value;
  }

  public String getAlias() {
    return alias;
  }

  public String getName() {
    return name;
  }
}
