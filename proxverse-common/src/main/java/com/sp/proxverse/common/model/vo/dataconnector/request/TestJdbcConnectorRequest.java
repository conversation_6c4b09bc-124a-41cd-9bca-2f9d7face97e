package com.sp.proxverse.common.model.vo.dataconnector.request;

import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.dict.DataConnectorBizTypeEnum;
import com.sp.proxverse.common.model.dict.DataConnectorJDBCTypeEnum;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.util.I18nUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@ApiModel("测试jdbc连通性请求对象")
public class TestJdbcConnectorRequest {

  @Tolerate
  public TestJdbcConnectorRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "IP", required = true)
  private String ip;

  @ApiModelProperty(value = "端口", required = true)
  private Integer port;

  @ApiModelProperty(value = "库名称", required = true)
  private String dbname;

  @ApiModelProperty(value = "数据库用户名", required = true)
  private String username;

  @ApiModelProperty(value = "数据库密码", required = true)
  private String password;

  @ApiModelProperty(value = "schema名称", required = true)
  private String schemaName;

  @ApiModelProperty(value = "实例号（HANA DB）", required = true)
  private String instanceNumber;

  /** @see DataConnectorBizTypeEnum */
  @ApiModelProperty(value = "1：标准链接，2：自定义链接", required = true)
  private Integer bizType;

  @ApiModelProperty(
      value = "jdbc类型（1：mysql,2:oracle,3:db2,4:hive,5:postgreSql,6:impala），当bizType=1时此字段不能为空")
  private Integer jdbcType;

  @ApiModelProperty(value = "自定义链接，当bizType=2时此字段不能为空")
  private String connectLink;

  public void check() {
    if (!Objects.equals(this.bizType, DataConnectorBizTypeEnum.STANDARD.getValue())) {
      if (StringUtils.isBlank(this.connectLink)) {
        throw new BizException(5000, I18nUtil.getMessage(I18nConst.CONNECT_LINK_EMPTY_DISABLED));
      }
      return;
    }

    if (StringUtils.isBlank(this.ip)
        || Objects.isNull(this.port)
        || StringUtils.isBlank(this.dbname)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.IP_PORT_EMPTY_DISABLED));
    }
    if (Objects.equals(this.jdbcType, DataConnectorJDBCTypeEnum.DB2.getValue())) {
      if (StringUtils.isBlank(this.schemaName)) {
        throw new BizException(5000, I18nUtil.getMessage(I18nConst.DB2_SCHEMA_EMPTY_DISABLED));
      }
    }
    if (!Objects.equals(this.jdbcType, DataConnectorJDBCTypeEnum.HIVE.getValue())) {
      if (StringUtils.isBlank(this.username) || StringUtils.isBlank(this.password)) {
        throw new BizException(
            5000, I18nUtil.getMessage(I18nConst.USERNAME_PASSWORD_EMPTY_DISABLED));
      }
    }
  }
}
