package com.sp.proxverse.common.model.enums;

import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dto.domain.Admin;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/2 13:32
 */
public enum UserStateEnum {
  UNKNOWN(0, "未知"),
  DELETED(-1, "删除"),
  DEACTIVATE(2, "停用"),

  NORMAL(1, "正常"),
  NEW(3, "新用户"),
  ;

  private Integer code;
  private String name;

  public String getName() {
    return this.name;
  }

  public Integer getCode() {
    return this.code;
  }

  UserStateEnum(Integer code, String name) {
    this.code = code;
    this.name = name;
  }

  public static List<Integer> getCommonUserStatus() {
    List<Integer> integers = new ArrayList<>();
    integers.add(NORMAL.code);
    integers.add(NEW.code);
    return integers;
  }

  public static UserStateEnum getUserStateEnumByAdmin(Admin admin) {
    if (admin == null) {
      return UNKNOWN;
    }
    if (Objects.equals(admin.getDeleted(), DeletedEnum.HAS_DELETED.getValue())) {
      return DELETED;
    }

    if (admin.getStatus() == 1) {
      return NORMAL;
    }

    if (admin.getStatus() == 2) {
      return DEACTIVATE;
    }
    if (admin.getStatus() == 3) {
      return NEW;
    }
    return UNKNOWN;
  }
}
