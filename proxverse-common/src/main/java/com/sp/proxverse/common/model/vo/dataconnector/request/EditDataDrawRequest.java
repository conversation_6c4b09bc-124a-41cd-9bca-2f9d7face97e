package com.sp.proxverse.common.model.vo.dataconnector.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("编辑数据提取请求对象")
public class EditDataDrawRequest {

  @ApiModelProperty(value = "jdbc数据提取ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataExtractorId;

  @ApiModelProperty(value = "jdbc数据提取名称", required = true)
  @NotBlank(message = "{400007}")
  private String name;
}
