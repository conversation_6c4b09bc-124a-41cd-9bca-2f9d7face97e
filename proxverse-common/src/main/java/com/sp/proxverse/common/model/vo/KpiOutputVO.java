package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("KPI列表列表VO对象")
public class KpiOutputVO {
  @Tolerate
  public KpiOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "当前kpi所属的业务主题ID")
  private Integer topicId;

  @ApiModelProperty(value = "kpiId")
  private Integer kpiId;

  @ApiModelProperty(value = "kpiId")
  private Integer kpiType;

  @ApiModelProperty(value = "kpi名称")
  private String name;

  @ApiModelProperty(value = "kpi名称")
  private String expression;

  @ApiModelProperty(value = "kpi单位")
  private String unit;

  @ApiModelProperty(value = "基线")
  private String baseLine;
}
