package com.sp.proxverse.common.model.dict;

import com.sp.proxverse.common.exception.BizException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum DataAuthDataIdTypeEnum {
  TOPIC(1, "业务主题"),
  DATA_POOL(20, "数据池"),
  ;

  private final Integer value;
  private final String name;

  DataAuthDataIdTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static boolean isTopic(Integer value) {
    return Objects.equals(value, TOPIC.value);
  }

  public static boolean isDataPool(Integer value) {
    return Objects.equals(value, DATA_POOL.value);
  }

  public static DataAuthDataIdTypeEnum getDataAuthDataIdTypeEnumByValue(Integer dataType) {
    for (DataAuthDataIdTypeEnum value : DataAuthDataIdTypeEnum.values()) {
      if (value.getValue().equals(dataType)) {
        return value;
      }
    }
    throw new BizException(5000, "dataType match failed: " + dataType);
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
