package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.dto.process.VariantCalculateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("流程树VO对象")
public class ProcessTreeOutputDTO {

  @Tolerate
  public ProcessTreeOutputDTO() {
    // comment empty
  }

  @ApiModelProperty("树结点集合")
  private List<TreeNode> treeNodeList;

  @ApiModelProperty("指向结点集合")
  private List<PointerDTO> pointerList;

  @ApiModelProperty("变体集合")
  private List<VariantResultDTO> variantList;

  @ApiModelProperty("变体过滤计算结果（变体下面两个圆圈里的数据）")
  private VariantCalculateDTO variantCalculateDTO;

  private String currentEventColumn;

  private String errorMsg;
}
