package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("过滤项事件是否选中VO对象")
public class EventVariableOutputVO {
  @Tolerate
  public EventVariableOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "当前业务主题数据名称")
  private String eventName;

  @ApiModelProperty(value = "0：未选中，1：选中")
  private Integer type;
}
