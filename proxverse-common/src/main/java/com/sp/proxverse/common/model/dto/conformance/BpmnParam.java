package com.sp.proxverse.common.model.dto.conformance;

import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.dict.conformance.TempNodeTypeEnum;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;
import org.apache.spark.unsafe.types.UTF8String;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Tuple2;

@Setter
@Getter
@Builder
public class BpmnParam {

  private static final Logger logger = LoggerFactory.getLogger(BpmnParam.class);

  @Tolerate
  public BpmnParam() {
    // comment empty
  }

  private List<String> places;

  private List<List<String>> transitions;

  private List<List<String>> edges;

  public PetriParam buildConformanceParam() throws Exception {
    PetriParam param = new PetriParam();
    param.setPlacesList(new HashSet<>(this.getPlaces()));
    Set<Tuple2<UTF8String, Integer>> transitionsLocal =
        this.getTransitions().stream()
            .map(m -> new Tuple2<>(UTF8String.fromString(m.get(0)), Integer.valueOf(m.get(1))))
            .collect(Collectors.toSet());
    param.setTransitionList(new HashSet<>(transitionsLocal));
    List<List<String>> edgesLocal = this.getEdges();
    List<List<String>> sourceList =
        edgesLocal.stream()
            .filter(f -> Objects.equals(f.get(0), "source"))
            .collect(Collectors.toList());
    if (sourceList.isEmpty()) {
      throw new BizException(5000, "not found start node");
    }
    List<String> start = new ArrayList<>();
    for (List<String> source : sourceList) {
      findStart(start, source.get(1), this);
    }
    List<List<String>> endList =
        edgesLocal.stream()
            .filter(f -> Objects.equals(f.get(1), "sink"))
            .collect(Collectors.toList());
    if (endList.isEmpty()) {
      throw new Exception("not found end node");
    }
    List<String> end = new ArrayList<>();
    for (List<String> source : endList) {
      findend(end, source.get(0), this);
    }
    List<Edges> edgeList = new ArrayList<>();
    for (List<String> edge : edgesLocal) {
      edgeList.add(Edges.builder().source(edge.get(0)).target(edge.get(1)).build());
    }
    param.setStartList(new HashSet<>(start));
    param.setEndList(new HashSet<>(end));
    param.setEdgesList(edgeList);
    return param;
  }

  private void findStart(List<String> start, String next, BpmnParam bpmnParam) {
    long count =
        bpmnParam.getTransitions().stream()
            .filter(
                f ->
                    Objects.equals(f.get(0), next)
                        && Objects.equals(f.get(1), TempNodeTypeEnum.NOT_TEMP.getValue()))
            .count();
    if (count > 0) {
      // current node is start node
      start.add(next);
    } else {
      // current node is temp not start node
      List<List<String>> sourceList =
          bpmnParam.getEdges().stream()
              .filter(f -> Objects.equals(f.get(0), next))
              .collect(Collectors.toList());
      for (List<String> sourceNext : sourceList) {
        findStart(start, sourceNext.get(1), bpmnParam);
      }
    }
  }

  private void findend(List<String> end, String pre, BpmnParam bpmnParam) {
    long count =
        bpmnParam.getTransitions().stream()
            .filter(
                f ->
                    Objects.equals(f.get(0), pre)
                        && Objects.equals(f.get(1), TempNodeTypeEnum.NOT_TEMP.getValue()))
            .count();
    if (count > 0) {
      // current node is end node
      end.add(pre);
    } else {
      // current node is temp not end node
      List<List<String>> endList =
          bpmnParam.getEdges().stream()
              .filter(f -> Objects.equals(f.get(1), pre))
              .collect(Collectors.toList());
      for (List<String> sourcePre : endList) {
        findend(end, sourcePre.get(0), bpmnParam);
      }
    }
  }

  @Override
  public String toString() {
    return "BpmnParam{"
        + "places="
        + places
        + ", transitions="
        + transitions
        + ", edges="
        + edges
        + '}';
  }
}
