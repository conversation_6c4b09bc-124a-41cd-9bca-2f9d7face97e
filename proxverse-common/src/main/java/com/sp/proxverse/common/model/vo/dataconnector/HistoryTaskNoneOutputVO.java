package com.sp.proxverse.common.model.vo.dataconnector;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取任务历史未执行列表VO对象")
public class HistoryTaskNoneOutputVO {
  @Tolerate
  public HistoryTaskNoneOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据任务Id")
  private Integer taskId;

  @ApiModelProperty(value = "开始时间")
  private String nextTime;

  @ApiModelProperty(value = "数据任务名称")
  private String taskName;

  @ApiModelProperty(value = "执行频率")
  private String execRate;
}
