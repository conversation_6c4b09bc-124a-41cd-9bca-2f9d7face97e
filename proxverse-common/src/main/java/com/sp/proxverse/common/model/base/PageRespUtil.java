package com.sp.proxverse.common.model.base;

import com.sp.proxverse.common.model.page.PageRespList;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.Objects;
import lombok.Data;

@Data
@ApiModel("分页请求返回数据util")
public class PageRespUtil<T> implements Serializable {

  public static Boolean isSucc(PageResp pageResp) {
    return Objects.nonNull(pageResp)
        && Objects.equals(pageResp.getCode(), 2000)
        && Objects.nonNull(pageResp.getData());
  }

  public static Boolean isSuccess(PageResponse pageResp) {
    return Objects.nonNull(pageResp)
        && Objects.equals(pageResp.getCode(), 2000)
        && Objects.nonNull(pageResp.getData());
  }

  public static Boolean isSucc(PageResponse pageResp) {
    return isSuccess(pageResp);
  }

  public static Boolean isSuccess(PageRespList pageResp) {
    return Objects.nonNull(pageResp) && Objects.nonNull(pageResp.getList());
  }
}
