package com.sp.proxverse.common.model.dict;

public enum TaskLogEnum {
  RUN_TYPE_AUTOMATIC(0, "自动运行"),
  RUN_TYPE_HANDLE(1, "手动运行"),
  STATUS_SUCCESS(2, "完成"),
  STATUS_LOADING(3, "运行中"),
  STATUS_WAIT(4, "等待"),
  STATUS_FAIL(5, "失败"),
  ;

  private final Integer value;
  private final String name;

  TaskLogEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
