package com.sp.proxverse.common.model.vo.processExplorer;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @create 2023-05-08 18:09
 */
@Builder
@Data
public class EventNode implements Serializable {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty("当前事件所属的渐变值")
  public Integer gradient;

  private String name;

  private Long number;

  @ApiModelProperty(value = "节点之间显示的数据")
  private String second;

  @ApiModelProperty("颜色")
  private String color;

  private List<String> kpiValues;

  @ApiModelProperty("是否是开始")
  private Boolean isStart;

  @ApiModelProperty("是否是结束")
  private Boolean isEnd;

  @ApiModelProperty("当前事件个数占所有事件个数的比例")
  private String eventRatio;

  @ApiModelProperty("当前事件所在的流程中占比")
  private String eventCaseRatio;

  @ApiModelProperty("平均每个案例发生概率")
  private String occursOnAveragePerCase;

  @Tolerate
  public EventNode() {
    // comment empty
  }

  public String getColor() {
    return color;
  }

  public void setColor(String color) {
    this.color = color;
  }

  public List<String> getKpiValues() {
    return kpiValues;
  }

  public void setKpiValues(List<String> kpiValues) {
    this.kpiValues = kpiValues;
  }

  public Boolean getStart() {
    return isStart;
  }

  public Boolean getEnd() {
    return isEnd;
  }

  public Integer getGradient() {
    return gradient;
  }

  public void setGradient(Integer gradient) {
    this.gradient = gradient;
  }

  public String getEventRatio() {
    return eventRatio;
  }

  public void setEventRatio(String eventRatio) {
    this.eventRatio = eventRatio;
  }

  public Long getNumber() {
    return number;
  }

  public void setNumber(Long number) {
    this.number = number;
  }

  public String getOccursOnAveragePerCase() {
    return occursOnAveragePerCase;
  }

  public void setOccursOnAveragePerCase(String occursOnAveragePerCase) {
    this.occursOnAveragePerCase = occursOnAveragePerCase;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getEventCaseRatio() {
    return eventCaseRatio;
  }

  public void setEventCaseRatio(String eventCaseRatio) {
    this.eventCaseRatio = eventCaseRatio;
  }
}
