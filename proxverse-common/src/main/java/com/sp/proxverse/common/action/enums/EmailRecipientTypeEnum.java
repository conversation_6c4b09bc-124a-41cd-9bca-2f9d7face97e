package com.sp.proxverse.common.action.enums;

import javax.mail.Message;
import javax.mail.internet.MimeMessage;

/**
 * 邮件收件人类型
 *
 * <AUTHOR>
 * @create 2022-05-31 2:14 下午
 */
public enum EmailRecipientTypeEnum {
  /** 收件人类型 */
  PRIMARY("primary", 1000, "主要收件人", MimeMessage.RecipientType.TO),

  CARBON_COPY("carbonCopy", 2000, "抄送人", MimeMessage.RecipientType.CC),

  BLIND_CARBON_COPY("blindCarbonCopy", 3000, "秘密抄送人", MimeMessage.RecipientType.BCC);

  private Integer code;

  private String name;

  private String describe;

  private Message.RecipientType recipientType;

  EmailRecipientTypeEnum(
      String name, Integer code, String describe, Message.RecipientType recipientType) {
    this.code = code;
    this.name = name;
    this.describe = describe;
    this.recipientType = recipientType;
  }

  public Integer getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

  public String getDescribe() {
    return describe;
  }

  public Message.RecipientType getRecipientType() {
    return recipientType;
  }
}
