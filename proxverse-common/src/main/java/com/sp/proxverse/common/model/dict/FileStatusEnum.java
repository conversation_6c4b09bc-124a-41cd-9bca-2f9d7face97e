package com.sp.proxverse.common.model.dict;

import com.sp.proxverse.common.exception.BizException;
import java.util.Objects;

public enum FileStatusEnum {
  NEW(0),
  LOADING(1),
  LOADED(2),
  FAILED(3),
  ;

  private final Integer value;

  FileStatusEnum(Integer value) {
    this.value = value;
  }

  public static String getNameByCode(Integer code) {
    if (Objects.isNull(code)) {
      throw new BizException(5000, "code is null");
    }
    FileStatusEnum[] values = FileStatusEnum.values();
    for (FileStatusEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value.name();
      }
    }
    throw new BizException(5000, "code not found");
  }

  public Integer getValue() {
    return value;
  }
}
