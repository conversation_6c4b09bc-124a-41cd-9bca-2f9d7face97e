package com.sp.proxverse.common.model.dict;

import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/23 18:05
 */
@Getter
public enum OccurrenceTypeEnum {
  FIRST_OCCURRENCE(10, "FIRST_OCCURRENCE", "第一次发生"),
  LAST_OCCURRENCE(20, "LAST_OCCURRENCE", "最后一次发生"),
  ;

  private final Integer code;

  private final String expression;

  private final String name;

  OccurrenceTypeEnum(Integer code, String expression, String name) {
    this.code = code;
    this.expression = expression;
    this.name = name;
  }

  public static OccurrenceTypeEnum findByCode(Integer code) {
    if (Objects.isNull(code)) {
      return null;
    }

    OccurrenceTypeEnum[] values = OccurrenceTypeEnum.values();
    for (OccurrenceTypeEnum value : values) {
      if (Objects.equals(value.getCode(), code)) {
        return value;
      }
    }
    return null;
  }

  public String getExpression() {
    return expression;
  }

  public Integer getCode() {
    return code;
  }

  public String getName() {
    return name;
  }
}
