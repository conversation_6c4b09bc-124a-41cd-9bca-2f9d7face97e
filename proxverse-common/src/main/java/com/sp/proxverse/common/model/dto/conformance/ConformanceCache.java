package com.sp.proxverse.common.model.dto.conformance;

import com.sp.proxverse.common.model.po.AllowProcessPO;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
public class ConformanceCache {

  @Tolerate
  public ConformanceCache() {
    // comment empty
  }

  private List<String> variantList;

  private List<String> conformanceVariantList;

  private List<UnConformance> unConformanceList;

  private List<UnConformance> allowUnconformanceList;

  private List<AllowProcessPO> allowProcessList;

  private List<UnConformance> allowList;

  private Map<String, Integer> variantToID;

  private String unconformanceSparkTableName;
}
