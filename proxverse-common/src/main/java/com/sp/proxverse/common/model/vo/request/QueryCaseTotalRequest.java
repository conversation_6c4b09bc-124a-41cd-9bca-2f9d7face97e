package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询案例总数请求对象")
public class QueryCaseTotalRequest {

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  private Integer sheetId;
}
