package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("设置文件内字段数据类型请求对象")
public class SetFieldTypeRequest {

  @ApiModelProperty(value = "字段名称", required = true)
  private String field;

  public String getField() {
    return this.field;
  }

  @ApiModelProperty(value = "数据类型ID，getFileDataList接口返回的dataType字段,v1.1修改", required = true)
  @NotNull(message = "{400006}")
  private Integer fieldType;

  @ApiModelProperty(value = "时间格式，当fieldType=5/6/7时，此字段必填 1.6.3改动", required = true)
  private String dateFormat;

  @ApiModelProperty(value = "表达式", required = true)
  private String expression;

  @ApiModelProperty(value = "主键标识，此字段 2.3.1 改动", required = true)
  private String isPrimaryKey;

  @ApiModelProperty(value = "时间key，此字段 2.3.1 改动", required = true)
  private String isUpdateTime;
}
