package com.sp.proxverse.common.model.vo.processai.request;

import com.sp.proxverse.common.model.vo.processai.TopicProcessDetailsKpi;
import io.swagger.annotations.ApiModel;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-02-09 17:03
 */
@Data
@ApiModel("流程图KPI详细信息响应")
public class TopicProcessDetailsReqVo {

  private Integer processKpiId;
  private String name;

  private String icon;

  private String bindEventKpiName;

  private String bindLineKpiName;

  private String eventColourInfo;

  private String lineColourInfo;

  private List<TopicProcessDetailsKpi> eventKpiInfos = new ArrayList<>();
  private List<TopicProcessDetailsKpi> lineKpiInfos = new ArrayList<>();

  private List<TopicProcessDetailsKpi> pathKpiInfos = new ArrayList<>();
}
