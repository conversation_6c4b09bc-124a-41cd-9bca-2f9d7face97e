package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-11 15:42
 */
@Data
@ApiModel("获取数据提取请求")
public class DataExtractorByConnectIdReq {

  @NotNull(message = "{400006}")
  private Integer pool;

  @ApiModelProperty("数据链接Id，可以为空，为空查询所有")
  private Integer connectId;
}
