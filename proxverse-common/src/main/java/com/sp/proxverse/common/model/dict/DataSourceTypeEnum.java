package com.sp.proxverse.common.model.dict;

import java.util.Objects;

public enum DataSourceTypeEnum {
  DATA_MODEL(1, "数据模型"),
  KNOWNLEADE_MODEL(2, "知识模型"),
  ;

  private final Integer value;
  private final String name;

  DataSourceTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static boolean isDataModel(Integer value) {
    return Objects.equals(value, DATA_MODEL.getValue());
  }

  public static boolean isKnowledgeModel(Integer value) {
    return Objects.equals(value, KNOWNLEADE_MODEL.getValue());
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
