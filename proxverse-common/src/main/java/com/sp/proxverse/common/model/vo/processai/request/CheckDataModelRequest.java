package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("检查当前所引用的数据模型是否可用请求对象")
public class CheckDataModelRequest {

  @ApiModelProperty(value = "当前topicsheetId", required = true)
  //    @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private Integer topicId;

  private Integer modelId;
}
