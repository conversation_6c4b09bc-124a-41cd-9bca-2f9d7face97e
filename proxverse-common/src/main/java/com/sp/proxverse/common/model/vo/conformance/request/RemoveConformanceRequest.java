package com.sp.proxverse.common.model.vo.conformance.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("从一致性流程移除请求对象")
public class RemoveConformanceRequest {

  @ApiModelProperty(value = "表格sheetID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  @ApiModelProperty(value = "变体", required = true)
  @NotNull(message = "{400009}")
  private String variant;
}
