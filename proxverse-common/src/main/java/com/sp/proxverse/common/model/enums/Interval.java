package com.sp.proxverse.common.model.enums;

import static org.apache.spark.sql.catalyst.util.DateTimeConstants.HOURS_PER_DAY;
import static org.apache.spark.sql.catalyst.util.DateTimeConstants.MINUTES_PER_HOUR;

import com.google.common.collect.Lists;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.Tolerate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Getter
@Builder
public class Interval {

  @Tolerate
  public Interval() {
    // comment empty
  }

  private static final Integer MONDAY = 0;
  private static final Integer TUESDAY = 1;
  private static final Integer WEDNESDAY = 2;
  private static final Integer THURSDAY = 3;
  private static final Integer FRIDAY = 4;
  private static final Integer SATURDAY = 5;
  private static final Integer SUNDAY = 6;

  public enum IntervalEnum {
    MONDAY(0, Lists.newArrayList(Interval.MONDAY)),
    TUESDAY(1, Lists.newArrayList(Interval.TUESDAY)),
    WEDNESDAY(2, Lists.newArrayList(Interval.WEDNESDAY)),
    THURSDAY(3, Lists.newArrayList(Interval.THURSDAY)),
    FRIDAY(4, Lists.newArrayList(Interval.FRIDAY)),
    SATURDAY(5, Lists.newArrayList(Interval.SATURDAY)),
    SUNDAY(6, Lists.newArrayList(Interval.SUNDAY)),
    WORKDAY(
        7,
        Lists.newArrayList(
            Interval.MONDAY,
            Interval.TUESDAY,
            Interval.WEDNESDAY,
            Interval.THURSDAY,
            Interval.FRIDAY)),
    ALLDAY(
        8,
        Lists.newArrayList(
            Interval.MONDAY,
            Interval.TUESDAY,
            Interval.WEDNESDAY,
            Interval.THURSDAY,
            Interval.FRIDAY,
            Interval.SATURDAY,
            Interval.SUNDAY));
    private int type;
    private List<Integer> days;

    IntervalEnum(int type, List<Integer> days) {
      this.type = type;
      this.days = days;
    }
  }

  public boolean inRange(long currentTime) {
    int time = (int) (currentTime % (MINUTES_PER_HOUR * HOURS_PER_DAY));
    int currentDay = (int) ((currentTime / (MINUTES_PER_HOUR * HOURS_PER_DAY)) % 7);
    if (time >= beginTime && time < endTime) {
      for (IntervalEnum intervalEnum : intervalEnums) {
        if (intervalEnum.days.contains(currentDay)) {
          return true;
        }
      }
    }
    return false;
  }

  public static final Timestamp ZERO_DAY = new Timestamp(1659283200);

  long beginTime;

  long endTime;

  int type;

  int runDate;

  List<IntervalEnum> intervalEnums;

  private Long resourceRunTime;

  public Long getResourceRunTime() {
    Long result = 0L;

    int multiple = runDate / 7;
    int remainder = runDate % 7;

    if (intervalEnums.contains(IntervalEnum.ALLDAY)) {
      result = runDate * (endTime - beginTime);
      return result;
    }

    if (intervalEnums.contains(IntervalEnum.WORKDAY)) {
      if (remainder >= 5) {
        remainder = 5;
      }
      result = remainder * (endTime - beginTime);
      result = result + multiple * 5 * (endTime - beginTime);
      return result;
    }

    Long week = 0L;
    if (multiple > 0) {
      week = getRunTime(week);
    }

    result = getRunTime(result);
    if (multiple >= 1) {
      result = result + week * multiple;
    }

    return result;
  }

  private Long getRunTime(Long result) {
    for (IntervalEnum intervalEnum : intervalEnums) {
      for (Integer day : intervalEnum.days) {
        result = result + (endTime - beginTime);
      }
    }
    return result;
  }

  public boolean isTimeArrived(long currentTime) {
    int time = (int) (currentTime % (MINUTES_PER_HOUR * HOURS_PER_DAY));
    int currentDay = (int) ((currentTime / (MINUTES_PER_HOUR * HOURS_PER_DAY)) % 7);

    if (time == beginTime) {
      for (IntervalEnum intervalEnum : intervalEnums) {
        if (intervalEnum.days.contains(currentDay)) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * intervalEnums 转String
   *
   * @param intervalEnums
   * @return
   */
  public static String intervalEnumConvertString(List<IntervalEnum> intervalEnums) {
    if (CollectionUtils.isNotEmpty(intervalEnums)) {
      StringBuilder sb = new StringBuilder();
      for (Interval.IntervalEnum intervalEnum : intervalEnums) {
        sb.append(intervalEnum.name());
        sb.append("@");
      }
      return sb.deleteCharAt(sb.length() - 1).toString();
    }
    return null;
  }

  /**
   * String 转intervalEnums
   *
   * @param str
   * @return
   */
  public static List<IntervalEnum> stringConvertIntervalEnum(String str) {
    List<IntervalEnum> result = new ArrayList<>();
    if (StringUtils.isBlank(str)) {
      return result;
    }
    String[] splits = str.split("@");

    for (String split : splits) {
      for (IntervalEnum value : IntervalEnum.values()) {
        if (Objects.equals(value.name(), split)) {
          result.add(value);
          continue;
        }
      }
    }

    if (result.contains(IntervalEnum.ALLDAY)) {
      result.clear();
      result.add(IntervalEnum.ALLDAY);

      return result;
    }

    if (result.contains(IntervalEnum.WORKDAY)) {
      result.clear();
      result.add(IntervalEnum.WORKDAY);
      return result;
    }

    return result;
  }
}
