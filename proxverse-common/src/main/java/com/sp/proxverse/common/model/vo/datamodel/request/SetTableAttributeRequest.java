package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("设置为案例表请求对象")
public class SetTableAttributeRequest {

  @ApiModelProperty(value = "数据模型ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataModelId;

  @ApiModelProperty(value = "案例表的表ID", required = true)
  @NotNull(message = "{400006}")
  private Integer fileId;

  private Integer poolId;

  private Integer caseId;

  private Integer event;

  @ApiModelProperty(value = "案例表的开始时间，非必须的字段")
  private Integer startTime;

  @ApiModelProperty(value = "案例表的结束时间，必须的字段")
  private Integer time;

  private List<Integer> sortingList;
}
