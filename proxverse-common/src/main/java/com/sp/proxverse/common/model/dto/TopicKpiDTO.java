package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("指挥舱kpi显示DTO")
public class TopicKpiDTO {

  @Tolerate
  public TopicKpiDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "kpi名称")
  private String name;

  @ApiModelProperty(value = "箭头指向")
  private Integer upDown;

  @ApiModelProperty(value = "kpi计算结果值")
  private String value;

  @ApiModelProperty(value = "单位")
  private String unit;
}
