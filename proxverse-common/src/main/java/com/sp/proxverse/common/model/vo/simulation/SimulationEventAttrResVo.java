package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-26 12:23
 */
@Data
@ApiModel("流程仿真中事件属性信息响应")
public class SimulationEventAttrResVo {

  private Integer eventId;

  private Integer eventAttrId;

  @ApiModelProperty("运行成本")
  private String runCost;

  @ApiModelProperty("执行时间")
  private String runTim;
}
