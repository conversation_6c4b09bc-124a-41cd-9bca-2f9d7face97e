package com.sp.proxverse.common.model.vo.processai;

import com.sp.proxverse.common.model.dict.KpiSaveTypeEnum;
import io.swagger.annotations.ApiModel;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-20 18:03
 */
@Data
@ApiModel("流程图Kpi")
public class TopicProcessKpiResVo {

  private String name;

  private String icon;

  private Integer processKpiId;

  private List<String> pathKpi;

  private KpiSaveTypeEnum kpiSaveTypeEnum;
}
