package com.sp.proxverse.common.action.bo;

import com.sp.proxverse.common.action.enums.ActionTypeEnum;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-05-09 2:09 下午
 */
@Getter
@Setter
public class ActionBo {

  /** 动作参数 */
  private ActionDoParamBo actionDoParamBo;

  /** 动作参数模版 */
  private String paramStr;

  /** 节点名称 */
  private String actionName;

  /** 当前动作运行结果模版 */
  private ActionDoResultBo actionDoResultBo;

  /** 动作结果,动作结果只有一个对象，其他运行结果要向里面添加 */
  private Map<Integer, ActionDoResultBo> actionResultRecord;

  /** 动作类型 */
  private ActionTypeEnum actionTypeEnum;

  /** 起始标识 */
  private Boolean startFlag;

  /** 结束标识 */
  private Boolean endFlag;

  /** 动作流Id */
  private Integer actionFlowId;

  /** 动作Id */
  private Integer actionId;

  /** 动作编码 */
  private Integer actionCode;

  /** 运行编码 */
  private Integer runCode;

  /** 单节点运行标识 */
  private Boolean singleNodeFlag;

  /** 孩子节点 */
  private List<ActionBo> childActions;

  /** 该节点是否需要过滤： 0：需要，1：不需要 */
  private Boolean filterFlag;

  /** 过滤文本 */
  private List<FilterParamBo> filterParams;

  /** 数据来源：1 ：正常生成，2：snapshot生成 */
  private Integer sourceType;

  /** 前端node信息 */
  private String nodeInfo;

  public ActionBo() {
    this.actionResultRecord = new HashMap<>();
    this.childActions = new ArrayList<>();
    this.singleNodeFlag = false;
    this.filterFlag = false;
  }

  /**
   * 添加一个结果集
   *
   * @param actionDoResultBo
   */
  public void addActionDoResultBo(ActionDoResultBo actionDoResultBo) {
    this.actionResultRecord.put(this.actionCode, actionDoResultBo);
  }

  public ActionBo getChildAction() {
    return this.childActions.get(0);
  }

  public void addChildAction(ActionBo actionBoChild) {
    this.childActions.add(actionBoChild);
  }

  public List<ActionDoResultBo> getActionResultRecordFormat() {
    List<ActionDoResultBo> actionDoResultBos = new ArrayList<>();
    for (Map.Entry<Integer, ActionDoResultBo> integerActionDoResultBoEntry :
        this.actionResultRecord.entrySet()) {
      actionDoResultBos.add(integerActionDoResultBoEntry.getValue());
    }
    return actionDoResultBos;
  }
}
