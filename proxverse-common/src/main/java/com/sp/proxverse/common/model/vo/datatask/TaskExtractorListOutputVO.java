package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据任务子链接列表VO对象")
public class TaskExtractorListOutputVO {
  @Tolerate
  public TaskExtractorListOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "任务提取ID")
  private Integer taskExtractorId;

  @ApiModelProperty(value = "任务提取名称")
  private String taskExtractorName;

  @ApiModelProperty(value = "任务提取名称")
  private String dataConnectorName;

  @ApiModelProperty(value = "类型（0：jdbc，1：kafka）")
  private Integer type;

  @ApiModelProperty(value = "状态（0：未开始，1：运行中，2：完成，3：失败）")
  private Integer status;
}
