package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import java.util.LinkedHashMap;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("case数据active字段详情")
public class CaseDetailActiveDTO {

  @Tolerate
  public CaseDetailActiveDTO() {
    // comment empty
  }

  private LinkedHashMap<String, String> active = new LinkedHashMap<>();
}
