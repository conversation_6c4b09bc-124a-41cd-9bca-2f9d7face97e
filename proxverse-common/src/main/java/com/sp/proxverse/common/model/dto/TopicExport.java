package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.dto.topicSheet.TopicKpiDto;
import com.sp.proxverse.common.model.dto.topicSheet.TopicParamDto;
import com.sp.proxverse.common.model.po.BusinessTopicPO;
import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.po.ProcessTreeKpiPo;
import com.sp.proxverse.common.model.po.ProcessTreeKpiRelationPo;
import com.sp.proxverse.common.model.po.SheetMenuPO;
import com.sp.proxverse.common.model.po.TempPQLTable;
import com.sp.proxverse.common.model.po.TopicConfigColorPO;
import com.sp.proxverse.common.model.po.TopicConfigPO;
import com.sp.proxverse.common.model.po.TopicFilterBookMarkPO;
import com.sp.proxverse.common.model.po.TopicFilterMarkPO;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Setter
@Builder
public class TopicExport {

  @Tolerate
  public TopicExport() {
    // comment empty
  }

  private BusinessTopicPO topic;

  @ApiModelProperty("通用设置-导出设置")
  private TopicConfigPO topicConfig;

  @ApiModelProperty("通用设置-颜色模板")
  private TopicConfigColorPO topicConfigColor;

  @ApiModelProperty("通用设置-KPI")
  private List<TopicKpiDto> topicKpiSetList;

  @ApiModelProperty("通用设置-过滤规则")
  private TopicFilterPO topicScriptFilter;

  @ApiModelProperty("通用设置-变量")
  private List<TopicParamDto> topicParamSetList;

  @ApiModelProperty("通用设置-流程图KPI")
  private List<ProcessTreeKpiPo> processTreeKpiList;

  @ApiModelProperty("通用设置-流程图KPI")
  private List<ProcessTreeKpiRelationPo> processTreeKpiRelationList;

  @ApiModelProperty("通用设置-流程图KPI")
  private List<KpiPO> processTreeRelationKpiList;

  @ApiModelProperty("通用设置-临时表")
  private List<TempPQLTable> tempPQLTables;

  @ApiModelProperty("topic过滤项")
  private List<TopicFilterPO> topicFilterList;

  @ApiModelProperty("topic的bookmark")
  private List<TopicFilterMarkPO> topicMarkList;

  @ApiModelProperty("topic的bookmark")
  private List<TopicFilterBookMarkPO> topicBookMarkList;

  @ApiModelProperty("bookmark的过滤项")
  private List<TopicFilterPO> bookMarkFilterList;

  @ApiModelProperty("sheet数据")
  private List<TopicSheetPO> sheetList;

  @ApiModelProperty("sheet排序数据")
  private List<SheetMenuPO> sheetMenuList;

  @ApiModelProperty("sheet详情数据")
  private List<TopicSheetExport> sheetExportList;

  public BusinessTopicPO getTopic() {
    return topic;
  }

  public TopicConfigPO getTopicConfig() {
    return topicConfig;
  }

  public TopicConfigColorPO getTopicConfigColor() {
    return topicConfigColor;
  }

  public List<TopicKpiDto> getTopicKpiSetList() {
    return topicKpiSetList == null ? new ArrayList<>() : topicKpiSetList;
  }

  public TopicFilterPO getTopicScriptFilter() {
    return topicScriptFilter;
  }

  public List<TopicParamDto> getTopicParamSetList() {
    return topicParamSetList == null ? new ArrayList<>() : topicParamSetList;
  }

  public List<ProcessTreeKpiPo> getProcessTreeKpiList() {
    return processTreeKpiList == null ? new ArrayList<>() : processTreeKpiList;
  }

  public List<ProcessTreeKpiRelationPo> getProcessTreeKpiRelationList() {
    return processTreeKpiRelationList == null ? new ArrayList<>() : processTreeKpiRelationList;
  }

  public List<KpiPO> getProcessTreeRelationKpiList() {
    return processTreeRelationKpiList == null ? new ArrayList<>() : processTreeRelationKpiList;
  }

  public List<TopicFilterPO> getTopicFilterList() {
    return topicFilterList == null ? new ArrayList<>() : topicFilterList;
  }

  public List<TopicFilterMarkPO> getTopicMarkList() {
    return topicMarkList == null ? new ArrayList<>() : topicMarkList;
  }

  public List<TopicFilterBookMarkPO> getTopicBookMarkList() {
    return topicBookMarkList == null ? new ArrayList<>() : topicBookMarkList;
  }

  public List<TopicFilterPO> getBookMarkFilterList() {
    return bookMarkFilterList == null ? new ArrayList<>() : bookMarkFilterList;
  }

  public List<TopicSheetPO> getSheetList() {
    return sheetList == null ? new ArrayList<>() : sheetList;
  }

  public List<SheetMenuPO> getSheetMenuList() {
    return sheetMenuList == null ? new ArrayList<>() : sheetMenuList;
  }

  public List<TopicSheetExport> getSheetExportList() {
    return sheetExportList == null ? new ArrayList<>() : sheetExportList;
  }

  public List<TempPQLTable> getTempPQLTables() {
    return tempPQLTables == null ? new ArrayList<>() : tempPQLTables;
  }
}
