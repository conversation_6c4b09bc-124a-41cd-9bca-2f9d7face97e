package com.sp.proxverse.common.model.vo.authority;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
public class UserGroupPermissionListDTO {

  @Tolerate
  public UserGroupPermissionListDTO() {
    // comment empty
  }

  private Integer dataId;

  private String name;

  @ApiModelProperty("10:topic主题，11：topic分析,20:pool,30:model")
  private Integer type;

  private String typeName;

  private List<String> permissionList;

  private Integer authorityValue;

  private String expression;

  private List<UserGroupPermissionListDTO> subList;
}
