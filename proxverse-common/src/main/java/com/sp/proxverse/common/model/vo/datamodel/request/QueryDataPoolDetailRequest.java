package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("获取数据池数据链接请求对象")
public class QueryDataPoolDetailRequest {

  @ApiModelProperty(value = "数据池ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataPoolId;

  @ApiModelProperty(value = "请求的数据类型（1：数据模型，2：数据链接，3：数据提取，4：数据任务）", required = true)
  @NotNull(message = "{400008}")
  private Integer type;
}
