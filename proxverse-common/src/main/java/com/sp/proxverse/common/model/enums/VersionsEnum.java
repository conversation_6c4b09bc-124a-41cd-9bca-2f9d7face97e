package com.sp.proxverse.common.model.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022-06-20 2:39 下午
 */
public enum VersionsEnum {
  /** 版本 */
  UNKNOWN(-1),

  SNAP(1000),

  SNAP_PLUS(2000),

  ENTERPRISE(3000),

  INDEPENDENT(4000),
  ;

  private Integer code;

  VersionsEnum(Integer code) {
    this.code = code;
  }

  public Integer getCode() {
    return code;
  }

  public static VersionsEnum getVersionsEnumByCode(Integer code) {
    for (VersionsEnum value : VersionsEnum.values()) {
      if (Objects.equals(value.getCode(), code)) {
        return value;
      }
    }
    return VersionsEnum.UNKNOWN;
  }
}
