package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询待编辑主题的信息VO对象")
public class EditBusinessTopicDataOutputVO {
  @Tolerate
  public EditBusinessTopicDataOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "当前主题ID")
  private Integer topicId;

  @ApiModelProperty(value = "当前业务主题名称")
  private String name;

  @ApiModelProperty(value = "当前数据模型ID")
  private Integer dataModelId;

  /** 知识模型Id */
  private Integer knowledgeModelId;

  @ApiModelProperty("数据源类型（1：data_model，2：knowledge_model）")
  private Integer dataSourceType;

  @ApiModelProperty("数据源名称(给前端默认显示，如果用户没有这个数据模型的数据权限时，前端会找不到name)")
  private String dataSourceName;

  private String poolName;
}
