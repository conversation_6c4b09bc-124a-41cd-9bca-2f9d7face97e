package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询编辑根因分析编辑VO对象")
public class EditRootCauseOutputVO {
  @Tolerate
  public EditRootCauseOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "根因分析ID")
  private Integer causeId;

  @ApiModelProperty(value = "根因分析名称")
  private String causeName;

  @ApiModelProperty(value = "所选的kpiIdList")
  private List<Integer> kpiIdList;

  @ApiModelProperty(value = "所选的kpiIdList名称")
  private List<String> kpiIdListName;

  @ApiModelProperty(value = "相关变量")
  private String variable;

  @ApiModelProperty(value = "排序KPIID")
  private Integer sortKpiId;
}
