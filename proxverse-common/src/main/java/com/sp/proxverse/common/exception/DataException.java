package com.sp.proxverse.common.exception;

/**
 * <AUTHOR>
 * @create 2022-03-07 1:22 下午
 */
public class DataException extends BaseException {
  private static final long serialVersionUID = -1;

  public DataException(ErrorCode ErrorCode) {
    super.setCode(ErrorCode.getCode());
    super.setMessage(ErrorCode.getDesc());
  }

  public DataException(ErrorCode ErrorCode, String message) {
    super.setCode(ErrorCode.getCode());
    super.setMessage(message);
  }

  public DataException(Integer code, String message) {
    super(code, message);
  }
}
