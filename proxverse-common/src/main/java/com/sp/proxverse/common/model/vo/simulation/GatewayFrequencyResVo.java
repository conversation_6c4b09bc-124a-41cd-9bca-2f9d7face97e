package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-21 11:36
 */
@Data
@ApiModel("网关频率信息响应")
public class GatewayFrequencyResVo {

  @ApiModelProperty("网关Id")
  private Integer gatewayId;

  @ApiModelProperty("网关名称")
  private String gatewayName;

  @ApiModelProperty("路由下面的事件")
  private List<GatewayFrequencyInfo> events;
}
