package com.sp.proxverse.common.model.dict.signal;

public enum SignalRuleStatusEnum {
  CLOSE(0, "关闭"),
  RUNNING(1, "运行中"),
  STOP(2, "暂停"),
  ;

  private final Integer value;
  private final String name;

  SignalRuleStatusEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
