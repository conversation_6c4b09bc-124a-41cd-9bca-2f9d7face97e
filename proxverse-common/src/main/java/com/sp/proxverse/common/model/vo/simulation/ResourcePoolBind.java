package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-23 15:56
 */
@Data
@ApiModel("设置仿真资源绑定信息")
public class ResourcePoolBind {
  @ApiModelProperty("事件属性Id")
  private List<Integer> eventIds;

  @ApiModelProperty("资源池Id")
  @NotNull(message = "{400006}")
  private Integer resourcePoolId;
}
