package com.sp.proxverse.common.model.dto.api;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Setter
@Getter
@Builder
public class DataModelFileInfoDTO {

  @Tolerate
  public DataModelFileInfoDTO() {
    // comment empty
  }

  private Integer tableId;

  private String tableName;

  @ApiModelProperty(value = "表类型1：主表，2：次表，3：虚拟case表，此字段为预留字段，暂不作业务使用")
  private Integer tableType;

  @ApiModelProperty(value = "表维度，1：event维度，2：case维度")
  private Integer dimension;

  private List<TableColumnInfoDTO> columnDTOList;
}
