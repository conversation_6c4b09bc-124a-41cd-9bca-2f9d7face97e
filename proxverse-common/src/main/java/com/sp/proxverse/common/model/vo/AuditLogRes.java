package com.sp.proxverse.common.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/8 15:54
 */
@Data
public class AuditLogRes {

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 接口地址 */
  private String path;

  /** 用户名称 */
  private String userName;

  /** 用户name名称 */
  private String name;

  /** 用户Id */
  private Integer userId;

  /** IP地址 */
  private String ip;

  public String createTime;

  /** 租户ID */
  private Integer tenantId;

  /** 请求参数 */
  private String parameter;
}
