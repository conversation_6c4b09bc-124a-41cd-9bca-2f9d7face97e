package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class TreeNode {

  @Tolerate
  public TreeNode() {
    // comment empty
  }

  @ApiModelProperty("节点名称，也叫事件名称")
  private String id;

  @ApiModelProperty("节点名称，也叫事件名称")
  private String label;

  @ApiModelProperty("表示当前节点有多少个，如果是按时间过滤的，则此字段为null")
  private Long value;

  @ApiModelProperty("颜色")
  private String color;

  private List<String> kpiValues;

  @ApiModelProperty(value = "内部流转使用，前端忽略", hidden = true)
  private String innerKey;

  @ApiModelProperty("是否是开始")
  private Boolean isStart;

  @ApiModelProperty("是否是结束")
  private Boolean isEnd;

  public void setKpiValues(List<String> kpiValues) {
    this.kpiValues = kpiValues;
  }
}
