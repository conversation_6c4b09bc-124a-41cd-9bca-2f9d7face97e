package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("用户名称VO对象")
public class UserNameVO {
  @Tolerate
  public UserNameVO() {
    // comment empty
  }

  @ApiModelProperty(value = "ID")
  private Integer userId;

  @ApiModelProperty(value = "名称")
  private String userName;
}
