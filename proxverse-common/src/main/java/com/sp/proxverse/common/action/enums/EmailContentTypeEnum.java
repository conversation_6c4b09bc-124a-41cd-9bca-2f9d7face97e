package com.sp.proxverse.common.action.enums;

/**
 * <AUTHOR>
 * @create 2022-05-31 2:09 下午
 */
public enum EmailContentTypeEnum {
  /** 邮件正文类型 */
  TEXT("text", 1000, "文本类型", "text/html;charset=UTF-8"),
  HTML("html", 2000, "HTML类型", "text/html;charset=UTF-8");

  private Integer code;

  private String name;

  private String describe;

  private String type;

  EmailContentTypeEnum(String name, Integer code, String describe, String type) {
    this.code = code;
    this.name = name;
    this.describe = describe;
    this.type = type;
  }

  public Integer getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

  public String getDescribe() {
    return describe;
  }

  public String getType() {
    return type;
  }
}
