package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("主题表格数据(kpi)请求对象")
public class TopicSheetDataRequest {

  @ApiModelProperty(value = "业务主题表格Id", required = true)
  private Integer topicSheetId;

  @ApiModelProperty(value = "topicId:传入此值时将该topic下面所有KPI查询", required = true)
  private Integer topicId;
}
