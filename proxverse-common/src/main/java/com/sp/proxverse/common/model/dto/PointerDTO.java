package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("树形指向DTO")
public class PointerDTO {

  @Tolerate
  public PointerDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "起始事件名称")
  private String source;

  @ApiModelProperty(value = "目标事件名称")
  private String target;

  @ApiModelProperty(value = "节点之间的秒数")
  private String second;

  private String ratio;

  private Long value;

  private Boolean loadFlag;

  @ApiModelProperty(value = "线宽")
  private Double penwidth;

  @ApiModelProperty(value = "颜色")
  private String color;

  @ApiModelProperty(value = "KPI结果集")
  private List<String> kpiValues;

  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public String getTarget() {
    return target;
  }

  public void setTarget(String target) {
    this.target = target;
  }

  public String getSecond() {
    return second;
  }

  public void setSecond(String second) {
    this.second = second;
  }

  public String getRatio() {
    return ratio;
  }

  public void setRatio(String ratio) {
    this.ratio = ratio;
  }

  public Long getValue() {
    return value;
  }

  public void setValue(Long value) {
    this.value = value;
  }

  public Boolean getLoadFlag() {
    return loadFlag;
  }

  public void setLoadFlag(Boolean loadFlag) {
    this.loadFlag = loadFlag;
  }

  public Double getPenwidth() {
    return penwidth;
  }

  public void setPenwidth(Double penwidth) {
    this.penwidth = penwidth;
  }

  public String getColor() {
    return color;
  }

  public void setColor(String color) {
    this.color = color;
  }

  public List<String> getKpiValues() {
    return kpiValues;
  }

  public void setKpiValues(List<String> kpiValues) {
    this.kpiValues = kpiValues;
  }
}
