package com.sp.proxverse.common.model.base;

import com.sp.proxverse.common.model.page.PageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel("请求返回数据")
public class PageResponse<T extends Serializable> implements Serializable {

  private static final long serialVersionUID = -5661206328302037980L;

  @ApiModelProperty("错误码")
  private Integer code;

  @ApiModelProperty("错误信息")
  private String msg;

  @ApiModelProperty("业务数据")
  private PageRespDTO<T> data;

  public PageResponse() {
    // comment empty
  }

  public static PageResponse success(PageRespDTO data) {
    PageResponse rtn = new PageResponse();
    rtn.setCode(2000);
    rtn.setData(data);
    return rtn;
  }
}
