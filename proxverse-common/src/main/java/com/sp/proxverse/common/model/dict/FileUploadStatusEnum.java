package com.sp.proxverse.common.model.dict;

import com.sp.proxverse.common.util.I18nUtil;
import java.util.Objects;

public enum FileUploadStatusEnum {
  FAILED(0, "200200"),
  LOADED(1, "200201"),
  LOAD(2, "200204"),
  ;

  private final Integer value;
  private final String name;

  FileUploadStatusEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    FileUploadStatusEnum[] values = FileUploadStatusEnum.values();
    for (FileUploadStatusEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return I18nUtil.getMessage(value.getName());
      }
    }
    return "";
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
