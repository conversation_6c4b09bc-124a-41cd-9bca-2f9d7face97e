package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("根因分析编辑对象")
public class RootCauseEditRequest {

  @ApiModelProperty(value = "根因ID", required = true)
  @NotNull(message = "{400006}")
  private Integer causeId;

  @ApiModelProperty(value = "名称，创建时必填", required = true)
  @NotBlank(message = "{400007}")
  private String name;

  @ApiModelProperty(value = "相关变量", required = true)
  @NotEmpty(message = "{400003}")
  private List<VariableSaveSubRequest> variableList;

  @ApiModelProperty(value = "选择kpi", required = true)
  @NotEmpty(message = "{400004}")
  private List<Integer> kpiList;

  @ApiModelProperty(value = "排序规则，事实上是kpiId", required = true)
  @NotNull(message = "{400005}")
  private Integer sort;
}
