package com.sp.proxverse.common.model.vo.useranalyse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("业务主题列表VO对象")
public class UserStatisticsOutputVO {
  @Tolerate
  public UserStatisticsOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "活跃员工")
  private Integer activeNum;

  @ApiModelProperty(value = "员工事件数")
  private Integer userEventNum;

  @ApiModelProperty(value = "员工参与案例数")
  private Integer userCaseNum;

  @ApiModelProperty(value = "每个案例的员工数")
  private Integer userNumPerCase;
}
