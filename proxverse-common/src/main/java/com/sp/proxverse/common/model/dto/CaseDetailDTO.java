package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("case数据字段详情（案例视图outputVO对象字段）")
public class CaseDetailDTO {

  @Tolerate
  public CaseDetailDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "事件名称")
  private String eventName;

  @ApiModelProperty(value = "发生时间")
  private String time;

  @ApiModelProperty(value = "开始时间")
  private String startTime;

  @ApiModelProperty(value = "时间间隔")
  private String timeSpace;

  @ApiModelProperty(value = "时间显示单位")
  private String unit;

  private Integer count;
}
