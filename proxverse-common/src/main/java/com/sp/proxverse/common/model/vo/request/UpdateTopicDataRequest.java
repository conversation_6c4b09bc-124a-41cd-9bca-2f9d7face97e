package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("修改业务主题数据模型请求实体类")
public class UpdateTopicDataRequest {

  @Tolerate
  public UpdateTopicDataRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "业务主题ID")
  @NotNull(message = "{400006}")
  private Integer topicId;

  private String name;

  @ApiModelProperty(value = "选择的数据源类型，目前只有在创建业务分析图谱时需要传值（1：data_model，2：knowledge_model）")
  @NotNull(message = "{400008}")
  private Integer dataSourceType;

  @ApiModelProperty(value = "选择的数据源ID，目前最多有一个元素")
  @NotNull(message = "{400006}")
  private Integer dataId;
}
