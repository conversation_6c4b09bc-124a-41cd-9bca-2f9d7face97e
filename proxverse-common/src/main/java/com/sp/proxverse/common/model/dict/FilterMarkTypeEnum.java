package com.sp.proxverse.common.model.dict;

public enum FilterMarkTypeEnum {
  TOPIC_FILTER(0, "原始filter"),
  BOOK_MARK(1, "bookmark的filter"),
  ;

  private final Integer value;
  private final String name;

  FilterMarkTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
