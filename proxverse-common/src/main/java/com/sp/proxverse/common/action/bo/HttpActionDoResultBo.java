package com.sp.proxverse.common.action.bo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-05-12 3:00 下午
 */
@Data
public class HttpActionDoResultBo extends ActionDoResultBo {

  /** 浏览器状态 */
  private String statusCode;

  /** 请求头 */
  private Map<String, List<String>> headers;

  /** cookies */
  private List<String> cookies;

  /** 数据 */
  private Object data;

  public HttpActionDoResultBo() {
    super();
    this.headers = new ConcurrentHashMap<>();
    this.cookies = new ArrayList<>();
  }
}
