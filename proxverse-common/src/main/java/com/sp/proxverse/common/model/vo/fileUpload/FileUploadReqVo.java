package com.sp.proxverse.common.model.vo.fileUpload;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @create 2022-09-05 11:17 上午
 */
@Data
@ApiModel
public class FileUploadReqVo implements Serializable {
  /** 当前文件块，从1开始 */
  private Integer chunkNumber;
  /** 分块大小 */
  private Long chunkSize;
  /** 当前分块大小 */
  private Long currentChunkSize;
  /** 总大小 */
  private Long totalSize;
  /** 文件标识 */
  private String identifier;
  /** 文件名 */
  private String filename;
  /** 相对路径 */
  private String relativePath;
  /** 总块数 */
  private Integer totalChunks;

  /** 二进制文件 */
  @JsonIgnore private transient MultipartFile file;

  @ApiModelProperty("数据池Id")
  String dataPoolId;
}
