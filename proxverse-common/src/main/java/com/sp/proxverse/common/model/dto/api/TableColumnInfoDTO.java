package com.sp.proxverse.common.model.dto.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
public class TableColumnInfoDTO {

  @Tolerate
  public TableColumnInfoDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "字段ID")
  private Integer id;

  @ApiModelProperty(value = "字段名称")
  private String name;

  @ApiModelProperty(value = "字段ID")
  private Integer fileId;

  @ApiModelProperty(value = "字段名称")
  private String fileName;

  @ApiModelProperty(value = "字段数据类型，见DataTypeEnum")
  private Integer type;

  @ApiModelProperty(value = "字段维度，1：event维度，2：case维度")
  private Integer dimension;

  @ApiModelProperty(value = "是否勾选")
  private Boolean selected;
}
