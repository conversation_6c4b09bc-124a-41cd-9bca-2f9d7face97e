package com.sp.proxverse.common.model.vo.datatask;

import com.sp.proxverse.common.model.dict.TaskRateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("创建数据任务请求对象")
public class SaveDataTaskRequest {

  @ApiModelProperty(value = "数据提取名称", required = true)
  @NotNull(message = "{400007}")
  private String name;

  @ApiModelProperty(value = "数据池ID", required = true)
  @NotNull(message = "{400006}")
  private Integer poolId;

  /** @see TaskRateTypeEnum */
  @ApiModelProperty(value = "任务频率（0：手动执行，1：每小时，2：每天，3：每周, 4 cron）", required = true)
  @NotNull(message = "400009")
  private Integer rateType;

  @ApiModelProperty(value = "分钟，此字段必填，0，15，30，45", required = true)
  private Integer minute;

  @ApiModelProperty(value = "小时（0-23），当rateType=2、3时，此字段必填")
  private Integer hour;

  @ApiModelProperty(value = "周日-周6（1、2、3、4、5、6、7），当rateType=3时，此字段必填")
  private Integer week;

  private String cronExpression;

  public TaskRateTypeEnum getTateTypeEnum() {
    return TaskRateTypeEnum.getEnumByCode(this.rateType);
  }
}
