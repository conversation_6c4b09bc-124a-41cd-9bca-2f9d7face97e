package com.sp.proxverse.common.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询kpi的二维图纵坐标数据VO对象")
public class KpiVerticalDTO {
  @Tolerate
  public KpiVerticalDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "纵坐标值")
  private List<BigDecimal> ydata;

  @ApiModelProperty(value = "分类（比如：全部、赵强、张伟、目标值）")
  private String column;
}
