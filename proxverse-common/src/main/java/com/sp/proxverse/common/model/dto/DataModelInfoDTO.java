package com.sp.proxverse.common.model.dto;

import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class DataModelInfoDTO {

  @Tolerate
  public DataModelInfoDTO() {
    // comment empty
  }

  private Integer dataModelId;

  private Integer caseid;

  private Integer event;

  private Integer time;

  private Integer startTime;

  private List<Integer> sortingList;
}
