package com.sp.proxverse.common.model.dict;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

public enum FileActiveTypeEnum {
  SECOND(0, "次表"),
  ACTIVE(1, "主表"),
  CASE(2, "CASE表"),
  VIRTUAL_CASE(3, "虚拟CASE表"),
  VARIANT_TABLE(60, "变体表");

  private final Integer value;
  private final String name;

  FileActiveTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static FileActiveTypeEnum getActiveTypeByCode(Integer code) {
    if (Objects.isNull(code)) {
      throw new IllegalArgumentException("code can not be null");
    }
    FileActiveTypeEnum[] values = FileActiveTypeEnum.values();
    for (FileActiveTypeEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value;
      }
    }
    throw new IllegalArgumentException("enum mapping error : code = " + code);
  }

  /**
   * 根据文件后缀名得到枚举int值
   *
   * @param f
   * @return
   */
  public static Integer filterFileType(String f) {
    FileActiveTypeEnum fileTypeEnum =
        Stream.of(values()).filter(e -> Objects.equals(e.getName(), f)).findFirst().orElse(null);
    return Objects.isNull(fileTypeEnum) ? null : fileTypeEnum.getValue();
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  public static List<Integer> getDataModelVersionType() {
    List<Integer> result = new ArrayList<>();
    result.add(ACTIVE.value);
    result.add(CASE.value);
    result.add(VIRTUAL_CASE.value);
    return result;
  }
}
