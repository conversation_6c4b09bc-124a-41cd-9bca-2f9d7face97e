package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
@Builder
@ApiModel("给数据模型增加知识")
public class AddKnowlege4ModelRequest {

  @ApiModelProperty(value = "数据模型ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataModelId;

  @ApiModelProperty(value = "文件", required = true)
  @NotNull(message = "文件不能为空")
  private MultipartFile files;

  @ApiModelProperty(value = "知识名称", required = false)
  private String knowledgeName;
}
