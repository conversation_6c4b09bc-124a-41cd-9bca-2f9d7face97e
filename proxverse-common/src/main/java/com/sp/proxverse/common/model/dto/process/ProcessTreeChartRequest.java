package com.sp.proxverse.common.model.dto.process;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.vo.request.ProcessTreeBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/6/19 17:05
 */
@Data
@ApiModel(value = "树变体请求对象")
public class ProcessTreeChartRequest extends ProcessTreeBase {

  private List<TopicFilterPO> addFilterList;

  private Boolean calculateEventCountFlag = false;

  @ApiModelProperty(value = "变体值 k - 变体  v- 数量")
  private Map<String, Long> variantMap;

  private List<String> variantList;

  @JsonIgnore private String errorMsg;

  @ApiModelProperty(value = "当‘其他’勾选时，此选项不能为空，所有变体中未勾选的变体")
  private List<String> excludeVariantList;

  @ApiModelProperty(value = "变体全选标识")
  private Boolean variantAllFlag;

  private Integer processEventNumberLimit;

  private Integer processConnectNumberLimit;

  public Boolean getVariantAllFlag() {
    if (this.variantAllFlag == null) {
      return false;
    }
    return this.variantAllFlag;
  }

  public Boolean getErrorMsgFlag() {
    if (StringUtils.isNotBlank(this.errorMsg)) {
      return true;
    }
    return false;
  }
}
