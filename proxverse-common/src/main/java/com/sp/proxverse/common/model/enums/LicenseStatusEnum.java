package com.sp.proxverse.common.model.enums;

public enum LicenseStatusEnum {
  NOT_INSTALLED(0, "not_installed"),
  NOT_VALID(1, "not_valid"),
  VALID(2, "valid"),
  EXPIRE(3, "expire");

  private final Integer value;
  private final String name;

  LicenseStatusEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
