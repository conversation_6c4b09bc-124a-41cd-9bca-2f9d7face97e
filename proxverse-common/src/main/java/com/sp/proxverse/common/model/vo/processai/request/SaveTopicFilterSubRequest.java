package com.sp.proxverse.common.model.vo.processai.request;

import com.sp.proxverse.common.model.dict.TopicFilterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("保存过滤条件请求子对象,v1.1")
public class SaveTopicFilterSubRequest {

  @Tolerate
  public SaveTopicFilterSubRequest() {
    // comment empty
  }

  /** @see TopicFilterTypeEnum */
  @ApiModelProperty(
      value =
          "过滤项类型（100：流程通过，101：通过任意，200：流程不通过，201：不通过任意，300：流程开始于，400：流程结束于，500：变体过滤，700:变量过滤(v1.1)，800：流程过滤，900：吞吐时间过滤，1000：返工过滤,3000:流程裁剪）",
      required = true)
  @NotNull(message = "{400008}")
  private Integer type;

  @ApiModelProperty(value = "表ID(事件过滤此字段不填，type=700时此字段必填)")
  private Integer fileId;

  @ApiModelProperty(value = "变量ID(事件过滤此字段不填，type=700时此字段必填)")
  private Integer fieldId;

  @ApiModelProperty(
      value = "变量值列表（勾选值列表）（operationType=10、20、30时，此列表只需传一个值，=41时，也传一个值比如：2021-10-10&2021-10-12）",
      required = true)
  @NotEmpty(message = "{400009}")
  private List<String> paramValue;

  @ApiModelProperty(value = "事件1")
  private String event1;

  @ApiModelProperty(
      value =
          "事件1的类型,(type=900,1100,3000时，10：第一次发生，20：最后一次发生)，（type=800时，10：直接跟随，20：跟随，30：不直接跟随，40：不跟随）")
  private Integer type1;

  @ApiModelProperty(value = "事件2")
  private String event2;

  @ApiModelProperty(value = "事件2的类型,type=900,1100,3000时，10：第一次发生，20：最后一次发生")
  private Integer type2;

  @ApiModelProperty(value = "当type=1000（返工过滤）、900（吞吐时间过滤）时，此为最小值")
  private Integer num1;

  @ApiModelProperty(value = "当type=1000（返工过滤）、900（吞吐时间过滤）时，此为最大值")
  private Integer num2;

  @ApiModelProperty(value = "当type=900（吞吐时间过滤）时，此为时间单位1：天，2：时，3：秒")
  private Integer unit;

  private String expression;

  @ApiModelProperty(value = "选中的event列的名字")
  private String eventColumn;

  @ApiModelProperty(value = "event过滤的属性名字")
  private List<String> filterEvent;

  @ApiModelProperty("记录过滤项描述(目前用在一致性过滤回显)")
  private String description;

  private String formatting;

  private String format;

  private String extension;

  private String startTime;

  private String endTime;

  public List<String> getFilterEvent() {
    if (filterEvent == null) {
      return new ArrayList<>();
    }
    return filterEvent;
  }

  private String param2;

  private String value2;

  // 操作：2-页面查询、3-功能查询、不传默认事件查询
  private Integer eventIndex;
}
