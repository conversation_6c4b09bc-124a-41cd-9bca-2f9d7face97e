package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.po.FileFieldPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FileFieldMapper extends BaseMapper<FileFieldPO> {

  @InterceptorIgnore(tenantLine = "on")
  List<FileFieldPO> listByIdsNoTenant(@Param("ids") List<Integer> ids);
}
