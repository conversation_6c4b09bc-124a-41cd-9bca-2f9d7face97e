package com.sp.proxverse.common.exception;

public abstract class BaseException extends RuntimeException {

  private static final long serialVersionUID = 3722991416976723523L;

  public BaseException() {}

  public BaseException(Integer code, String message) {
    this.code = code;
    this.message = message;
  }

  private Integer code;

  private String message;

  public Integer getCode() {
    return code;
  }

  public void setCode(Integer code) {
    this.code = code;
  }

  @Override
  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }
}
