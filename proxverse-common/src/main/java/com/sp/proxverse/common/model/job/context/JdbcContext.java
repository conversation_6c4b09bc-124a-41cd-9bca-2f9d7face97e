package com.sp.proxverse.common.model.job.context;

import com.sp.proxverse.common.model.job.SaveJdbcConnectorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-09 10:48
 */
@Data
public class JdbcContext {

  /** @see TableLogStatusEnum */
  @ApiModelProperty("提取状态")
  private Integer status;

  private String failDesc;

  SaveJdbcConnectorDTO saveJdbcConnectorDTO;
}
