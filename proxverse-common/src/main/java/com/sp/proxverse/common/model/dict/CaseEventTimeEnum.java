package com.sp.proxverse.common.model.dict;

import java.util.Objects;
import java.util.stream.Stream;

public enum CaseEventTimeEnum {
  CASE_ID(1, "caseId"),
  EVENT(2, "event"),
  TIME(3, "time"),
  ;

  private final Integer value;
  private final String name;

  CaseEventTimeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    CaseEventTimeEnum[] values = CaseEventTimeEnum.values();
    for (CaseEventTimeEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value.getName();
      }
    }
    return "";
  }

  /**
   * 根据文件后缀名得到枚举int值
   *
   * @param f
   * @return
   */
  public static Integer filterFileType(String f) {
    CaseEventTimeEnum fileTypeEnum =
        Stream.of(values()).filter(e -> Objects.equals(e.getName(), f)).findFirst().orElse(null);
    return Objects.isNull(fileTypeEnum) ? null : fileTypeEnum.getValue();
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
