package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("一致性图数据DTO")
public class ConformanceViewDTO {

  @Tolerate
  public ConformanceViewDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "横轴时间")
  private String xdata;

  @ApiModelProperty(value = "纵轴比例数量")
  private BigDecimal ydata;
}
