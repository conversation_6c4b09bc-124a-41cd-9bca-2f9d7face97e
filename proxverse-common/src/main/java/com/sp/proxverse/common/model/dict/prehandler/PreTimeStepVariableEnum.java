package com.sp.proxverse.common.model.dict.prehandler;

import org.apache.spark.common.model.DataTypeEnum;

/** 变体-时间步骤预处理字段 */
public enum PreTimeStepVariableEnum {
  VARIANT_ID("variant_id", "variant_id", DataTypeEnum.INT.getValue()),
  CASE_ID("case_id", "case_id", DataTypeEnum.STR.getValue());

  private final String value;
  private final String name;

  private final int type;

  PreTimeStepVariableEnum(String value, String name, int type) {
    this.value = value;
    this.name = name;
    this.type = type;
  }

  public String getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  public int getType() {
    return type;
  }
}
