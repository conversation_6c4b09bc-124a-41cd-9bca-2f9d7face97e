package com.sp.proxverse.common.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

@Getter
@ApiModel("查询kpi值4数据模型请求DTO")
public class QueryKpi4DataModelDTO {

  @ApiModelProperty(value = "数据模型id", required = true, notes = "248")
  private Integer dataModelId;

  @ApiModelProperty(
      value = "kpi公式",
      required = true,
      notes = "count(`case_table`.eventNum)+count(`case_table`.caseDuration)")
  private String expression;

  @ApiModelProperty(value = "时间维度列ID 1.6.3", required = true)
  //    @NotNull(message = "时间维度列ID不能为空")
  public Integer timeColumnId;
}
