package com.sp.proxverse.common.model.vo.request;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("创建业务主题请求实体类")
public class SaveBusinessTopicRequest {

  @Tolerate
  public SaveBusinessTopicRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "业务主题ID，此字段如果有值则是创建二级主题")
  private Integer businessTopicId;

  @ApiModelProperty(value = "业务主题名称，创建时必填")
  private String name;

  @ApiModelProperty(
      value = "主题类型（100:全景图，101：业务全景图，200：业务主题，201:业务分析图谱，202：业务知识模型，203：流程分析），创建时必填",
      required = true)
  @NotNull(message = "主题类型不能为空")
  private Integer type;

  @ApiModelProperty(value = "选择的数据源类型，目前只有在创建业务分析图谱时需要传值（1：data_model，2：knowledge_model）")
  private Integer dataSourceType;

  @ApiModelProperty(value = "用户Id")
  private Integer userId;

  @ApiModelProperty(value = "选择的数据源ID，目前最多有一个元素")
  private List<Integer> dataId;

  @ApiModelProperty(value = "导入文件方式创建topic")
  private JSONObject topicJsonData;
}
