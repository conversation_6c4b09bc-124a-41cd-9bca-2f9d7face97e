package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-02-22 19:33
 */
@Data
@ApiModel("获取血缘关系类型")
public class ETLLineageRelationReq {

  @NotNull(message = "数据池不能为空")
  private Integer dataPoolId;

  private Integer transformationSqlId;

  private Integer dataTaskChildId;
}
