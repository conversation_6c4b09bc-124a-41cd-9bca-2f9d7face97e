package com.sp.proxverse.common.model.dto.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
public class License extends BaseEntity {
  private String sn;

  private String filename;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date notBefore;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date notAfter;

  @ApiModelProperty(value = "lic过期时间")
  private String notAfterZh;

  private Long capacity;

  private Integer consumerAmount;

  private String consumerType;

  private Long modelCountLimit;

  @ApiModelProperty(hidden = true)
  private String subject;

  @ApiModelProperty(hidden = true)
  private String info;

  @ApiModelProperty(hidden = true)
  private Integer status;

  @ApiModelProperty(hidden = true)
  private Date created;

  @ApiModelProperty(hidden = true)
  private Date updated;
}
