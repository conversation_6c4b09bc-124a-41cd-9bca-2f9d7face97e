package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-20 11:33
 */
@Data
@ApiModel("创建流程仿真资源池")
public class CreateSimulationResourcePoolReqVo {

  @ApiModelProperty(value = "Id 当不为空是更新")
  private Integer id;

  @ApiModelProperty(value = "流程仿真Id")
  @NotNull(message = "{400006}")
  private Integer simulationId;

  @ApiModelProperty(value = "资源池名称")
  private String resourceName;
}
