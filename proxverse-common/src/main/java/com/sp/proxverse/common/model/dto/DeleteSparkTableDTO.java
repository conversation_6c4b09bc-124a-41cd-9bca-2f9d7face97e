package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
@ApiModel("删除 Spark 表 DTO")
public class DeleteSparkTableDTO implements Serializable {
  @Tolerate
  public DeleteSparkTableDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "删除表")
  private List<String> tables;
}
