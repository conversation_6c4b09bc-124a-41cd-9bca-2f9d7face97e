package com.sp.proxverse.common.model.vo.processai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-20 18:03
 */
@Data
@ApiModel("详细流程图Kpi,Kpi信息")
public class TopicProcessDetailsKpi {

  private String name;

  private String expression;

  private Integer kpiId;

  private Integer processTreeKpiRelationId;

  @ApiModelProperty("单位")
  private String unit;

  private String formatting;

  private String format;
}
