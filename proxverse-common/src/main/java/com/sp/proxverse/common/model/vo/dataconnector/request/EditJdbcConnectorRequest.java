package com.sp.proxverse.common.model.vo.dataconnector.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("编辑jdbc请求对象")
public class EditJdbcConnectorRequest {

  @ApiModelProperty(value = "数据链接ID", required = true)
  @NotNull(message = "{400006}")
  private Integer id;

  @ApiModelProperty(value = "数据链接名称")
  @NotBlank(message = "{400007}")
  private String name;

  @ApiModelProperty(value = "IP", required = true)
  private String ip;

  @ApiModelProperty(value = "端口", required = true)
  private Integer port;

  @ApiModelProperty(value = "库名称", required = true)
  private String dbname;

  @ApiModelProperty("所选表名称")
  private String schemaName;

  @ApiModelProperty(value = "实例号（HANA DB）", required = true)
  private String instanceNumber;

  @ApiModelProperty(value = "数据库用户名", required = true)
  private String username;

  @ApiModelProperty(value = "数据库密码", required = true)
  private String password;

  @ApiModelProperty(value = "1：标准链接，2：自定义链接", required = true)
  @NotNull(message = "{400006}")
  private Integer bizType;

  @ApiModelProperty(
      value = "jdbc类型（1：mysql,2:oracle,3:db2,4:hive,5:postgreSql,6:impala），当bizType=1时此字段不能为空")
  private Integer jdbcType;

  @ApiModelProperty(value = "自定义链接，当bizType=2时此字段不能为空")
  private String connectLink;
}
