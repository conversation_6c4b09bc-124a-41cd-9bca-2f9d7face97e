package com.sp.proxverse.common.model.vo.processai.request;

import com.google.common.base.Objects;

/**
 * <AUTHOR>
 * @create 2022-12-27 17:52
 */
public enum CalculationSymbol {
  /** 计算符号 */
  GREATER(">"),
  GREATER_EQUAL("≥"),
  EQUAL("="),
  LESS_EQUAL("≤"),
  LESS("<"),
  ;

  private String symbol;

  CalculationSymbol(String symbol) {
    this.symbol = symbol;
  }

  public static CalculationSymbol getCalculationSymbolBySymbol(String symbol) {
    for (CalculationSymbol value : CalculationSymbol.values()) {
      if (Objects.equal(symbol, value.symbol)) {
        return value;
      }
    }
    return null;
  }
}
