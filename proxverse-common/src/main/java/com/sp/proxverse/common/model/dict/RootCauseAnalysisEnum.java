package com.sp.proxverse.common.model.dict;

import java.util.function.Function;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/6/19 14:07 根因分析类型
 */
@Getter
public enum RootCauseAnalysisEnum {
  EVENT_ROOT_CAUSE(
      1,
      "事件根因",
      "%s",
      "variable",
      (variable) ->
          String.format("MATCH_ACTIVITIES(ACTIVITY_COLUMN(),node_any['%s']) = 1", variable)),
  PATH_CAUSE(
      2,
      "路径根因",
      "process_variant(%s)",
      "variable",
      (variable) -> String.format("process_variant(ACTIVITY_COLUMN()) = '%s'", variable)),
  LINE_ROOT_CAUSE(
      3,
      "连线根因",
      "source(%s), target(%s)",
      "source,target",
      (variable) ->
          String.format(
              " PROCESS  EQUALS '%s' to '%s'", variable.split(",")[0], variable.split(",")[1])),
  REWORK_ROOT_CAUSE(
      4,
      "返工根因",
      "case when INDEX_ACTIVITY_TYPE ( ACTIVITY_COLUMN () ) > 1 then ACTIVITY_COLUMN() end",
      "variable",
      (variable) -> String.format("CALC_REWORK ( ACTIVITY_COLUMN () = '%s') > 1", variable)),
  CUSTOMER_EXPRESSION(5, "自定义根因", "", "", (variable) -> ""),
  ;

  private final Integer code;

  private final String name;

  private final String sortPqlTemplate;

  private final String columnName;

  private final Function<String, String> filterExpressionGenerator;

  RootCauseAnalysisEnum(
      Integer code,
      String name,
      String sortPqlTemplate,
      String columnName,
      Function<String, String> filterExpressionGenerator) {
    this.code = code;
    this.name = name;
    this.sortPqlTemplate = sortPqlTemplate;
    this.columnName = columnName;
    this.filterExpressionGenerator = filterExpressionGenerator;
  }

  public String generateFilterExpression(String variable) {
    return filterExpressionGenerator.apply(variable);
  }

  public static RootCauseAnalysisEnum getByCode(Integer code) {
    for (RootCauseAnalysisEnum value : values()) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return null;
  }
}
