package com.sp.proxverse.common.model.vo.useranalyse.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("获取所有员工请求对象")
public class QueryUserVariableRequest {

  @ApiModelProperty(value = "表格sheetID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  @ApiModelProperty(value = "开始时间")
  private String startTime;

  @ApiModelProperty(value = "结束时间")
  private String endTime;
}
