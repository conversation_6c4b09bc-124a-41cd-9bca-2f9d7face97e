package com.sp.proxverse.common.model.dto.process;

import com.sp.proxverse.common.model.dto.VariantResultDTO;
import com.sp.proxverse.common.model.po.KpiPO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/** <AUTHOR> */
@Data
@ApiModel("流程图变体响应")
public class ProcessTreeVariantOut {

  @ApiModelProperty("变体集合")
  private List<VariantResultDTO> variantList;

  @ApiModelProperty("饼图信息")
  private VariantCalculateDTO variantCalculate;

  @ApiModelProperty("当前所选列")
  private String currentEventColumn;

  private String defaultEventColumn;

  private String errorMsg;

  private List<KpiPO> pathKpis;
}
