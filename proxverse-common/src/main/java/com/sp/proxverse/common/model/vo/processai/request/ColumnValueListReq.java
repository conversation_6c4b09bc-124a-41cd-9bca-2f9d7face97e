package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/4 11:45
 */
@Data
@ApiModel("获取列值计算")
public class ColumnValueListReq {

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty(value = "可以为空，当为空当时候为active列")
  public String columnName;

  @ApiModelProperty(value = "忽略topic过滤")
  public Boolean ignoreTopicFilter;

  public boolean getIgnoreTopicFilter() {
    if (ignoreTopicFilter == null) {
      return false;
    }
    return ignoreTopicFilter;
  }
}
