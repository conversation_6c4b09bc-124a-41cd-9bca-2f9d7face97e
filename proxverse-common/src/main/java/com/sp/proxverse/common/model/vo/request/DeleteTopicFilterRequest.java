package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("删除流程树的过滤项列表请求对象")
public class DeleteTopicFilterRequest {
  @Tolerate
  public DeleteTopicFilterRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "过滤项的主键ID", required = true)
  @NotNull(message = "{400006}")
  private Integer id;

  private Integer topicId;
}
