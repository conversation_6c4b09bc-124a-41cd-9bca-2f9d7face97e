package com.sp.proxverse.common.model.listener.service;

import com.sp.proxverse.common.model.listener.LoadModelEvent;
import com.sp.proxverse.common.model.listener.ModeifyModelStatusEvent;
import com.sp.proxverse.common.model.listener.RefreshModelEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class LoadModelPublishService {
  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  @Async
  public void publishModelLoadEvent(Integer modelId) {
    // 发送一个事件可以让发送短信和邮件都接收同一个事件（如果需要传不同的参数，就需要发布两个不同的事件）
    applicationEventPublisher.publishEvent(new LoadModelEvent(modelId));
  }

  @Async
  public void publishRefreshModelEvent(Integer modelId) {
    applicationEventPublisher.publishEvent(new RefreshModelEvent(modelId));
  }

  @Async
  public void publishModifyModelStatusEvent(Integer modelId) {
    applicationEventPublisher.publishEvent(new ModeifyModelStatusEvent(modelId));
  }
}
