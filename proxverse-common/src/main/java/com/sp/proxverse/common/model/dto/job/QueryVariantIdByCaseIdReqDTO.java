package com.sp.proxverse.common.model.dto.job;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Builder
@Getter
@Setter
public class QueryVariantIdByCaseIdReqDTO implements Serializable {

  private static final long serialVersionUID = 1788816420025414677L;

  @Tolerate
  public QueryVariantIdByCaseIdReqDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "虚拟case表文件ID")
  private Integer fileId;

  @ApiModelProperty(value = "该节点下挂载的子节点")
  private List<String> caseIdList;
}
