package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.po.TopicFilterPO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class BuildQueryDbSqlDTO implements Serializable {
  private static final long serialVersionUID = 2090307504616884308L;

  @Tolerate
  public BuildQueryDbSqlDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "主题ID", required = true)
  private Integer topicId;

  private List<TopicFilterPO> addFilterList;
}
