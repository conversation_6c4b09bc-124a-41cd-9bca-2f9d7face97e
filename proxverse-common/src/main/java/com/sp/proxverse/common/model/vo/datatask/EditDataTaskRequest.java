package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("编辑数据任务请求对象")
public class EditDataTaskRequest {

  @ApiModelProperty(value = "数据提取名称", required = true)
  private String name;

  @ApiModelProperty(value = "数据任务ID", required = true)
  @NotNull(message = "{400006}")
  private Integer taskId;

  @ApiModelProperty(value = "任务频率（1：每小时，2：每天，3：每周）", required = true)
  @NotNull(message = "{400009}")
  private Integer rateType;

  @ApiModelProperty(value = "分钟，此字段必填", required = true)
  private Integer minute;

  @ApiModelProperty(value = "小时（0-23），当rateType=2、3时，此字段必填")
  private Integer hour;

  @ApiModelProperty(value = "周1-周日（1、2、3、4、5、6、7），当rateType=3时，此字段必填")
  private Integer week;

  private String cronExpression;
}
