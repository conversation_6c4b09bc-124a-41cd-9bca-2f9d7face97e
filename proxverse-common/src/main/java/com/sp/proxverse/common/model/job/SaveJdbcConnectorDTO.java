package com.sp.proxverse.common.model.job;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("创建链接器DTO")
public class SaveJdbcConnectorDTO {

  @Tolerate
  public SaveJdbcConnectorDTO() {
    // comment empty
  }

  @ApiModelProperty("File id")
  @NotBlank(message = "File id")
  private int fileId;

  @ApiModelProperty("连接器名称")
  @NotBlank(message = "{400007}")
  private String name;

  @ApiModelProperty("jdbcURL")
  @NotBlank(message = "jdbcURL不能为空")
  private String url;

  @ApiModelProperty("用户名")
  @NotBlank(message = "{400007}")
  private String user;

  @ApiModelProperty("密码")
  @NotBlank(message = "{400007}")
  private String password;

  @ApiModelProperty("whereRule")
  @NotBlank(message = "whereRule")
  private String whereRule;

  @ApiModelProperty("数据集")
  @NotBlank(message = "数据集不能为空")
  private String eventset;

  @ApiModelProperty("业务ID")
  @NotBlank(message = "业务ID不能为空")
  private Integer source;

  private Integer incr;

  @ApiModelProperty("TEMP TableName")
  @NotBlank(message = "Temp Table Name不能为空")
  private String tempTableName;

  @ApiModelProperty("JDBC TableName")
  @NotBlank(message = "jdbc Table Name不能为空")
  private String jdbcTableName;

  @ApiModelProperty("TableName")
  @NotBlank(message = "{400007}")
  private String tableName;

  private String schemaName;

  @ApiModelProperty("DataPoolID")
  @NotBlank(message = "{400006}")
  private int poolId;

  @ApiModelProperty("JDBC Driver")
  @NotBlank(message = "JDBC Driver")
  private String jdbcDriver;

  @ApiModelProperty("1：增量更新（默认），2：全量更新")
  private Integer updateType;
}
