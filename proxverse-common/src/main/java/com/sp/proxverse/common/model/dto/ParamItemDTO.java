package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
public class ParamItemDTO implements Serializable {

  @Tolerate
  public ParamItemDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "主键Id")
  private Integer columnId;

  @ApiModelProperty(value = "执行动作名称")
  private String name;
}
