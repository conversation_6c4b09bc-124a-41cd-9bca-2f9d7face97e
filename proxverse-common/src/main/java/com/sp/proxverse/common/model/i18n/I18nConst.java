package com.sp.proxverse.common.model.i18n;

public class I18nConst {

  // 1开头--数据模型
  // 2开头--topic
  // 3开头--auth2
  // 4开头--Valid
  // 5开头--异常类

  public static String POOL_NOT_EXIST = "100001";

  public static String DB2_NEED_SCHAME = "100002";

  public static String SAVE_CONNECT_ERROR = "100003";

  public static String DATA_CONNECT_NOT_EXIST = "100004";

  public static String EXACTOR_NAME_NOT_SUPPORT_SPECIAL_CHARACTER = "100005";

  public static String TABLE_NAME_MUST_CONTAIN_CHARS = "100006";

  public static String EXACTOR_FILENAME_REPEAT = "100007";

  public static String EXACTOR_NOT_EXIST = "100008";

  public static String EXACTOR_EDIT_DISABLE = "100009";

  public static String MUST_HAS_UPDATE_TIME = "100010";

  public static String EXACTOR_NOT_SUPPORT_CANCEL_COLUMN = "100011";

  public static String EXACTOR_USING_MUST_DELETE_FROM_MODEL = "100012";

  public static String POOL_HAS_EXIST = "100013";

  public static String DATA_MODEL_NOT_EXIST = "100014";

  public static String DELETE_MODEL_DISABLE_IN_PROCESS_AI = "100015";

  public static String DELETE_MODEL_DISABLE_IN_PROCESS_WORK = "100016";

  public static String DELETE_MODEL_DISABLE_IN_EXECUTION = "100017";

  public static String DELETE_KNOWLEDGE_DISABLE_IN_EXECUTION = "100070";

  public static String DELETE_MODEL_DISABLE_IN_KPI = "100018";

  public static String DELETE_FILE_DISABLE_IN_MODEL = "100019";

  public static String DATA_MODEL_REPEAT = "100020";

  public static String SAVE_DATA_MODEL_ERROR = "100021";

  public static String FILE_NOT_EXIST = "100022";
  public static String TABLE_NOT_EXIST = "100068";

  public static String CASE_ATTR_CHECK = "100023";

  public static String EVENT_DATA_TYPE_CHECK = "100024";

  public static String TIME_DATA_TYPE_CHECK = "100025";

  public static String READ_CSV_ERROR = "100026";
  public static String READ_PARQUET_ERROR = "100074";

  public static String READ_ZIP_ERROR = "100075";
  public static String CRON_EXPRESSION_ERROR = "100076";

  public static String CRON_EXPRESSION_INTERVAL_ERROR = "100077";
  public static String EXPRESSION_RULE_ERROR = "100071";

  public static String INVALID_FOREIGN_KEY = "100072";

  public static String FILE_DOES_NOT_EXIST_IN_DIRECTORY = "100073";

  public static String INPUT_DATE_TIME_FORMAT = "100027";

  public static String LINE_NOT_SUPPORT_CYCLE = "100028";

  public static String SELECT_FILE_REPEAT = "100029";

  public static String ACTIVE_TABLE_DELETE_DISABLE = "100030";

  public static String CASE_TABLE_REPEAT = "100031";

  public static String ACTIVE_AND_CASE_TABLE_LINE_WITH_CASEID = "100032";

  public static String DATA_TASK_NOT_EXIST = "100033";

  public static String JDBC_TYPE_NOT_EXIST = "100034";

  public static String DATA_MODEL_HAS_EXIST = "100035";

  public static String DATA_MODEL_HAS_BIND_BY_CHILD = "100036";
  public static String SORTING_TIME_TYPE_DISABLED = "100037";

  public static String DATA_MODEL_EXISTS_ACTIVE = "100038";

  public static String NO_ACTIVE_TABLE = "100039";

  public static String PER_DAY = "100040";
  public static String PER_HOUR = "100041";
  public static String PER_WEEK = "100042";

  public static String SECOND = "100050";
  public static String MINUTE = "100051";
  public static String HOUR = "100052";
  public static String DAY = "100053";
  public static String WEEK = "100054";
  public static String MILLISECOND = "100055";

  public static String MONDAY = "100061";
  public static String TUESDAY = "100062";
  public static String WEDNESDAY = "100063";
  public static String THURSDAY = "100064";
  public static String FRIDAY = "100065";
  public static String SATURDAY = "100066";
  public static String SUNDAY = "100067";
  public static String FIELD_TYPE_NOT_FOUND = "100069";

  public static String DATA_POOL = "100100";
  public static String BUSINESS_TOPIC = "100101";
  public static String BUSINESS_ANALYSIS = "100102";
  public static String DATA_CONNECTOR_NOT_EXIST = "100103";

  public static String IP_PORT_EMPTY_DISABLED = "100200";
  public static String DB2_SCHEMA_EMPTY_DISABLED = "100201";
  public static String USERNAME_PASSWORD_EMPTY_DISABLED = "100202";
  public static String CONNECT_LINK_EMPTY_DISABLED = "100203";

  public static String LICENSE_EXCEPTION = "100210";
  public static String NO_ACTIVE_LICENSE = "100211";
  public static String LICENSE_ILLEGAL = "100212";
  public static String LICENSE_EXPIRED = "100213";
  public static String LICENSE_FLLE_NOT_FOUND = "100214";
  public static String LICENSE_USER_NUMBER_LIMIT = "100215";

  public static String EXECUTE_REMAINING_THREAD_POOL_IMMEDIATELY = "100216";
  public static String REMAINING_TASKS_IN_THREAD_POOL = "100217";
  public static String UPDATE_TABLE_COLLECTION = "100218";
  public static String THE_REFRESHED_DATA_MODELS_ARE = "100219";
  public static String ERROR_MESSAGE = "100220";
  public static String PARENT_TASK_EXECUTOR_ID = "100221";

  public static String TASK_ACTUATOR_ID = "100222";
  public static String START_TIME = "100223";
  public static String RUNNING_TIME = "100224";
  public static String TASK = "100225";

  public static String THE_REFRESHED_DATA_CONVERSIONS_ARE = "100226";

  public static String FILE_ID = "100228";
  public static String INCREASE_MAXIMUM_VALUE_OF_TIME_COLUMN = "100229";
  public static String ADD_TIME_COLUMN_NAME = "100230";
  public static String NUMBER_OF_DATA_EXTRACTION_ROWS = "100231";
  public static String PULLED_NUMBER_DATA_EXTRACTION_ROWS = "100243";
  public static String EXECUTE_THE_REMAINING_TASKS_OF_THE_THREAD_POOL_IMMEDIATELY = "100233";

  public static String DATA_MODEL_ID = "100234";
  public static String DATA_MODEL_NAME = "100235";

  public static String LOCAL = "100236";

  public static String DATA_MODEL_LOADING = "100300";
  public static String DATA_MODEL_NO_CASEID_WHEN_LOAD = "100301";
  public static String DATA_MODEL_NO_ACTIVE_WHEN_LOAD = "100302";

  public static String PASSWORDS_MUST_DIFFER_FROM_THE_DEFAULT_PASSWORD = "100303";
  public static String PASSWORD_CANNOT_REUSE_RECENT_PASSWORDS = "100304";
  public static String TYPE_NOT_FOUND = "400008";

  public static String CASE_ID = "100340";
  public static String EVENT_NUM = "100341";
  public static String CASE_DURATION = "100342";

  public static String CAPACITY_REACHED_UPPER_LIMIT = "100400";
  public static String FILE_FORMAT_INCORRECT = "100401";
  public static String FILE_NAME_OPERATORS_ERROR = "100402";

  public static String TOPIC_NOT_EXIST = "200001";
  public static String ACTION_FLOW_NOT_EXIST = "200002";

  public static String BPMN_FILE_DATA_ERROR = "200003";

  public static String USER_INFO_NOT_EXIST = "200004";
  public static String LEVEL_ONE_TOPIC_MUST_BE = "200005";
  public static String TYPE_NOT_MATCH = "200006";
  public static String ONE_AT_MOST = "200007";
  public static String DATA_MODEL_DISABLE_BEFORE_LOAD = "200008";

  public static String NAME_EMPTY_DISABLE = "200009";
  public static String NAME_CONTAINS_SPACE_DISABLE = "200010";
  public static String NAME_CONTAINS_POINT_DISABLE = "200011";

  public static String KPI_NOT_EXIST = "200020";
  public static String KPI_HAS_EXIST = "200021";
  public static String KPI_TYPE_ERROR = "200022";
  public static String KPI_ATTR_EMPTY = "200023";

  public static String PARAM_NAME_HAS_EXIST_FOR_SYSTEM = "200025";
  public static String PARAM_HAS_EXIST = "200026";
  public static String PARAM_NOT_EXIST = "200027";

  public static String DATA_NOT_EXIST = "200030";
  public static String DATA_ALREADY_EXIST = "200031";

  public static String FOUR_AT_MOST = "200040";

  public static String MAPPER_GROUP_REPEAT = "200041";

  public static String EVENT_FILTER = "200050";
  public static String FILTER_CONDITION = "200051";
  public static String FILTER_PROCESS_START = "200052";
  public static String FILTER_PROCESS_END = "200053";
  public static String FILTER_ALL_EVENT = "200054";
  public static String CUSTOM_FILTER = "200055";
  public static String PARAMETER_FILTER = "200056";
  public static String VARIABLE_FILTER = "200057";

  public static String LIMIT_EMPTY_DISABLE = "200060";
  public static String PQL_EMPTY_DISABLE = "200061";

  public static String VARIABLE = "200065";

  public static String REWORK_FILTER = "200066";

  public static String EVENT = "200067";

  public static String PROCESS_WITH = "200240";
  public static String PROCESS_WITH_ANY = "200241";
  public static String PROCESS_WITHOUT = "200245";
  public static String PROCESS_WITHOUT_ANY = "200246";

  public static String VARIANT_FILTER = "200250";

  public static String PROCESS_FILTER = "200300";
  public static String PROCESS_DIRECT_FOLLOW = "200301";
  public static String PROCESS_FILTER_FOLLOW = "200302";
  public static String PROCESS_NOT_DIRECTLY_FOLLOW = "200303";
  public static String PROCESS_NOT_FOLLOW = "200304";

  public static String FILTER_THROUGHPUT_TIME = "200310";
  public static String FILTER_COMPONENT = "200311";
  public static String FILTER_EXPRESSION = "200312";
  public static String FILTER_CROP_SELECTION = "200313";

  public static String START = "200070";

  public static String END = "200071";

  public static String START_STR = "200073";

  public static String UNDISTRIBUTED = "200072";

  public static String PERCENTAGE = "200080";
  public static String CASE_NUM = "200081";

  public static String VARIANT_BEEN_CONFORMANCE = "200082";

  public static String EVENT_SORT_DIFF = "200083";

  public static String PUBLISH_SECOND_TOPIC = "200085";

  public static String PUBLISH_EMPTY_DISABLE = "200086";

  public static String DATA_ERROR = "200087";

  public static String START_ERROR = "200088";
  public static String END_ERROR = "200089";

  public static String EVENT_ROUTE_ERROR = "200090";
  public static String MULTIPLE_START_OR_END = "200091";
  public static String GATEWAY_PROBABILITY_ERROR = "200092";

  public static String ADD_FILTER_FIRST = "200100";

  public static String BOOK_MARK_NOT_EXISTS = "200101";

  public static String COMMAND_SHEET_CREATE_DISABLED = "200102";

  public static String VARIANT_OTHER = "200103";

  public static String TOP_N_CONTAIN_AGGREGATION = "200104";
  public static String DEVIATION_KPI_CONTAIN_AGGREGATION = "200105";
  public static String PIVOT_TABLE_KPI_CONTAIN_AGGREGATION = "200106";
  public static String DEVIATION_EVENT_KPI_CONTAIN_AGGREGATION = "200107";

  public static String NO_START_END = "200120";

  public static String INFORMANCE_INCOMPLETE = "200121";
  public static String MISSING_START_PARAMETER = "200123";
  public static String ACTION_RUNNING_ERROR = "200122";

  public static String EMAIL_CREATE_ERROR = "200130";
  public static String EMAIL_BUILD_ERROR = "200131";
  public static String EMAIL_SEND_ERROR = "200132";
  public static String EMAIL_CLOSE_ERROR = "200133";

  public static String USER_SEND_NOT_EXISTS = "200134";
  public static String COMPANY_WECHAT_NOT_EXISTS = "200135";
  public static String COMPANY_WECHAT_AGENTID_NOT_EXISTS = "200136";
  public static String COMPANY_WECHAT_PASSWORD_KEY_ERROR = "200137";
  public static String SEND_MSG = "200138";

  public static String FAIL = "200200";
  public static String SUCCESS = "200201";
  public static String INCOMPLETED = "200202";
  public static String COMPLETED = "200203";

  public static String DISTRIBUTED_TO_DIFFERENT = "200260";
  public static String DISTRIBUTED_RANDOM = "200261";
  public static String DISTRIBUTED_SEQUENCE = "200262";

  public static String EXECUTION_DATA_MODEL_COLUMN_ERROR = "200270";

  public static String NO_CORRELATION = "200271";

  public static String PROCESS_AI = "200221";
  public static String PROCESS_VARIANT = "200222";
  public static String CASE_EXPLORER = "200223";
  public static String KPI_ANALYSE = "200224";
  public static String PROCESS_EXPLORER = "200225";
  public static String CONFORMANCE = "200226";
  public static String NEW_SHEET = "200227";
  public static String REWORK = "200228";
  public static String THROUGHPUT_TIME = "200229";
  public static String PROCESS_COMPARISON = "200230";
  public static String PATH_EXPLORER = "200230";

  public static String CONFORMANCE_FILTER = "200290";
  public static String UNCONFORMANCE_FILTER = "200291";

  public static String PROCESS_AND_CHILDREN = "200400";
  public static String PROCESS_FILE_IS_EMPTY = "200401";
  public static String NEWSHEET_TEMPLATE_COUNT_MAX = "200410";

  // process manager
  public static String CREATE_PROCESS_MANAGER_LEVEL_ERROR = "200500";

  public static String USER_NOT_EXIST = "300001";
  public static String USER_STATUS_ERROR = "300002";
  public static String USER_PASSWORD_ERROR = "300003";
  public static String USER_LOGIN_ERROR = "300070";
  public static String USER_GROUP_NOT_EXIST = "300004";
  public static String USER_EXPIRE = "300005";
  public static String USER_CHECK_PASS = "300006";
  public static String USER_LOCK_MAX_ERROR = "300007";
  public static String USER_LOCK_NUM_ERROR = "300008";
  public static String USER_UPDATE_PASSWORD_ERROR = "300009";

  public static String COMPANY_NOT_EXIST = "300010";
  public static String COMPANY_USER_EXCEED_LIMIT = "300011";
  public static String EMAIL_NOT_EXIST = "300012";
  public static String EMAIL_HAS_EXIST = "300013";
  public static String EDIT_ROOT_DISABLE = "300014";
  public static String EDIT_SELF_DISABLE = "300015";
  public static String OOS_USER_NOT_LOADED = "300016";

  public static String USER_APPLY = "300020";
  public static String BACK_APPLY = "300021";

  public static String VIEW_PERMISSION = "300030";
  public static String EDIT_PERMISSION = "300031";
  public static String GRANT_PERMISSION = "300032";
  public static String USE_PERMISSION = "300033";

  public static String USER_GROUP_REPEAT = "300050";
  public static String HAS_ALL_PERMISSION = "300060";
  public static String AUTHORIZATION_EXPIRED = "300061";

  /** 账号被顶用 */
  public static String ACCOUNT_NUMBER_IS_USED = "300062";

  /** 用户被踢下线 */
  public static String USER_WAS_KICKED_OFFLINE = "300063";

  // role and permission  300200
  public static String ROLE_ADMIN_USING = "300200";
  public static String ROLE_NAME_REPEAT = "300201";
  public static String ROLE_EXPIRE_JUMP_TO_LOGIN_PAGE = "300202";

  // permission name
  public static String DASH_HOARD = "300400";
  public static String VIEW_PERMISSIONS = "300401";
  public static String SHARE_PERMISSION = "300402";
  public static String STUDIO = "300500";
  public static String STUDIO_VIEW_PERMISSIONS = "300501";
  public static String CREATE_BUSINESS_ANALYSIS = "300502";
  public static String PERMISSION_SETTING = "300503";
  public static String DOWNLOAD = "300504";
  public static String CREATE_ACTION_FLOW = "300505";
  public static String CREATE_KNOWLEDGE_MODEL = "300506";
  public static String CREATE_PROCESS_SIMULATION = "300507";
  public static String PUBLISH_BUSINESS_ANALYSIS = "300508";
  public static String TOPIC_EXPORT = "300509";
  public static String PROCESS_MANAGE = "300600";
  public static String VIEW_PROCESS_MANAGE = "300601";
  public static String CREATE_FIRST_WORKFLOW = "300602";
  public static String PUBLISH = "300603";
  public static String DOWNLOAD_BPMN = "300604";
  public static String DOWNLOAD_SVG = "300605";
  public static String ACTION_ENGINE = "300700";
  public static String DATA_ETL = "300800";
  public static String VIEW_DATA_ETL = "300801";
  public static String IMPORT_DATA = "300802";
  public static String VIEW_DATA_LINK = "300803";
  public static String DATA_MODEL = "300804";

  public static String ROOT = "300300";

  public static String ID_CANNOT_BE_EMPTY = "400006";
  public static String FILE_PACKAGING_EXCEPTION = "400029";

  public static String REQUEST_TIMEOUT = "400011";
  public static String PROCESS_MANAGE_NAME_REPEAT = "400023";

  public static String BPMN_FILE_PARSING_FAILED = "400024";
  public static String BEST_PRACTICES = "400025";
  public static String FILE_UPLOAD_FAILED = "400026";
  public static String FILE_DOWNLOAD_FAILED = "400027";
  public static String PROCESS_MANAGE_EXPORT_HEADER = "400028";

  public static String ERROR_PARAM_COMMON = "500001";

  public static String LOGIN_TOKEN_EXCEPTION = "500010";

  public static String NULL_PONIT_EXCEPTION = "500011";

  public static String DUP_NAME = "500012";

  // file upload
  public static String ERROR_FILE_NAME = "600001";

  public static String ERROR_FILE_TYPE = "600002";

  public static String ERROR_FILE_SIZE = "600003";

  public static String INSUFFICIENT_STORAGE_SPACE = "600004";

  public static String MAXIMUM_DATA_SPACE_REACHED = "600005";
  public static String FILE_TYPE_CANNOT_BE_EMPTY = "600006";
  public static String DATA_POOL_FILE_NAME_EXIST = "600007";
  public static String ETL_IS_RUNNING = "600008";

  public static String ETL_RUN_RESULT_NOT_EXIST = "600009";

  public static String FILE_NOT_FOUND = "600010";

  // license
  public static String LICENSE_MODEL_COUNT_LIMIT = "600200";

  // signal
  public static String EXECUTION_ACTION_UP_TO_4 = "700001";

  public static String EXECUTION_COLUMN_MUST_SELECTED = "700002";

  public static String SIGNAL_DELETED = "700003";

  public static String SIGNAL_DATA_MODEL_DELETED = "700004";

  public static String SIGNAL_DATA_MODEL_NOT_READY = "700005";

  public static String SHARING_HAS_LAPSED = "700006";
  public static String SIGNAL_AUTOMATIC_EXECUTION = "700100";
  public static String MANUAL_EXECUTION = "700101";
  public static String CHINESE_CHARACTERS_ERROR = "700102";
  public static String DATA_EXTRACTION_NAME_CANNOT_BE_EMPTY = "400030";
  public static String COLUMN_CANNOT_BE_EMPTY = "700103";
  public static String ADDED_DATA_COLUMN_RULE_ERROR = "700104";

  // AI
  public static String AI_ERROR_MESSAGE = "800001";
  public static String SERVICE_RESTART = "800002";

  public static String AI_EXCEPTION = "900001";

  public static String UPLOAD_DOCUMENT_ERROR = "900002";

  public static String DELETE_DOCUMENT_ERROR = "900003";

  public static String WORD_LEARN_ERROR = "910001";

  public static String REPORT_NOT_FOUND = "920001";
  public static String REPORT_DOWNLOAD_ERROR = "920002";
  public static String REPORT_GET_ERROR = "920003";
  public static String REPORT_DELETE_ERROR = "920004";
  public static String REPORT_QUERY_ERROR = "920005";
}
