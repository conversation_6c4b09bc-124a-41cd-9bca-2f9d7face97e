package com.sp.proxverse.common.model.dto;

import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
public class QueryColumnDataByColumnRespDTO {

  @Tolerate
  public QueryColumnDataByColumnRespDTO() {
    // comment empty
  }

  private Integer count;

  private Integer pageNum;
  private Integer pageSize;

  private List<String> data;
}
