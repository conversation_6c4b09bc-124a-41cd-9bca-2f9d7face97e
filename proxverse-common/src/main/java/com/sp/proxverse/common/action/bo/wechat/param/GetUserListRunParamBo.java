package com.sp.proxverse.common.action.bo.wechat.param;

import lombok.Data;
import org.codehaus.jackson.annotate.JsonIgnore;

/**
 * <AUTHOR>
 * @create 2022-06-09 10:57 上午
 */
@Data
public class GetUserListRunParamBo extends WechatRunParamBo {
  /** token */
  @JsonIgnore private String accessToken;

  /** 部门Id */
  private String departmentId;

  /** 非必填 是否递归获取子部门下面的成员：1-递归获取，0-只获取本部门 */
  private String fetchChild;
}
