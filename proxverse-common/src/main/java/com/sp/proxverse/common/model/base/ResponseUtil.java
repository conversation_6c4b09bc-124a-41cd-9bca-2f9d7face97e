package com.sp.proxverse.common.model.base;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.Objects;
import lombok.Data;

@Data
@ApiModel("请求返回数据util")
public class ResponseUtil<T> implements Serializable {

  public static Boolean isSucc(Response response) {
    return Objects.nonNull(response)
        && Objects.equals(response.getCode(), 2000)
        && Objects.nonNull(response.getData());
  }
}
