package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.dto.DataModelDTO;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.vo.DataSourceOutputVO;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

@CacheNamespace
public interface DataModelMapper extends BaseMapper<DataModelPO> {

  List<DataModelDTO> getDataModelList(
      @Param("modelIds") List<Integer> modelIds, @Param("poolIds") List<Integer> poolIds);

  Integer queryTenantId(
      @Param("tableName") String tableName,
      @Param("field") String field,
      @Param("value") String value,
      @Param("tenantId") Integer tenantId);

  List<DataSourceOutputVO> getDataModelList4Page(
      @Param("modelIdList") List<Integer> modelIdList,
      @Param("poolIds") List<Integer> poolIds,
      @Param("name") String name,
      @Param("start") Integer start,
      @Param("pageSize") Integer pageSize);

  Long getDataModelCount4Page(
      @Param("modelIdList") List<Integer> modelIdList,
      @Param("poolIds") List<Integer> poolIds,
      @Param("name") String name);
}
