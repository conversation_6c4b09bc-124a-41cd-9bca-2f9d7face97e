package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("业务分析图谱下的sheet表格对象")
public class TopicSheetOutputVO {

  @Tolerate
  public TopicSheetOutputVO() {
    // comment empty
  }

  @ApiModelProperty("主题表格主键ID")
  private Integer dataId;

  @ApiModelProperty("表格类型（1：流程AI，2：流程变体，3：案例视图，4：业务视图，5：流程视图）")
  private Integer type;

  @ApiModelProperty(value = "sheet名称")
  private String name;

  @ApiModelProperty(value = "sheet config")
  private String configMap;

  private Integer hasSheetFilter;
}
