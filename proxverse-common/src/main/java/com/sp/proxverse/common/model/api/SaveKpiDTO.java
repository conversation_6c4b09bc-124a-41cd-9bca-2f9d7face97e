package com.sp.proxverse.common.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
@ApiModel("api保存kpi信息DTO")
public class SaveKpiDTO {

  @Tolerate
  public SaveKpiDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "主键id(新增没有，更新有)")
  public Integer id;

  @ApiModelProperty(value = "数据模型id")
  public Integer dataModelId;

  @ApiModelProperty(value = "kpi名称")
  private String name;

  @ApiModelProperty(value = "单位")
  private String unit;

  @ApiModelProperty(value = "1:越大越优，2：越小越优")
  private Integer type;

  @ApiModelProperty(value = "目标值")
  private BigDecimal target;

  @ApiModelProperty(value = "公式")
  private String expression;

  @ApiModelProperty(value = "时间维度列ID 1.6.3")
  public Integer timeColumnId;
}
