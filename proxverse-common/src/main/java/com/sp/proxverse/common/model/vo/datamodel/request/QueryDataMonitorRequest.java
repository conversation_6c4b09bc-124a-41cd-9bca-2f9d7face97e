package com.sp.proxverse.common.model.vo.datamodel.request;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("查询数据监测请求对象")
public class QueryDataMonitorRequest extends PageRequest {

  @ApiModelProperty(value = "开始时间")
  private String startTime;

  @ApiModelProperty(value = "结束时间")
  private String endTime;

  @ApiModelProperty(value = "状态（1：成功，2：进行中，3：失败）")
  private Integer status;

  @ApiModelProperty(value = "数据池ID")
  private Integer poolId;
}
