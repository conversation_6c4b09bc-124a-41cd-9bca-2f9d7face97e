package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/** <AUTHOR> */
@Data
@Builder
@ApiModel("查询租户状态请求DTO")
public class QueryGroupStatusVO {

  @Tolerate
  public QueryGroupStatusVO() {
    // comment empty
  }

  @ApiModelProperty(value = "租户ID")
  private Integer groupId;
}
