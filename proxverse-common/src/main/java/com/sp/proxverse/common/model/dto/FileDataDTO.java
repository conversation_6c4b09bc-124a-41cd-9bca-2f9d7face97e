package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.dict.FileTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("渲染文件数据DTO")
public class FileDataDTO {

  @Tolerate
  public FileDataDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "文件分割符")
  private String separatorMark;

  @ApiModelProperty(value = "文件编码")
  private String charEncode;

  private String fileName;

  @ApiModelProperty(value = "数据列表")
  private List<TableDataDTO> list;

  private boolean isEditable = true;

  private Boolean includeHeader;

  private FileTypeEnum fileTypeEnum;

  private String status;
}
