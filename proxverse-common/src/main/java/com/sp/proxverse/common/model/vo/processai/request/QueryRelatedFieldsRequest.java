package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询相关变量请求对象")
public class QueryRelatedFieldsRequest {

  @Tolerate
  public QueryRelatedFieldsRequest() {
    // comment empty
  }

  private List<Integer> fieldType;

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  //    @NotNull(message = "当前所在主题ID不能为空")
  private Integer topicId;

  private Integer dataModelId;

  private Integer signalRuleId;

  @ApiModelProperty(value = "在其他地方调用可不传，但在添加过滤项里获取字段列表时需传1，传2表示要带上kpi")
  private Integer source;
}
