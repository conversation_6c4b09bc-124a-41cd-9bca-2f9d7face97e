package com.sp.proxverse.common.model.dict;

public enum BuildFilterSqlTypeEnum {
  QUERY_WITH("query_with", "通过"),
  QUERY_WITH_ANY("query_with_any", "任意通过"),
  QUERY_WITHOUT("query_without", "不通过"),
  QUERY_WITHOUT_ANY("query_without_any", "任意不通过"),
  QUERY_START("query_start", "以事件开始"),
  QUERY_END("query_end", "以事件结束"),
  QUERY_VARIANT("query_variant", "变体"),
  QUERY_VARIANT_NOTEQ("query_variant_noteq", "变体不等于"),
  QUERY_VARIABLE("query_variable", "变量"),
  QUERY_PARAM("query_param", "参数项"),
  QUERY_GTEQQ("query_gteq", "大于等于"),
  QUERY_LTEQ("query_lteq", "小于等于"),
  QUERY_GT("query_gt", "大于"),
  QUERY_LT("query_lt", "小于"),
  QUERY_EQ("query_eq", "等于"),
  QUERY_STR_EQ("query_str_eq", "等于"),
  QUERY_TIME("query_time", "时间范围"),

  QUERY_PROCESS("query_process", "流程过滤"),
  QUERY_CALC_TIME("query_calc_time", "吞吐时间过滤"),
  QUERY_REWORK("query_rework", "返工过滤"),
  ;

  private final String value;
  private final String name;

  BuildFilterSqlTypeEnum(String value, String name) {
    this.value = value;
    this.name = name;
  }

  public String getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
