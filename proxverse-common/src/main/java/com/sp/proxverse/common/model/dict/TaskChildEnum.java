package com.sp.proxverse.common.model.dict;

public enum TaskChildEnum {
  TASK_TYPE_DATA_EXTRACTION(0, "数据提取"),
  TASK_TYPE_ETL(1, "数据转换"),
  TASK_TYPE_SIMULATION(2, "流程仿真"),
  TASK_TYPE_UNKNOWN(-1, "UNKNOWN"),
  ;

  private final Integer value;
  private final String name;

  TaskChildEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
