package com.sp.proxverse.common.model.dto;

import lombok.Builder;
import lombok.experimental.Tolerate;

@Builder
public class GroupCountDTO {

  @Tolerate
  public GroupCountDTO() {
    // comment empty
  }

  private String param;

  private Integer count;

  public String getParam() {
    return param;
  }

  public void setParam(String param) {
    this.param = param;
  }

  public Integer getCount() {
    return count;
  }

  public void setCount(Integer count) {
    this.count = count;
  }
}
