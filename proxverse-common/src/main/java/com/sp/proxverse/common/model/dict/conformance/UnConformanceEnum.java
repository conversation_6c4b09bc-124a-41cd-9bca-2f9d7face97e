package com.sp.proxverse.common.model.dict.conformance;

import com.sp.proxverse.common.util.I18nUtil;
import scala.Tuple2;

public enum UnConformanceEnum {
  CONFORMANCE(0, 500, "Conforms", "200280"),
  NOT_FOUND(1, 501, "is an undesired activity", "200281"),
  FOLLOWED_BY(2, 502, " is followed by ", "200282"),
  NOT_START(3, 503, "executed as start activity", "200283"),
  INCOMPLETE(4, 504, "Incomplete", "200284"),
  ;

  public static Tuple2<String, Integer> getNameByDesc(Tuple2<String, Integer> tuple2) {
    UnConformanceEnum[] enums = values();
    for (UnConformanceEnum en : enums) {
      if (en.value.equals(tuple2._2)) {
        if (en.value.equals(CONFORMANCE.value)) {
          return tuple2;
        }
        if (en.value.equals(NOT_FOUND.value)) {
          String desc = tuple2._1;
          return new Tuple2<>(
              desc.replace(NOT_FOUND.name, I18nUtil.getMessage(NOT_FOUND.msgKey)), tuple2._2);
        }
        if (en.value.equals(FOLLOWED_BY.value)) {
          String desc = tuple2._1;
          String[] descArr = desc.split(FOLLOWED_BY.name);
          String msg = I18nUtil.getMessage(FOLLOWED_BY.msgKey);
          String message = String.format(msg, descArr[0], descArr[1]);
          return new Tuple2<>(message, tuple2._2);
        }
        if (en.value.equals(NOT_START.value)) {
          String desc = tuple2._1;
          return new Tuple2<>(
              desc.replace(NOT_START.name, I18nUtil.getMessage(NOT_START.msgKey)), tuple2._2);
        }
        if (en.value.equals(INCOMPLETE.value)) {
          String desc = tuple2._1;
          return new Tuple2<>(
              desc.replace(INCOMPLETE.name, I18nUtil.getMessage(INCOMPLETE.msgKey)), tuple2._2);
        }
      }
    }
    return tuple2;
  }

  public static String to18InResult(String message) {
    String result = "";
    for (UnConformanceEnum en : UnConformanceEnum.values()) {
      if (message.contains(en.getName())) {
        if (en.value.equals(FOLLOWED_BY.value)) {
          String[] descArr = message.split(FOLLOWED_BY.name);
          String msg = I18nUtil.getMessage(FOLLOWED_BY.msgKey);
          result = String.format(msg, descArr[0], descArr[1]);
        } else {
          result = message.replace(en.getName(), I18nUtil.getMessage(en.msgKey));
        }
      }
    }
    return result;
  }

  private final Integer value;
  private final Integer filterType;
  private final String name;
  private final String msgKey;

  UnConformanceEnum(Integer value, Integer filterType, String name, String msgKey) {
    this.value = value;
    this.filterType = filterType;
    this.name = name;
    this.msgKey = msgKey;
  }

  public Integer getFilterType() {
    return filterType;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  public String getMsgKey() {
    return msgKey;
  }
}
