package com.sp.proxverse.common.model.dto.result;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
@ApiModel
public class ValidationResult implements Serializable {

  @Tolerate
  public ValidationResult() {}

  public ValidationResult(boolean valid, String syntaxError) {
    this.valid = valid;
    this.syntaxError = syntaxError;
  }

  private boolean valid;

  private String syntaxError;
}
