package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("kpi名称VO对象")
public class KpiNameVO {
  @Tolerate
  public KpiNameVO() {
    // comment empty
  }

  @ApiModelProperty(value = "ID")
  private Integer id;

  @ApiModelProperty(value = "名称")
  private String name;
}
