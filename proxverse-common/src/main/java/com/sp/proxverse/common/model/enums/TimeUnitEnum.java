package com.sp.proxverse.common.model.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 时间单位
 *
 * <AUTHOR>
 * @create 2022-08-15 4:36 下午
 */
public enum TimeUnitEnum {

  /** 小时 */
  HOUR("h"),
  DAY("d"),
  MINUTE("m"),
  SECOND("s"),
  ;

  /** 简称 */
  private String value;

  TimeUnitEnum(String value) {
    this.value = value;
  }

  public static TimeUnitEnum getTimeUnitEnumByName(String name) {
    if (StringUtils.isBlank(name)) {
      return null;
    }
    for (TimeUnitEnum timeUnitEnum : TimeUnitEnum.values()) {
      if (timeUnitEnum.name().equals(name)) {
        return timeUnitEnum;
      }
    }
    return null;
  }
}
