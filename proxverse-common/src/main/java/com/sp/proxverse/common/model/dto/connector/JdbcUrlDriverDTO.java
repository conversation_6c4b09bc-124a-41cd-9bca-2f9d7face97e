package com.sp.proxverse.common.model.dto.connector;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("jdbcURL和驱动DTO")
public class JdbcUrlDriverDTO {

  @Tolerate
  public JdbcUrlDriverDTO() {
    // comment empty
  }

  @ApiModelProperty("jdbcurl")
  private String url;

  @ApiModelProperty("驱动")
  private String driver;
}
