package com.sp.proxverse.common.model.vo.processExplorer;

import com.sp.proxverse.common.model.po.KpiPO;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import scala.Serializable;

/**
 * <AUTHOR>
 * @create 2023-04-21 18:11
 */
@Data
public class ProcessExplorerRes implements Serializable {

  private String errorMsg;

  private List<EventNode> eventNodes = new ArrayList<>();

  private List<LineNode> lineNodes = new ArrayList<>();

  private List<KpiPO> eventKpis;

  private List<KpiPO> lineKpis;

  private Long eventCount;

  private Integer gradientMax;

  private Long caseCount;

  private String currentEventColumn;

  private String defaultEventColumn;

  private Integer lineNumberLimit;

  private Integer eventNumberLimit;

  public void setEventNodes(List<EventNode> eventNodes) {
    this.eventNodes = eventNodes;
  }

  public ProcessExplorerRes() {}

  public ProcessExplorerRes(String errorMsg) {
    this.errorMsg = errorMsg;
  }

  public void setErrorMsg(String errorMsg) {
    this.errorMsg = errorMsg;
  }

  public String getCurrentEventColumn() {
    return currentEventColumn;
  }

  public void setCurrentEventColumn(String currentEventColumn) {
    this.currentEventColumn = currentEventColumn;
  }

  public void setDefaultEventColumn(String defaultEventColumn) {
    this.defaultEventColumn = defaultEventColumn;
  }

  public List<EventNode> getEventNodes() {
    return eventNodes;
  }

  public List<LineNode> getLineNodes() {
    return lineNodes;
  }

  public void setLineNodes(List<LineNode> lineNodes) {
    this.lineNodes = lineNodes;
  }

  public Long getEventCount() {
    return eventCount;
  }

  public void setEventCount(Long eventCount) {
    this.eventCount = eventCount;
  }

  public void setGradientMax(Integer gradientMax) {
    this.gradientMax = gradientMax;
  }

  public Long getCaseCount() {
    return caseCount;
  }

  public void setCaseCount(Long caseCount) {
    this.caseCount = caseCount;
  }
}
