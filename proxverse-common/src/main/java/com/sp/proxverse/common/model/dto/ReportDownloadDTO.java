package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/** 报告下载数据传输对象 */
@Data
@Builder
public class ReportDownloadDTO {

  @ApiModelProperty("报告文件内容")
  private byte[] content;

  @ApiModelProperty("文件名")
  private String fileName;

  @ApiModelProperty("文件大小")
  private long fileSize;

  @ApiModelProperty("报告标题")
  private String title;
}
