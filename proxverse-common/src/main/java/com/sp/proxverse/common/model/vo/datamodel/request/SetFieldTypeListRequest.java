package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("设置文件内字段数据类型列表请求对象")
public class SetFieldTypeListRequest {

  @ApiModelProperty(value = "文件ID", required = true)
  @NotNull(message = "{400006}")
  private Integer fileId;

  @ApiModelProperty(value = "数据池ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataPoolId;

  @ApiModelProperty(value = "文件数据分割符")
  private String separator;

  @ApiModelProperty(value = "文件数据字符编码")
  private String charEncode;

  @ApiModelProperty(value = "数据类型ID，getFileDataList接口返回的dataType字段,v1.1修改")
  private List<SetFieldTypeRequest> list;

  private String fileName;

  /** 是否包含表头 */
  private Boolean includeHeader;

  public Boolean getIncludeHeader() {
    if (includeHeader == null) {
      return true;
    }
    return includeHeader;
  }

  /** 属性列重命名 */
  private Map<String, String> columnRename;

  private String primaryKey;
  private String updateTime;
}
