package com.sp.proxverse.common.model.dict.kpi;

import java.util.Objects;

public enum VariableKpiRuleEnum {
  SCROLL(1, "滑动"),
  LIMIT(2, "限制"),
  HEAD_LIMIT(3, "前N行"),
  ;

  private final Integer value;
  private final String name;

  VariableKpiRuleEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    VariableKpiRuleEnum[] values = VariableKpiRuleEnum.values();
    for (VariableKpiRuleEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value.getName();
      }
    }
    return "";
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
