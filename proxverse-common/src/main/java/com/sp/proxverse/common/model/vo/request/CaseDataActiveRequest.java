package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("案例数据active请求对象")
public class CaseDataActiveRequest {

  @NotBlank(message = "{400007}")
  private String caseId;

  private String activeName;

  private String activeTime;

  private String activeIndex;

  @ApiModelProperty(value = "业务主题表格ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  // 操作：2-页面查询、3-功能查询、不传默认事件查询
  public Integer eventIndex;
}
