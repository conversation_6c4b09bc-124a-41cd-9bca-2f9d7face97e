package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("指挥舱全景图列表VO对象")
public class OverallOutputVO {
  @Tolerate
  public OverallOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "名称")
  private String name;

  @ApiModelProperty(value = "更新时间")
  private String updateTimeZh;

  @ApiModelProperty(value = "主题ID")
  private Integer topicId;

  @ApiModelProperty(value = "主题类型（100:全景图，101：业务全景图，200：业务主题，201:业务分析图谱，202：业务知识模型）")
  private Integer type;
}
