package com.sp.proxverse.common.model.vo.datadraw.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("删除数据提取请求对象")
public class DeleteDataExtratorRequest {

  @ApiModelProperty(value = "数据提取ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataExtractorId;
}
