package com.sp.proxverse.common.model.dto.processoverview;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/2/22 10:11
 */
@Getter
@Setter
public class KpiInfo {
  private Integer kpiId;
  private String kpiName;
  private String expression;
  private String unit;
  private String value;
  private String up;
  private String down;
  private Integer initType;

  public KpiInfo() {}

  public KpiInfo(
      Integer kpiId,
      String kpiName,
      String expression,
      String unit,
      String value,
      String up,
      String down) {
    this.kpiId = kpiId;
    this.kpiName = kpiName;
    this.expression = expression;
    this.unit = unit;
    this.value = value;
    this.up = up;
    this.down = down;
  }

  public KpiInfo(
      Integer kpiId,
      String kpiName,
      String expression,
      String unit,
      String value,
      String up,
      String down,
      Integer initType) {
    this.kpiId = kpiId;
    this.kpiName = kpiName;
    this.expression = expression;
    this.unit = unit;
    this.value = value;
    this.up = up;
    this.down = down;
    this.initType = initType;
  }
}
