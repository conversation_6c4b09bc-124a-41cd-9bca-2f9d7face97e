package com.sp.proxverse.common.model.vo.fileUpload;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2022-09-05 6:06 下午
 */
@Data
@ApiModel("文件合并请求")
public class FileMergeReqVo {

  @ApiModelProperty("文件标识符")
  private String identifier;

  @ApiModelProperty("块数量")
  private Integer chunkNumber;

  @ApiModelProperty("文件后缀")
  private String suffix;

  @ApiModelProperty("文件名称")
  private String fileName;

  public String getFileName() {
    if (StringUtils.isNotBlank(fileName)) {
      return fileName;
    } else {
      return identifier;
    }
  }

  public Integer getChunkNumber() {
    if (this.getCheckFlag()) {
      return this.chunkNumber;
    }
    return 999;
  }

  /**
   * 检查标识
   *
   * @return
   */
  public boolean getCheckFlag() {
    return this.chunkNumber != null && this.chunkNumber != 0;
  }
}
