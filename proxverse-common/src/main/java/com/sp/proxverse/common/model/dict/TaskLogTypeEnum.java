package com.sp.proxverse.common.model.dict;

import io.swagger.annotations.ApiModel;

@ApiModel("任务日志状态枚举类")
public enum TaskLogTypeEnum {
  NO_DELETED(0, "任务分配"),
  HAS_DELETED(1, "状态"),
  ;

  private final Integer value;
  private final String name;

  TaskLogTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
