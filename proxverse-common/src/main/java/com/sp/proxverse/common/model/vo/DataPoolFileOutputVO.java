package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("数据池中文件展示VO对象")
public class DataPoolFileOutputVO {
  @Tolerate
  public DataPoolFileOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据池ID")
  private Integer dataId;

  @ApiModelProperty(value = "文件名称（不带后缀）")
  private String name;

  @ApiModelProperty(value = "文件ID")
  private Integer fileId;

  @ApiModelProperty(value = "文件名称")
  private String fileName;

  @ApiModelProperty(value = "更新时间转换成常规格式")
  private String createTimeZh;

  private String fileSize;

  private String parquetSize;

  private String fileSizeUnit;

  @ApiModelProperty(value = "导入状态（1：成功，2：失败）")
  private String status;

  private Integer statusValue;

  private Boolean refresh;

  private String suffix;

  private String errorMessage;
}
