package com.sp.proxverse.common.action;

import com.sp.proxverse.common.action.bo.ActionDoParamBo;
import com.sp.proxverse.common.action.bo.ActionDoResultBo;
import java.io.IOException;

/**
 * 动作服务
 *
 * <AUTHOR>
 * @create 2022-05-09 1:56 下午
 */
public interface ActionService {

  /**
   * 执行函数
   *
   * @param actionParamBo
   * @return
   * @throws IOException
   */
  ActionDoResultBo doAction(ActionDoParamBo actionParamBo) throws IOException;
}
