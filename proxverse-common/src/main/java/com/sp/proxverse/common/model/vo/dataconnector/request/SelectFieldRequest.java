package com.sp.proxverse.common.model.vo.dataconnector.request;

import com.sp.proxverse.common.model.enums.UpdateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("提交jdbc字段选择请求对象")
public class SelectFieldRequest {

  @Tolerate
  public SelectFieldRequest() {
    // comment empty
  }

  private Integer dataConnectorId;

  @ApiModelProperty(value = "提取器ID", required = true)
  private Integer dataExtractorId;

  @ApiModelProperty(value = "表名", required = true)
  private String tableName;

  /** @see UpdateTypeEnum */
  @ApiModelProperty(value = "1：增量更新（默认），2：全量更新")
  private Integer updateType;

  public Integer getUpdateType() {
    if (updateType == null) {
      return 1;
    }
    return updateType;
  }

  @ApiModelProperty(value = "规则 1.6.3改动")
  private String whereRule;

  @ApiModelProperty(value = "主键字段", required = true)
  private List<SelectFieldSubRequest> fieldList;
}
