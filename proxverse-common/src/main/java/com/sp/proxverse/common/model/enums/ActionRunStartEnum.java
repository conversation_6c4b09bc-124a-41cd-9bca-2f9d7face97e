package com.sp.proxverse.common.model.enums;

import com.sp.proxverse.common.exception.DataException;
import com.sp.proxverse.common.exception.ErrorCode;

/**
 * <AUTHOR>
 * @create 2022-05-19 10:42 上午
 */
public enum ActionRunStartEnum {
  /** 当前执行状态 */
  IN_EXECUTION("inExecution", 100, "正在执行"),
  EXECUTED("executed", 200, "执行完成"),
  NOT_EXECUTION("notExecution", 300, "未执行"),

  /** 执行结果状态 */
  UNKNOWN("unknown", -1, "未知状态"),
  SUCCESS("success", 0, "执行成功"),
  FAILURE("failure", 1, "执行失败"),
  ;

  public String displayName;

  public String describe;

  public Integer code;

  ActionRunStartEnum(String displayName, Integer code, String describe) {
    this.displayName = displayName;
    this.describe = describe;
    this.code = code;
  }

  public static ActionRunStartEnum getActionRunStartEnumByCode(Integer code) {
    for (ActionRunStartEnum value : ActionRunStartEnum.values()) {
      if (value.code.equals(code)) {
        return value;
      }
    }
    throw new DataException(ErrorCode.ERROR_SYSTEM, "not support this type！");
  }
}
