package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ReportVO {

  @ApiModelProperty("报告ID")
  private Integer id;

  @ApiModelProperty("租户ID")
  private Integer tenantId;

  @ApiModelProperty("主题ID")
  private Integer topicId;

  @ApiModelProperty("表格ID")
  private Integer sheetId;

  @ApiModelProperty("组件ID")
  private Integer componentId;

  @ApiModelProperty("报告标题")
  private String title;

  @ApiModelProperty("报告类型")
  private String type;

  @ApiModelProperty("状态: 1-正常, 0-禁用")
  private Integer status;

  @ApiModelProperty("报告内容路径")
  private String reportPath;

  @ApiModelProperty("创建时间")
  private Date createTime;
}
