package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.dto.DataModelDTO;
import com.sp.proxverse.common.model.dto.DataModelFileDTO;
import com.sp.proxverse.common.model.dto.TopicFileDTO;
import com.sp.proxverse.common.model.po.BusinessTopicDataPO;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

@CacheNamespace
public interface BusinessTopicDataMapper extends BaseMapper<BusinessTopicDataPO> {

  List<DataModelDTO> getDataModelListByTopicId(@Param("topicId") Integer topicId);

  TopicFileDTO getTopicFileByTopicId(@Param("topicId") Integer topicId);

  List<DataModelFileDTO> getDataModelFileListByTopicId(@Param("topicId") Integer topicId);

  List<Integer> getKnowledgeIdListByModelIds(@Param("modelIds") List<Integer> modelIds);
}
