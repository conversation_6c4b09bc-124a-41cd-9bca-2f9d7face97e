package com.sp.proxverse.common.model.vo.datamodel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("异步加载连线后的数据请求对象")
public class AsyncLoadDataRequest {

  @ApiModelProperty(value = "数据模型ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataModelId;
}
