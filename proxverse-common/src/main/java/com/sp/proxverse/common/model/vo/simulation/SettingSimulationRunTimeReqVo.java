package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-22 14:57
 */
@Data
@ApiModel("设置仿真中事件的执行时间")
public class SettingSimulationRunTimeReqVo {

  @ApiModelProperty("方案Id")
  @NotNull(message = "{400006}")
  private Integer programmeId;

  @ApiModelProperty("执行时间，分钟")
  @NotNull(message = "{400009}")
  private String runTime;

  @ApiModelProperty("事件Id")
  @NotNull(message = "{400006}")
  private Integer eventId;

  private String expression;

  private String formatting;

  private String format;
}
