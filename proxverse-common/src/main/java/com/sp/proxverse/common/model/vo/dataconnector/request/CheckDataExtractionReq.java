package com.sp.proxverse.common.model.vo.dataconnector.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("检查数据链接")
public class CheckDataExtractionReq extends SelectFieldRequest {

  @ApiModelProperty(value = "数据提取名称", required = true)
  private String name;

  @ApiModelProperty(value = "数据链接ID")
  private Integer dataConnectorId;

  @ApiModelProperty(value = "数据池", required = true)
  @NotNull(message = "数据池ID不能为空")
  private Integer poolId;
}
