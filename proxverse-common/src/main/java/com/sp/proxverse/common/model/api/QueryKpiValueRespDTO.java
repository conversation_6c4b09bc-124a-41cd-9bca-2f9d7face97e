package com.sp.proxverse.common.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询kpi值VO对象")
public class QueryKpiValueRespDTO {
  @Tolerate
  public QueryKpiValueRespDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "kpi目标值")
  private String target;

  @ApiModelProperty(value = "更新时间")
  private String updateTime;

  @ApiModelProperty(value = "kpi单位")
  private String unit;
}
