package com.sp.proxverse.common.model.dict;

import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.util.I18nUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

public enum FileTypeEnum {
  /** 文件类型 */
  CSV(1, "csv", true),
  XES(2, "xes", true),
  JDBC(30, "jdbc", true),
  KAFKA(40, "kafka", true),
  IMAGE(3, "IMAGE", false),
  IMAGE_BASE(4, "IMAGE_BASE", false),
  XML_BASE(5, "XML_BASE", false),
  TRANSFORMATION(6, "TRANSFORMATION", false),

  ATTACHMENT(7, "ATTACHMENT", false),
  PROCESS_DESCRIPTION(8, "PROCESS_DESCRIPTION", false),

  PARQUET(9, "parquet", true),
  DEL(10, "del", true),
  ZIP(11, "zip", true),
  XLSX(12, "xlsx", true),
  INC_CSV(13, "inc-csv", true),
  FULL_CSV(14, "full-csv", true),
  ;

  private final Integer value;
  private final String name;

  private boolean isModelFile;

  FileTypeEnum(Integer value, String name, boolean isModelFile) {
    this.value = value;
    this.name = name;
    this.isModelFile = isModelFile;
  }

  /**
   * 根据文件后缀名得到枚举int值
   *
   * @param f
   * @return
   */
  public static Integer filterFileType(String f) {
    FileTypeEnum fileTypeEnum =
        Stream.of(values()).filter(e -> e.getName().equalsIgnoreCase(f)).findFirst().orElse(null);
    return Objects.isNull(fileTypeEnum) ? null : fileTypeEnum.getValue();
  }

  private static List<String> dataPoolImportFileType = new ArrayList<>();
  private static List<Integer> dataPoolImportFileTypeValue = new ArrayList<>();

  static {
    dataPoolImportFileType.add(CSV.getName());
    dataPoolImportFileType.add(XLSX.getName());
    dataPoolImportFileType.add(DEL.getName());
    dataPoolImportFileType.add(PARQUET.getName());
    dataPoolImportFileType.add(ZIP.getName());

    dataPoolImportFileTypeValue.add(CSV.getValue());
    dataPoolImportFileTypeValue.add(DEL.getValue());
    dataPoolImportFileTypeValue.add(PARQUET.getValue());
    dataPoolImportFileTypeValue.add(ZIP.getValue());
  }

  public static List<String> getDataPoolImportFileType() {
    return dataPoolImportFileType;
  }

  public static List<Integer> getDataPoolImportFileValue() {
    return dataPoolImportFileTypeValue;
  }

  public static FileTypeEnum getTypeByName(String fileType) {
    for (FileTypeEnum value : FileTypeEnum.values()) {
      if (Objects.equals(fileType, value.name)) {
        return value;
      }
    }
    throw new BizException(5000, I18nUtil.getMessage(I18nConst.TYPE_NOT_FOUND));
  }

  public static FileTypeEnum getTypeByValue(Integer fileTypeValue) {
    if (fileTypeValue == null) {
      return null;
    }
    for (FileTypeEnum value : FileTypeEnum.values()) {
      if (Objects.equals(fileTypeValue, value.value)) {
        return value;
      }
    }
    return null;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
