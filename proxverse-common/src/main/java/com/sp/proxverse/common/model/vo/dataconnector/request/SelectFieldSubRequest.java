package com.sp.proxverse.common.model.vo.dataconnector.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("提交jdbc字段选择请求子对象")
public class SelectFieldSubRequest {

  @Tolerate
  public SelectFieldSubRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "表名", required = true)
  @NotEmpty(message = "{400007}")
  private String field;

  /** @see DataTypeEnum */
  @ApiModelProperty(value = "表字段类型", required = true)
  @NotNull(message = "{400008}")
  private Integer fieldType;

  @ApiModelProperty(value = "表名别名", required = true)
  @NotEmpty(message = "{400007}")
  private String fieldAlias;

  @ApiModelProperty(value = "表达式", required = true)
  private String expression;

  @ApiModelProperty(value = "主键字段")
  private Integer hasPriField;

  @ApiModelProperty(value = "增量时间列字段")
  private Integer hasUpdateTimeField;
}
