package com.sp.proxverse.common.model.dto.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/** <AUTHOR> */
@Data
@Accessors(chain = true)
public class BaseEntity {

  private static final long serialVersionUID = 1L;

  /** 自增 id */
  @TableId(value = "id", type = IdType.AUTO)
  Integer id;
}
