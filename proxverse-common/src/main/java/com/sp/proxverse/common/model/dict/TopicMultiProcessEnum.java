package com.sp.proxverse.common.model.dict;

/**
 * 操作枚举：2-页面查询、3-功能查询、不传默认事件查询
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
public enum TopicMultiProcessEnum {
  EVENT(1, "事件"),
  PAGE(2, "页面"),
  FUNCTION(3, "功能"),
  ;

  private final Integer value;
  private final String name;

  TopicMultiProcessEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
