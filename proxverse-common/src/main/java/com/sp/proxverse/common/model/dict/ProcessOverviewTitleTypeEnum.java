package com.sp.proxverse.common.model.dict;

public enum ProcessOverviewTitleTypeEnum {
  OVERVIEW(1, "流程概览"),
  STANDARDIZATION(2, "流程标准化"),
  AGEING(3, "流程时效"),
  REWORK(4, "流程返工"),
  KPI(5, "流程-kpi"),
  ;

  private final Integer value;
  private final String name;

  ProcessOverviewTitleTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
