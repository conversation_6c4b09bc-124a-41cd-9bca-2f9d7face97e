package com.sp.proxverse.common.model.base;

import com.sp.proxverse.common.exception.BaseErrorCode;
import com.sp.proxverse.common.exception.BaseException;
import com.sp.proxverse.common.exception.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import org.apache.commons.lang3.StringUtils;

@ApiModel("请求返回数据")
public class Response<T> implements Serializable {

  private static final long serialVersionUID = -5661206328302037980L;

  @ApiModelProperty("错误码")
  private Integer code;

  @ApiModelProperty("错误信息")
  private String msg;

  @ApiModelProperty("业务数据")
  private transient T data;

  public Response() {}

  public Response(T data) {
    this.data = data;
    code = 2000;
  }

  public Integer getCode() {
    return code;
  }

  public void setCode(Integer code) {
    this.code = code;
  }

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public T getData() {
    return data;
  }

  public void setData(T data) {
    this.data = data;
  }

  public static <R> Response<R> success(String msg, R body) {
    Response<R> rtn = new Response<>();
    rtn.setCode(2000);
    rtn.setMsg(msg);
    rtn.setData(body);
    return rtn;
  }

  public static <R> Response<R> success(R body) {
    Response<R> rtn = new Response<>();
    rtn.setCode(2000);
    rtn.setData(body);
    return rtn;
  }

  public static <R> Response<R> fail() {
    Response<R> rtn = new Response<>();
    rtn.setCode(ErrorCode.SYSTEM_ERROR.getCode());
    rtn.setMsg(ErrorCode.SYSTEM_ERROR.getDesc());
    return rtn;
  }

  public static <R> Response<R> fail(ErrorCode errorCode) {
    if (errorCode == null) {
      return null;
    } else {
      Response<R> rtn = new Response<>();
      rtn.setCode(errorCode.getCode());
      rtn.setMsg(errorCode.getDesc());
      return rtn;
    }
  }

  public static <R> Response<R> fail(BaseException exception) {
    Response<R> rtn = new Response<>();
    rtn.setCode(exception.getCode());
    rtn.setMsg(exception.getMessage());
    return rtn;
  }

  public static <R> Response<R> fail(BaseErrorCode errorCode, R body) {
    if (errorCode == null) {
      return null;
    } else {
      Response<R> rtn = new Response<>();
      rtn.setCode(errorCode.getCode());
      rtn.setMsg(errorCode.getDesc());
      rtn.setData(body);
      return rtn;
    }
  }

  public static <R> Response<R> failOfMsg(ErrorCode errorCode, String msg) {
    if (errorCode == null) {
      return null;
    } else {
      Response<R> rtn = new Response<>();
      rtn.setCode(errorCode.getCode());
      rtn.setMsg(errorCode.getDesc());
      if (StringUtils.isNotBlank(msg)) {
        rtn.setMsg(msg);
      }
      return rtn;
    }
  }

  public static <R> Response<R> failOfMsg(String msg) {
    Response<R> rtn = new Response<>();
    rtn.setCode(5000);
    rtn.setMsg(msg);
    if (StringUtils.isNotBlank(msg)) {
      rtn.setMsg(msg);
    }
    return rtn;
  }
}
