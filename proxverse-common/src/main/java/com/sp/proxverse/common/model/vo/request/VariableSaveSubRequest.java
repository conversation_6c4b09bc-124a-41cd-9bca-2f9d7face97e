package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("新建或编辑参数项子对象请求对象")
public class VariableSaveSubRequest {

  @Tolerate
  public VariableSaveSubRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "参数项名称", required = true)
  @NotBlank(message = "{400007}")
  private String variable;

  @ApiModelProperty(value = "文件ID（即表ID）", required = true)
  @NotNull(message = "{400006}")
  private Integer fileId;

  @ApiModelProperty(value = "字段ID（即表ID）", required = true)
  @NotNull(message = "{400006}")
  private Integer fieldId;

  private String fileName;

  @ApiModelProperty(value = "自定义 kpi")
  private String refParam;
}
