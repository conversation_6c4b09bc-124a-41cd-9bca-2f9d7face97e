package com.sp.proxverse.common.model.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/2/18 13:49
 */
public enum ProcessChartValueTypeEnum {
  AVERAGE_DURATION(1, true),
  NUMBER(2, false),
  AVERAGE_DURATION_AND_NUMBER(3, false),
  MEDIAN_DURATION(4, true),
  EVENT_NUMBER(5, false),
  NUMBER_AND_AVG_DURATION(6, false),
  EVENT_NUMBER_AND_MEDIAN_DURATION(7, false);

  private Integer value;

  private Boolean isTime;

  public static ProcessChartValueTypeEnum getProcessChartValueTypeEnumByValue(Integer value) {
    if (value == null) {
      return null;
    }
    for (ProcessChartValueTypeEnum processChartValueTypeEnum : ProcessChartValueTypeEnum.values()) {
      if (Objects.equals(processChartValueTypeEnum.getValue(), value)) {
        return processChartValueTypeEnum;
      }
    }
    return null;
  }

  public static ProcessChartValueTypeEnum getDefaultType() {
    return ProcessChartValueTypeEnum.NUMBER;
  }

  ProcessChartValueTypeEnum(Integer value, Boolean isTime) {
    this.value = value;
    this.isTime = isTime;
  }

  public Integer getValue() {
    return this.value;
  }

  public Boolean getIsTime() {
    return this.isTime;
  }
}
