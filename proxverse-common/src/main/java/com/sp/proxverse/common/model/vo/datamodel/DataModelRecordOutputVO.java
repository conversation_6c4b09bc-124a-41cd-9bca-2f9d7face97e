package com.sp.proxverse.common.model.vo.datamodel;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据模型日志VO对象")
public class DataModelRecordOutputVO {
  @Tolerate
  public DataModelRecordOutputVO() {
    // comment empty
  }

  private Integer userId;

  private String userName;

  private String name;

  private String timeZh;

  private Integer type;

  private String typeZh;
}
