package com.sp.proxverse.common.model.dict;

import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

public enum ExpressionEnum {
  COUNT("count", 1, "总数量"),
  AVG("avg", 2, "平均值"),
  SUM("sum", 3, "总和"),
  ;

  private final String expression;
  private final Integer type;
  private final String name;

  public static boolean checkExpre(String expre) {
    if (StringUtils.isBlank(expre)) {
      return false;
    }
    String lowerCase = expre.toLowerCase();
    if (lowerCase.contains(COUNT.getExpression())) {
      return true;
    }
    if (lowerCase.contains(AVG.getExpression())) {
      return true;
    }
    if (lowerCase.contains(SUM.getExpression())) {
      return true;
    }
    return false;
  }

  public static ExpressionEnum getExpreType(String expre) {
    ExpressionEnum[] values = values();
    for (ExpressionEnum e : values) {
      if (Objects.equals(e.getExpression().toLowerCase(), expre)) {
        return e;
      }
    }
    return null;
  }

  ExpressionEnum(String expression, Integer type, String name) {
    this.expression = expression;
    this.type = type;
    this.name = name;
  }

  public Integer getType() {
    return type;
  }

  public String getExpression() {
    return expression;
  }

  public String getName() {
    return name;
  }
}
