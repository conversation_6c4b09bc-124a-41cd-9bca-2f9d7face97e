package com.sp.proxverse.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.po.FileVariantPO;
import org.apache.ibatis.annotations.Param;

public interface FileVariantPOMapper extends BaseMapper<FileVariantPO> {
  Integer sumVariantCount(@Param("fileId") Integer fileId, @Param("topicId") Integer topicId);

  Integer sumVariantSkipCount(@Param("fileId") Integer fileId, @Param("topicId") Integer topicId);
}
