package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-21 14:46
 */
@Data
@ApiModel("仿真方案事件信息响应")
public class SimulationEventByProgrammeResVo {

  @ApiModelProperty("事件名称")
  private String eventName;

  @ApiModelProperty("执行时间")
  private Integer runTime;

  @ApiModelProperty("执行成本")
  private Integer runCost;

  @ApiModelProperty("事件Id")
  private Integer eventId;

  @ApiModelProperty("事件属性Id")
  private Integer eventAttrId;

  @ApiModelProperty("事件元素Id")
  private String eventElementId;

  @ApiModelProperty("事件类型 -2：结束 -1：开始， 0:事件 ，1：专属网关（开始），2：并行网关（开始）， 11：专属网关（结束），12：并行网关（结束）")
  private Integer eventType;

  private String runCostExpression;

  private String runCostFormatting;

  private String runCostFormat;

  private String runTimeExpression;

  private String runTimeFormatting;

  private String runTimeFormat;
}
