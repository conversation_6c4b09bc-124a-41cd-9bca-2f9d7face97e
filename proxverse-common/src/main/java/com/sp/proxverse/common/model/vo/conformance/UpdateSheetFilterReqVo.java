package com.sp.proxverse.common.model.vo.conformance;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-08-08 6:35 下午
 */
@Data
public class UpdateSheetFilterReqVo {

  @ApiModelProperty("sheetId")
  @NotNull(message = "sheetId不能为空")
  private Integer sheetId;

  @ApiModelProperty("参数值")
  private String paramValue;
}
