package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询链接器4select请求对象")
public class QueryDataConnector4SelectRequest {

  @ApiModelProperty(value = "数据池ID", required = true)
  @NotNull(message = "{400006}")
  private Integer poolId;
}
