package com.sp.proxverse.common.model.dict;

import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.util.I18nUtil;
import java.util.Objects;

/** 连接的数据库类型 */
public enum DataConnectorJDBCTypeEnum {
  MYSQL(1, "mysql", "com.mysql.cj.jdbc.Driver"),
  ORACLE(2, "oracle", "oracle.jdbc.driver.OracleDriver"),
  DB2(3, "db2", "com.microsoft.sqlserver.jdbc.SQLServerDriver"),
  HIVE(4, "hive", "org.apache.hive.jdbc.HiveDriver"),
  POSTGRESQL(5, "postgresql", "org.postgresql.Driver"),
  // impala(6, "impala",""),
  SQLSERVER(7, "sqlserver", "com.microsoft.sqlserver.jdbc.SQLServerDriver"),
  SQP_HANA(8, "sap_hana", "com.sap.db.jdbc.Driver"),
  GAUSS_DB_MYSQL(101, "gaussDB_mysql", "com.mysql.cj.jdbc.Driver"),
  GAUSS_DB_POSTGRESQL(105, "gaussDB_postgresql", "org.postgresql.Driver"),
  ;

  private final Integer value;
  private final String name;
  private final String driver;

  public static boolean needSchemaName(Integer value) {
    if (Objects.equals(value, DB2.getValue())
        || Objects.equals(value, POSTGRESQL.getValue())
        || Objects.equals(value, GAUSS_DB_POSTGRESQL.getValue())
        || Objects.equals(value, SQLSERVER.getValue())
        || Objects.equals(value, ORACLE.getValue())
        || Objects.equals(value, SQP_HANA.getValue())) {
      return true;
    }
    return false;
  }

  public static String getDescByValue(Integer value) {
    DataConnectorJDBCTypeEnum[] values = DataConnectorJDBCTypeEnum.values();
    for (DataConnectorJDBCTypeEnum type : values) {
      if (type.value.equals(value)) {
        return type.name;
      }
    }
    throw new UnsupportedOperationException(
        I18nUtil.getMessage(I18nConst.DATA_CONNECTOR_NOT_EXIST) + " jdbcType=" + value);
  }

  DataConnectorJDBCTypeEnum(Integer value, String name, String driver) {
    this.value = value;
    this.name = name;
    this.driver = driver;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  public String getDriver() {
    return driver;
  }
}
