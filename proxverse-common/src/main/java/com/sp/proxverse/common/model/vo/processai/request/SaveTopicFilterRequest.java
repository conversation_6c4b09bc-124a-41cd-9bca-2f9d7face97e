package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("保存过滤条件请求对象,v1.1")
public class SaveTopicFilterRequest {

  @Tolerate
  public SaveTopicFilterRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "编辑回显再提交时，需要传此字段")
  private Integer prikey;

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty(
      value =
          "运算符号类型（10：>=，11：>，20：<=，21：<，30：=，40：time勾选，41：time的范围，50：like，51：not like）(事件过滤此字段不填)，type=700时此字段可填可不填")
  private Integer operationType;

  @ApiModelProperty(value = "运算符号值")
  private String operationValue;

  @ApiModelProperty(value = "过滤项类型集合", required = true)
  private List<SaveTopicFilterSubRequest> list;

  @ApiModelProperty(value = "1：创建topic时添加过滤项")
  private Integer bizType;

  @ApiModelProperty(value = "是否取反（空或者0：否，1：是）")
  private Integer hasInvert;

  @ApiModelProperty(value = "此字段不为空说明是从newsheet里添加的过滤项（则同newsheet里相同字段的过滤项要覆盖）")
  private Integer componentId;

  private Integer sheetId;

  private String formatting;

  private String format;

  private Boolean isConvertPQL;

  private Integer enableJump;

  private String extension;
}
