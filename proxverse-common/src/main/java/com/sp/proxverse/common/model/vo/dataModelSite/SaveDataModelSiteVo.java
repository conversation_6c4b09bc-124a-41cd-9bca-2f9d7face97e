package com.sp.proxverse.common.model.vo.dataModelSite;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-04-19 5:10 下午
 */
@Getter
@Setter
@ApiModel("保存数据模型位置")
public class SaveDataModelSiteVo {

  @ApiModelProperty(value = "数据模型ID")
  @NotNull(message = "{400006}")
  private Integer dataModelId;

  @ApiModelProperty(value = "文件Id")
  @NotNull(message = "{400006}")
  private Integer fileId;

  @ApiModelProperty(value = "topRatio")
  private String topRatio;

  @ApiModelProperty(value = "leftRatio")
  private String leftRatio;

  @ApiModelProperty(value = "topValue")
  private String topValue;

  @ApiModelProperty(value = "leftValue")
  private String leftValue;
}
