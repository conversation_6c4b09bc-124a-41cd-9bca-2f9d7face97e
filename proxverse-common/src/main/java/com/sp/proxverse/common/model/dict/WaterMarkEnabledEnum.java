package com.sp.proxverse.common.model.dict;

public enum WaterMarkEnabledEnum {
  USERNAME_TIME(1, "用户 时间"),
  NAME_TIME(2, "名称 时间"),
  ;

  private final Integer value;
  private final String name;

  WaterMarkEnabledEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
