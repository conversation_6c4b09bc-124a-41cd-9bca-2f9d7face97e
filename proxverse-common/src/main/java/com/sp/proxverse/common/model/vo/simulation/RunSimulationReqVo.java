package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-23 17:04
 */
@Data
@ApiModel("运行流程仿真")
public class RunSimulationReqVo {

  @ApiModelProperty("方案Id")
  @NotNull(message = "{400006}")
  private Integer programmeId;

  @ApiModelProperty("运行时间：单位天")
  @NotNull(message = "{400009}")
  private Integer runTime;

  private Integer tenantId;

  private Integer dataTaskId;
}
