package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.po.TopicFilterPO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class DataModelFileDTO implements Serializable {

  private static final long serialVersionUID = 3055733217641857609L;

  @Tolerate
  public DataModelFileDTO() {
    // comment empty
  }

  private String fileName;

  private Integer id;

  @ApiModelProperty("所属数据模型ID")
  private Integer dataModelId;

  @ApiModelProperty("所属文件ID")
  private Integer fileId;

  @ApiModelProperty("所属文件ID")
  private Integer topicId;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("0：非主表，1：主表")
  private Integer active;

  private String fromSql;

  private String fromSqlAlias;

  private String onAlias;

  private Boolean hasBizTopicFilter;

  private List<TopicFilterPO> addFilterList;

  private List<Integer> excludeFilterList;
}
