package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
@ApiModel("创建业务主题前置请求实体类")
public class SaveBusinessTopicPreRequest {

  // todo 如果是二级主题选数据源则不能选全量，要把一级主题ID传过来，在一级主题数据源内选择

  @ApiModelProperty(value = "业务主题ID，此字段如果有值则是创建二级主题")
  private Integer businessTopicId;

  @ApiModelProperty(value = "业务主题名称，创建时必填")
  @NotBlank(message = "{400007}")
  private String name;

  @ApiModelProperty(value = "主题类型（1:全景图，2：业务全景图，3：业务主题，4:业务分析图谱，5：业务知识模型），创建时必填")
  private Integer type;
}
