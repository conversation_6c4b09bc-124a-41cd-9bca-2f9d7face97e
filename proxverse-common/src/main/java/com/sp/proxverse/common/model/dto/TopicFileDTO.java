package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class TopicFileDTO implements Serializable {

  @Tolerate
  public TopicFileDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "文件表主题ID")
  private Integer fileId;

  @ApiModelProperty(value = "文件表主题ID")
  private Integer topicId;

  @ApiModelProperty(value = "文件表主题ID")
  private Integer dataModelId;

  @ApiModelProperty(value = "文件唯一名，传到python处理的")
  private String uniqFileName;

  @ApiModelProperty(value = "文件名称")
  private String filename;

  @ApiModelProperty(value = "文件属性字段")
  private String attributes;

  private Integer knowledgeableId;
}
