package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("根因分析输出结果对象DTO")
public class RootCauseResultDTO {

  @Tolerate
  public RootCauseResultDTO() {
    // comment empty
  }

  @ApiModelProperty("相关变量值")
  private String variant;

  @ApiModelProperty("此相关变量数据所占百分比")
  private String value;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("此相关变量在kpi中计算得出的值和单位")
  private List<Map<String, String>> valueList;
}
