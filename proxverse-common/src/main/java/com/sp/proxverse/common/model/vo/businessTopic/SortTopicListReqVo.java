package com.sp.proxverse.common.model.vo.businessTopic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
@ApiModel("保存导航拦快照请求对象")
public class SortTopicListReqVo {

  @ApiModelProperty(value = "topicId")
  @NotBlank(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty(value = "子topicId数组")
  @NotBlank(message = "{400010}")
  private List<SortTopicListReqVo> subPackageList;
}
