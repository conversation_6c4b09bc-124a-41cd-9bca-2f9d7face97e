package com.sp.proxverse.common.model.vo.datatask;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询数据任务日志请求对象")
public class QueryDataTaskLogRequest extends PageRequest {

  @ApiModelProperty(value = "数据任务ID", required = true)
  @NotNull(message = "{400006}")
  private Integer poolId;
}
