package com.sp.proxverse.common.model.enums;

import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2023-03-20 20:44
 */
public enum AuthorityClientEnum {
  OKTA("OKTA", "OKTA"),
  LOCAL("本地", "100236"),
  LDAP("LDAP", "LDAP"),
  SAP("SAP", "SAP"),
  BYTEDANCE("BYTEDANCE", "BYTEDANCE"),

  UNKNOWN("", "");

  public String value;
  public String msgKey;

  AuthorityClientEnum(String value, String msgKey) {
    this.value = value;
    this.msgKey = msgKey;
  }

  public String getValue() {
    return this.value;
  }

  public String getMsgKey() {
    return this.msgKey;
  }

  public static AuthorityClientEnum getAuthorityClientEnumByName(String name) {
    if (StringUtils.isBlank(name)) {
      return UNKNOWN;
    }
    for (AuthorityClientEnum value : AuthorityClientEnum.values()) {
      if (Objects.equals(value.name(), name)) {
        return value;
      }
    }
    return UNKNOWN;
  }
}
