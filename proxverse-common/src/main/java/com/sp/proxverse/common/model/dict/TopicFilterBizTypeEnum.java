package com.sp.proxverse.common.model.dict;

public enum TopicFilterBizTypeEnum {
  BIZ_CREATE(0, "业务添加topic filter"),
  INIT(1, "创建topic初始化filter"),
  PROCESS_LEFT(20, "流程对比-左"),
  PROCESS_RIGHT(21, "流程对比-右"),
  SHEET_FILTER(30, "sheet过滤项");

  private final Integer value;
  private final String name;

  TopicFilterBizTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
