package com.sp.proxverse.common.model.vo.dataconnector.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("根据表名查询字段列表请求对象")
public class QueryFieldListRequest {

  @ApiModelProperty(value = "数据链接ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataConnectorId;

  @ApiModelProperty(value = "表名", required = true)
  @NotEmpty(message = "{400007}")
  private String tableName;
}
