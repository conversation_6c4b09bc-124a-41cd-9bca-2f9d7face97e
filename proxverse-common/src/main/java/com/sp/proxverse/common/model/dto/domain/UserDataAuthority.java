package com.sp.proxverse.common.model.dto.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * 用户数据权限
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
@TableName("t_user_data_authority")
public class UserDataAuthority implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 数据权限ID */
  private Integer dataAuthorityId;

  private String filter;

  /** 用户Id */
  private Integer userId;

  private Integer userGroupId;

  /** 权限值：2：读权限，4：修改权限，8：分配权限；具体的值是权限之和 */
  private Integer authorityValue;

  /** 创建人Id */
  private Integer createUser;

  /** 0：未删除，1：已删除 */
  private Integer deleted;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  /**
   * 根据权限值进行拆分2进制数据拆分
   *
   * @param int1
   * @return
   */
  public static List<Integer> getAuthorityValues(Integer int1) {
    if (int1 == null || int1 == 0) {
      return new ArrayList<>();
    }
    List<Integer> bases = new ArrayList<>();
    bases.add(2);
    bases.add(4);
    bases.add(8);
    bases.add(16);
    bases.add(32);
    bases.add(64);

    List<Integer> results = new ArrayList<>();

    for (Integer base : bases) {
      if (base == (int1 & base)) {
        results.add(base);
      }
    }
    return results;
  }
}
