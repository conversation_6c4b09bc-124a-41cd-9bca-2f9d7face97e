package com.sp.proxverse.common.model.vo.businessTopic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@Data
@ApiModel("保存导航拦快照请求对象")
public class SortSheetListReqVo {

  @ApiModelProperty(value = "sheetIdList")
  @NotEmpty(message = "{400006}")
  private List<Integer> sheetIdList;
}
