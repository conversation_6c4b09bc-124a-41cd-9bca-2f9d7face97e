package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("业务主题列表VO对象")
public class BusinessTopicOutputVO {
  @Tolerate
  public BusinessTopicOutputVO() {
    this.authorityValues = new ArrayList();
  }

  @ApiModelProperty(value = "当前数据ID")
  private Integer topicId;

  @ApiModelProperty(value = "当前业务主题数据名称")
  private String name;

  @ApiModelProperty(value = "主题类型（100:全景图，101：业务全景图，200：业务主题，201:业务分析图谱，202：业务知识模型）")
  private Integer type;

  @ApiModelProperty(value = "当前业务主题数据下的子数据集合")
  private List<BusinessTopicOutputVO> subPackageList;

  @ApiModelProperty("流程仿真Id")
  private Integer simulationId;

  @ApiModelProperty(value = "权限值：2：读权限，4：修改权限，8：分配权限和", required = true)
  private List authorityValues;

  @ApiModelProperty(value = "权限值：2：读权限，4：修改权限，8：分配权限", required = true)
  private String authorityValue;

  private Integer dataModelId;

  public void setAuthorityValue(Integer authorityValueInt) {
    this.authorityValue = authorityValueInt.toString();
  }
}
