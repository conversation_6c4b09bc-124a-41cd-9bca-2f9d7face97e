package com.sp.proxverse.common.model.vo.dataconnector.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("创建数据提取请求对象")
public class SaveDataDrawRequest {

  @ApiModelProperty(value = "数据提取名称", required = true)
  @NotBlank(message = "{400007}")
  private String name;

  @ApiModelProperty(value = "类型（1：jdbc，2：kafka）", required = true)
  @NotNull(message = "类型不能为空")
  private Integer type;

  @ApiModelProperty(value = "数据链接ID")
  private Integer dataConnectorId;

  @ApiModelProperty(value = "数据池", required = true)
  @NotNull(message = "数据池ID不能为空")
  private Integer poolId;

  @ApiModelProperty(value = "链接信息", required = true)
  private DataExtractionPreviewReq dataExtractionPreviewReq;
}
