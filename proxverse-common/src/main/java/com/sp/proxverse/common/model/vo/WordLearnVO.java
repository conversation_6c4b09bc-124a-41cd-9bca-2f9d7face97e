package com.sp.proxverse.common.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class WordLearnVO {

  private Integer id;
  private String kind;

  @TableField(typeHandler = JacksonTypeHandler.class)
  private List<String> symbols;

  @TableField(typeHandler = JacksonTypeHandler.class)
  private List<String> synonyms;

  private String name;

  private String prompt;

  private Integer topicId;
  private Integer status;
}
