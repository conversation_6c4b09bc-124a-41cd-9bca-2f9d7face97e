package com.sp.proxverse.common.action.enums;

/**
 * 动作跟节点路由
 *
 * <AUTHOR>
 * @create 2022-05-16 11:59 上午
 */
public enum ActionRootTypeEnum {

  /** 根节点枚举 */
  TOOL(1000, "tool", "工具"),

  HTTP(2000, "http", "HTTP"),

  EMAIL(3000, "email", "邮件"),

  WECHAT(4000, "wechat", "企业微信");

  private Integer code;

  private String name;

  private String describe;

  ActionRootTypeEnum(Integer code, String name, String describe) {
    this.code = code;
    this.name = name;
    this.describe = describe;
  }

  public Integer getCode() {
    return this.code;
  }

  public String getDescribe() {
    return describe;
  }

  public String getName() {
    return this.name;
  }
}
