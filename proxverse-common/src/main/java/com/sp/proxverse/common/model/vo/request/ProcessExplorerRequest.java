package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.model.vo.processExplorer.EventNode;
import com.sp.proxverse.common.model.vo.processExplorer.LineNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-04-18 09:54
 */
@Data
@ApiModel("获取流程图资源请求")
public class ProcessExplorerRequest extends ProcessTreeBase {

  @ApiModelProperty("当前线的索引从1开始，当为0，或者为空的时候取默认")
  private Integer currentLineIndex;

  private Integer currentEventIndex;

  private List<LineNode> loadLineNodes;

  private List<EventNode> loadEventNodes;
}
