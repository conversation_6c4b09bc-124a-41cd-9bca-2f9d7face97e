package com.sp.proxverse.common.model.enums.job;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022-12-12 15:05
 */
public enum JobStatusEnum {
  /** 未知 */
  UNKNOWN("unknown", -1),

  /** 暂停 */
  SUSPEND("suspend", 0),

  /** 开启 */
  OPEN("open", 1),

  /** 完成 */
  FINISH("finish", 2),

  /** 执行 */
  EXECUTING("executing", 3),

  /** 加载 */
  LOAD("LOAD", 4),

  ERROR("error", 5),

  HAND("hand", 6),

  /** 子任务取代 */
  SUBTASK_REPLACEMENT("subtaskReplacement", 7),

  NOT_EXECUTING("notExecuting", 8);

  private String name;

  private Integer value;

  JobStatusEnum(String name, Integer value) {
    this.name = name;
    this.value = value;
  }

  public static JobStatusEnum getJobStatusEnumByName(String name) {
    for (JobStatusEnum jobStatusEnum : JobStatusEnum.values()) {
      if (Objects.equals(name, jobStatusEnum.name())) {
        return jobStatusEnum;
      }
    }
    return UNKNOWN;
  }

  public String getName() {
    return name;
  }

  public Integer getValue() {
    return value;
  }
}
