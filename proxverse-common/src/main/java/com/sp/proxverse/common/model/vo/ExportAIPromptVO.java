package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("AI提示词导出VO对象")
public class ExportAIPromptVO {
  @Tolerate
  public ExportAIPromptVO() {}

  public ExportAIPromptVO(
      String topicId,
      String topicName,
      String sheetId,
      String sheetName,
      String componentName,
      String type,
      String prompt) {
    this.topicId = topicId;
    this.topicName = topicName;
    this.sheetId = sheetId;
    this.sheetName = sheetName;
    this.componentName = componentName;
    this.type = type;
    this.prompt = prompt;
  }

  @ApiModelProperty(value = "主题编号")
  private String topicId;

  @ApiModelProperty(value = "主题名称")
  private String topicName;

  @ApiModelProperty(value = "页面编号")
  private String sheetId;

  @ApiModelProperty(value = "页面名称")
  private String sheetName;

  @ApiModelProperty(value = "组件名称")
  private String componentName;

  @ApiModelProperty(value = "类型")
  private String type;

  @ApiModelProperty(value = "提示词")
  private String prompt;
}
