package com.sp.proxverse.common.model.vo.processai.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询事件列表时案例数量与占比（事件过滤）请求对象")
public class QueryEventRateRequest {
  @Tolerate
  public QueryEventRateRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty(value = "事件列表", required = true)
  @NotEmpty(message = "{400009}")
  private List<QueryEventRateSubRequest> eventList;

  private Integer prikey;

  private List<SaveTopicFilterRequest> filterPqlLsit;
}
