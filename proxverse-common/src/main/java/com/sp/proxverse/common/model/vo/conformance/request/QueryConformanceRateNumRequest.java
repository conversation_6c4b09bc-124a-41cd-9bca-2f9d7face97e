package com.sp.proxverse.common.model.vo.conformance.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询流程比例、流程案例数请求对象")
public class QueryConformanceRateNumRequest {

  @ApiModelProperty(value = "表格sheetID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private Integer topicId;

  @ApiModelProperty(value = "变体", required = true)
  private String variant;

  private List<String> variantList;
}
