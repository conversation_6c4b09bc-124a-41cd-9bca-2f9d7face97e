package com.sp.proxverse.common.model.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "分页入参")
public class PageRequest {
  @ApiModelProperty(value = "当前页码，不传默认为1", example = "1")
  public Integer pageNum = 1;

  @ApiModelProperty(value = "每页条数，不传默认为10", example = "10")
  public Integer pageSize = 10;

  @ApiModelProperty(hidden = true)
  public int getStartItem() {
    int start = (this.pageNum - 1) * this.pageSize;
    if (start < 0) {
      start = 0;
    }

    return start;
  }

  public static int getStartItem(int currentPage, int itemsPerPage) {
    int start = (currentPage - 1) * itemsPerPage;
    if (start < 0) {
      start = 0;
    }

    return start;
  }
}
