package com.sp.proxverse.common.model.vo.processExplorer;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.Data;
import scala.Tuple2;

@Data
public class DefaultChart {

  private List<Tuple2<String, String>> sourceTarget = new ArrayList<>();

  public void addSourceTarget(String source, String target) {
    sourceTarget.add(new Tuple2<>(source, target));
  }

  public Set<String> getSourceTargetNameList() {
    Set<String> eventNames = new HashSet<>();
    sourceTarget.forEach(
        t -> {
          eventNames.add(t._1() + "," + t._2());
        });
    return eventNames;
  }

  public Set<String> getEventList() {
    Set<String> eventList = new HashSet<>();
    sourceTarget.forEach(
        t -> {
          eventList.add(t._1);
          eventList.add(t._2);
        });
    return eventList;
  }

  public Boolean isEmpty() {
    return sourceTarget.isEmpty();
  }
}
