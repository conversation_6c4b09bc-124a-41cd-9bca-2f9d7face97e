package com.sp.proxverse.common.model.dict;

/** 连接的数据库类型 */
public enum DataConnectorBizTypeEnum {
  STANDARD(1, "标准链接"),
  CUSTOM(2, "自定义"),
  ;

  private final Integer value;
  private final String name;

  DataConnectorBizTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
