package com.sp.proxverse.common.model.dto.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class QueryTenantIdDTO {

  @Tolerate
  public QueryTenantIdDTO() {
    // comment empty
  }

  @ApiModelProperty("表名")
  private String table;

  @ApiModelProperty("字段")
  private String field;

  @ApiModelProperty("字段值")
  private String value;

  private Integer tenantId;
}
