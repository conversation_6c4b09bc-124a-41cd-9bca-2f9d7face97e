package com.sp.proxverse.common.model.dto;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QueryColumnDataByColumnIdDTO extends PageRequest {

  private Integer columnId;

  @ApiModelProperty(value = "运算规则")
  private List<QueryColumnDataRuleDTO> ruleList;
}
