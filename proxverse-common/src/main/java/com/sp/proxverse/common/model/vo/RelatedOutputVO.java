package com.sp.proxverse.common.model.vo;

import com.sp.proxverse.common.model.dto.RelatedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("计算相关性VO对象")
public class RelatedOutputVO {
  @Tolerate
  public RelatedOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "变量名称")
  private String variant;

  @ApiModelProperty(value = "相关系数")
  private String relatedValue;

  @ApiModelProperty(value = "相关系数后台排序使用，前端忽略", hidden = true)
  private BigDecimal relatedValueDecimal;

  @ApiModelProperty(value = "当前所传业务参数的总包含比例")
  private BigDecimal containRatioDecimal;

  @ApiModelProperty(value = "当前所传业务参数的不包含比例")
  private BigDecimal notContainRatioDecimal;

  @ApiModelProperty(value = "当前业务主题数据下的子数据集合")
  private List<RelatedDTO> subRelatedDTOList;
}
