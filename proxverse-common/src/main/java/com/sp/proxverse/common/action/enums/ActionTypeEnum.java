package com.sp.proxverse.common.action.enums;

/**
 * Action类型
 *
 * <AUTHOR>
 * @create 2022-05-09 2:11 下午
 */
public enum ActionTypeEnum {

  /** 未知 */
  UNKNOWN(0, "UNKNOWN", "未知请求", 0),

  /** Http请求 */
  HTTP(2100, "httpRequest", "http请求", 2000),

  /** 路由 */
  ROUTE(1100, "route", "路由", 1000),

  /** 发送邮件 */
  SEND_EMAIL(3100, "sendEmail", "发送邮件(smtp)", 3000),

  /** 发送企业微信消息 */
  SEND_WECHAT_MSG(4100, "sendWechatMsg", "发送企业微信消息", 4000),
  ;

  /** 编码 */
  public Integer code;

  /** 根节点编码 */
  public Integer rootCode;

  /** 名称 */
  public String displayName;

  /** 描述 */
  public String describe;

  /**
   * 构造方法
   *
   * @param code
   * @param displayName
   */
  ActionTypeEnum(Integer code, String displayName, String describe, Integer rootCode) {
    this.code = code;
    this.displayName = displayName;
    this.rootCode = rootCode;
    this.describe = describe;
  }

  /**
   * 根据code获取动作类型
   *
   * @param code
   * @return
   */
  public static ActionTypeEnum getActionTypeByCode(Integer code) {
    if (code == null) {
      return null;
    }
    for (ActionTypeEnum value : ActionTypeEnum.values()) {
      if (value.code.equals(code)) {
        return value;
      }
    }
    return null;
  }

  public Integer getCode() {
    return code;
  }

  public Integer getRootCode() {
    return rootCode;
  }

  public String getDisplayName() {
    return displayName;
  }

  public String getDescribe() {
    return describe;
  }
}
