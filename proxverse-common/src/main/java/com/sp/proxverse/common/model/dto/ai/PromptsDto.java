package com.sp.proxverse.common.model.dto.ai;

import com.sp.proxverse.common.exception.BizException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class PromptsDto {

  private Integer id;

  private String promptsType;

  private String promptsValue;

  private String modelConfig;

  /** tenantId表示该提示词有那些公司可以使用 当ID为0的时候,表示该提示词可以用于所有公司 */
  private Integer tenantId;

  private Integer deleted;

  private Boolean isTemplate;

  public void check() {
    if (StringUtils.isEmpty(promptsType)) {
      throw new BizException(5000, "promptsType can not be null");
    }

    if (StringUtils.isEmpty(promptsValue)) {
      throw new BizException(5000, "promptsValue can not be null");
    }
    if (isTemplate == null) {
      throw new BizException(5000, "isTemplate can not be null");
    }
  }
}
