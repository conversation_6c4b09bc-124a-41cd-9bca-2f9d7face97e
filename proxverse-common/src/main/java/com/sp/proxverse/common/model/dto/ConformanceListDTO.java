package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("一致性流程列表DTO")
public class ConformanceListDTO {

  @Tolerate
  public ConformanceListDTO() {
    // comment empty
  }

  @ApiModelProperty("该条数据的主键ID")
  private Integer dataId;

  @ApiModelProperty("topicId（跳详情请求树图要用到）")
  private Integer topicId;

  @ApiModelProperty("案例数")
  private Integer caseNum;

  @ApiModelProperty("案例数，后台已经乘以100")
  private BigDecimal caseRate;

  @ApiModelProperty("事件列表，目前只给出三个，前端拼接即可")
  private List<String> event;

  @ApiModelProperty("当前数据的变体")
  private String variant;

  @ApiModelProperty("当前数据的变体md5")
  private String variantMd5;

  @ApiModelProperty("kpi列表信息")
  private List<ConformanceKipDTO> kipDTOList;
}
