package com.sp.proxverse.common.model.vo.processai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("检查数据模型是否可用VO对象")
public class CheckDataModelOutputVO {
  @Tolerate
  public CheckDataModelOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据模型是否可用(true：可用，false：不可用)")
  private Boolean check;

  @ApiModelProperty(value = "数据模型ID，如果不可用的话，依据此ID跳转到对应的数据模型详情去加载数据")
  private Integer dataModelId;

  @ApiModelProperty(value = "数据模型名称")
  private String dataModelName;

  @ApiModelProperty(value = "数据池ID")
  private Integer dataPoolId;

  @ApiModelProperty(value = "数据池ID")
  private Integer activeFileId;

  @ApiModelProperty(value = "数据池名称")
  private String dataPoolName;

  @ApiModelProperty(value = "模型更新时间")
  private String updateTime;

  @ApiModelProperty(value = "数据模型是否为单时间列")
  private Boolean isSingleTimeColumn;

  private String error;
}
