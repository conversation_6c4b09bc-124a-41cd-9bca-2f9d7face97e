package com.sp.proxverse.common.model.dict;

public enum KpiFromKnowledgeEnum {
  NOT_FROM(0, "不是从知识模型导入过来的"),
  FROM(1, "是从知识模型导入过来的"),
  ;

  private final Integer value;
  private final String name;

  KpiFromKnowledgeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
