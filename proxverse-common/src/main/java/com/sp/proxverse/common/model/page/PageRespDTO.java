package com.sp.proxverse.common.model.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel(value = "分页出参")
public class PageRespDTO<T extends Serializable> implements Serializable {

  @Tolerate
  public PageRespDTO() {
    // comment empty
  }

  private static final long serialVersionUID = 7451247139863093367L;

  @ApiModelProperty(value = "listObj")
  private T dto;

  @ApiModelProperty(value = "总条数", example = "100")
  private Long total;

  @ApiModelProperty(value = "当前页码，不传默认为1", example = "1")
  private Integer pageNum = 1;

  @ApiModelProperty(value = "每页条数，不传默认为10", example = "1000")
  private Integer pageSize = 10;
}
