package com.sp.proxverse.common.model.vo.simulation;

import com.sp.proxverse.common.model.enums.Interval;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-21 14:07
 */
@Data
@ApiModel("流程资源响应")
public class SimulationResourceResVo {

  @ApiModelProperty("资源Id")
  private Integer resourceId;

  @ApiModelProperty("资源Id")
  private Integer resourcePoolId;

  @ApiModelProperty("资源池名称")
  private String resourceName;

  @ApiModelProperty("周")
  public List<Interval.IntervalEnum> intervalEnums;

  @ApiModelProperty("仿真ID")
  private Integer simulationId;

  @ApiModelProperty("开始时间")
  private String startTime;

  @ApiModelProperty("结束时间")
  private String endTime;

  @ApiModelProperty("数量")
  private Integer number;

  @ApiModelProperty("资源成本")
  private Long resourceCost;

  private String numberExpression;

  private String numberFormatting;

  private String numberFormat;

  private String resourceCostExpression;

  private String resourceCostFormatting;

  private String resourceCostFormat;
}
