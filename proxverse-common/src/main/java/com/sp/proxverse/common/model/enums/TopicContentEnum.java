package com.sp.proxverse.common.model.enums;

import lombok.Getter;

@Getter
public enum TopicContentEnum {
  CONFIG("exportTopicConfigService", "通用配置"),
  KPI("exportTopicKpiService", "kpi"),
  SCRIPT_FILTER("exportTopicScriptFilterService", "过滤规则"),
  PARAM("exportTopicParamService", "变量"),
  PROCESS_KPI("exportTopicProcessKpiService", "流程图kpi"),
  BOOK_MARK("exportTopicBookMarkService", "bookmark"),
  FILTER("exportTopicFilterService", "topic过滤"),
  TEMP_TABLE("exportTopicTempTableService", "临时表");

  private String service;
  private String msg;

  TopicContentEnum(String service, String msg) {
    this.service = service;
    this.msg = msg;
  }
}
