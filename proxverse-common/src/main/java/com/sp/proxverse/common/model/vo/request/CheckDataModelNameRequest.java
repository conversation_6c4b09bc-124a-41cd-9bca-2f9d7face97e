package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("检查数据Model创建对象")
public class CheckDataModelNameRequest {

  @ApiModelProperty(value = "数据池ID", required = true)
  @NotNull(message = "{400006}")
  private Integer poolId;

  @ApiModelProperty(value = "名称，创建时必填", required = true)
  @NotBlank(message = "{400007}")
  private String name;
}
