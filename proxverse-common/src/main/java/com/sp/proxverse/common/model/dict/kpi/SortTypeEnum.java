package com.sp.proxverse.common.model.dict.kpi;

import java.util.Objects;

public enum SortTypeEnum {
  ASC(1, "ASC"),
  DESC(2, "DESC"),
  ;

  private final Integer value;
  private final String name;

  SortTypeEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public static String getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return "";
    }
    SortTypeEnum[] values = SortTypeEnum.values();
    for (SortTypeEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value.getName();
      }
    }
    return "";
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }
}
