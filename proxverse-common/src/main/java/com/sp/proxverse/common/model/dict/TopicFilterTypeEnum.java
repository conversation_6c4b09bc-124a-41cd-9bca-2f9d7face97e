package com.sp.proxverse.common.model.dict;

import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.util.I18nUtil;
import java.util.Objects;

public enum TopicFilterTypeEnum {
  WITH(100, "200240"),
  WITH_CUSTOM_EVENT(110, "流程通过-自定义event列"),
  WITH_ANY(101, "200241"),
  WITH_ANY_CUSTOM_EVENT(111, "任意通过-自定义event列"),

  WITHOUT(200, "200245"),
  WITHOUT_CUSTOM_EVENT(210, "流程不通过-自定义event列"),
  WITHOUT_ANY(201, "200246"),

  WITHOUT_ANY_CUSTOM_EVENT(211, "任意不通过-自定义event列"),

  START(300, "200052"),
  START_CUSTOM_EVENT(310, "流程开始于-自定义event列"),
  END(400, "200053"),
  END_CUSTOM_EVENT(410, "流程结束于-自定义event列"),
  VARIANT(500, "200250"),
  VARIANT_FILTER(502, "200251"),
  VARIANT_NOTEQ(501, "变体过滤不等于"),
  VARIANT_CONFORMANCE(510, "一致过滤"),
  VARIANT_UNCONFORMANCE(511, "不一致过滤"),

  VARIANT_ID_CONFORMANCE(520, "一致过滤"),
  VARIANT_ID_UNCONFORMANCE(520, "不一致过滤"),

  VARIABLE_KPI_PARAM(600, "参数项kpi属性值过滤"),
  VARIABLE(700, "200057"),

  PROCESS_FOLLOW_FILTER(800, "200300"),
  PROCESS_FOLLOW_DIRECT(810, "200301"),
  PROCESS_FOLLOW(820, "200302"),
  PROCESS_FOLLOW_INDIRECT(830, "200303"),
  PROCESS_FOLLOW_NON(840, "200304"),

  CALC_TIME(900, "200310"),
  CALC_TIME_FF(910, "第一次发生-第一次发生"),
  CALC_TIME_FL(920, "第一次发生-最后一次发生"),
  CALC_TIME_LF(930, "最后一次发生-第一次发生"),
  CALC_TIME_LL(940, "最后一次发生-最后一次发生"),

  REWORK_NUM(1000, "200066"),

  EXPRESSION(2000, "200312"),

  EXPRESSION_SHEET(2001, "SHEET的过滤规则"),

  COMPONENT_FILTER(2100, "200311"),

  /** 有两个参数 1 开始事件 ： type : 0 第一次发生，1 最后一次发生 2 结束事件 ： type : 0 第一次发生，1 最后一次发生 */
  CROP_SELECTION(3000, "200313"),
  ;

  public final Integer value;
  public final String displayName;

  public static boolean isEvent(Integer value) {
    if (Objects.isNull(value)) {
      return false;
    }
    return Objects.equals(value, WITH.getValue())
        || Objects.equals(value, WITH_ANY.getValue())
        || Objects.equals(value, WITHOUT.getValue())
        || Objects.equals(value, WITHOUT_ANY.getValue())
        || Objects.equals(value, START.getValue())
        || Objects.equals(value, END.getValue());
  }

  public static boolean isExpression(Integer value) {
    if (Objects.isNull(value)) {
      return false;
    }
    return Objects.equals(value, EXPRESSION.getValue());
  }

  public static boolean isVariable(Integer value) {
    if (Objects.isNull(value)) {
      return false;
    }
    return Objects.equals(value, VARIABLE.getValue());
  }

  public static boolean isComponent(Integer value) {
    return Objects.equals(value, COMPONENT_FILTER.getValue());
  }

  public static boolean isVariant(Integer value) {
    if (Objects.isNull(value)) {
      return false;
    }
    return Objects.equals(value, VARIANT.getValue())
        || Objects.equals(value, VARIANT_NOTEQ.getValue());
  }

  public static boolean isProcess(Integer value) {
    if (Objects.isNull(value)) {
      return false;
    }
    return value >= 800 && value < 900;
  }

  public static boolean isProcessFollow(Integer value) {
    return Objects.equals(value, PROCESS_FOLLOW.getValue());
  }

  public static boolean isProcessFollowNon(Integer value) {
    return Objects.equals(value, PROCESS_FOLLOW_NON.getValue());
  }

  public static boolean isProcessFollowDirect(Integer value) {
    return Objects.equals(value, PROCESS_FOLLOW_DIRECT.getValue());
  }

  public static boolean isProcessFollowInDirect(Integer value) {
    return Objects.equals(value, PROCESS_FOLLOW_INDIRECT.getValue());
  }

  public static boolean isCalcTime(Integer value) {
    if (Objects.isNull(value)) {
      return false;
    }
    return value >= 900 && value < 1000;
  }

  public static boolean isRework(Integer value) {
    if (Objects.isNull(value)) {
      return false;
    }
    return Objects.equals(value, REWORK_NUM.getValue());
  }

  public static Integer followBizType2EnumType(Integer bizType) {
    if (Objects.equals(bizType, 10)) {
      return PROCESS_FOLLOW_DIRECT.getValue();
    }
    if (Objects.equals(bizType, 20)) {
      return PROCESS_FOLLOW.getValue();
    }
    if (Objects.equals(bizType, 30)) {
      return PROCESS_FOLLOW_INDIRECT.getValue();
    }
    if (Objects.equals(bizType, 40)) {
      return PROCESS_FOLLOW_NON.getValue();
    }
    throw new BizException(5000, I18nUtil.getMessage(I18nConst.TYPE_NOT_MATCH));
  }

  public static String getNameByValue(Integer value) {
    if (Objects.equals(value, VARIABLE_KPI_PARAM.getValue())) {
      return I18nUtil.getMessage(VARIABLE_KPI_PARAM.getDisplayName());
    }
    TopicFilterTypeEnum[] values = values();
    for (TopicFilterTypeEnum typeEnum : values) {
      if (Objects.equals(typeEnum.getValue(), value)) {
        return I18nUtil.getMessage(typeEnum.getDisplayName());
      }
    }
    return "";
  }

  public static String getNameByValue(TopicFilterPO po) {
    if (Objects.equals(po.getType(), VARIABLE_KPI_PARAM.getValue())) {
      return po.getParam();
    }
    TopicFilterTypeEnum[] values = values();
    for (TopicFilterTypeEnum typeEnum : values) {
      if (Objects.equals(typeEnum.getValue(), po.getType())) {
        return I18nUtil.getMessage(typeEnum.getDisplayName());
      }
    }
    return "";
  }

  public static String getDescByPO(TopicFilterPO topicFilterPO, String param) {
    if (Objects.equals(topicFilterPO.getType(), WITH.getValue())
        || Objects.equals(topicFilterPO.getType(), WITH_ANY.getValue())
        || Objects.equals(topicFilterPO.getType(), WITHOUT.getValue())
        || Objects.equals(topicFilterPO.getType(), START.getValue())
        || Objects.equals(topicFilterPO.getType(), END.getValue())) {
      return topicFilterPO.getValue() + I18nUtil.getMessage(I18nConst.EVENT);
    }
    if (Objects.equals(topicFilterPO.getType(), VARIANT.getValue())) {
      // todo 数据库要再加一个字段#1#2这种
    }
    if (Objects.equals(topicFilterPO.getType(), VARIABLE_KPI_PARAM.getValue())) {
      return param + "=" + topicFilterPO.getValue();
    }
    if (Objects.equals(topicFilterPO.getType(), VARIABLE.getValue())) {
      return param + "=" + topicFilterPO.getValue();
    }
    return null;
  }

  TopicFilterTypeEnum(Integer value, String displayName) {
    this.value = value;
    this.displayName = displayName;
  }

  public static boolean isCrop(Integer type) {
    if (Objects.isNull(type)) {
      return false;
    }
    return type == 3000;
  }

  public static TopicFilterTypeEnum fromValue(Integer type) {

    TopicFilterTypeEnum[] values = values();
    for (TopicFilterTypeEnum typeEnum : values) {
      if (Objects.equals(typeEnum.getValue(), type)) {
        return typeEnum;
      }
    }
    return null;
  }

  public Integer getValue() {
    return value;
  }

  public String getDisplayName() {
    return displayName;
  }
}
