package com.sp.proxverse.common.model.job.create;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-10 14:03
 */
@Data
public class ETLTaskGeneratorInfo extends TaskGeneratorInfo {

  private Integer poolId;

  private Integer transformationId;

  public ETLTaskGeneratorInfo(
      Integer poolId,
      Integer transformationId,
      Integer dataTaskId,
      Integer tenantId,
      Integer taskExecutorId,
      Boolean immediatelyFlag) {
    this.poolId = poolId;
    this.transformationId = transformationId;
    this.dataTaskId = dataTaskId;
    this.tenantId = tenantId;
    this.taskExecutorId = taskExecutorId;
    this.immediatelyFlag = immediatelyFlag;
  }
}
