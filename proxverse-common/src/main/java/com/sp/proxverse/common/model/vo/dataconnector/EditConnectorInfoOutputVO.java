package com.sp.proxverse.common.model.vo.dataconnector;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取编辑链接器的回显信息VO对象")
public class EditConnectorInfoOutputVO {
  @Tolerate
  public EditConnectorInfoOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "链接ID")
  private Integer connectorId;

  @ApiModelProperty(value = "链接名称")
  private String name;

  @ApiModelProperty(value = "IP")
  private String ip;

  @ApiModelProperty(value = "端口")
  private Integer port;

  @ApiModelProperty(value = "库名称")
  private String dbname;

  @ApiModelProperty(value = "数据库用户名")
  private String username;

  @ApiModelProperty(value = "数据库密码")
  private String password;

  @ApiModelProperty(value = "schema名称", required = true)
  private String schemaName;

  @ApiModelProperty(value = "实例号（HANA DB）", required = true)
  private String instanceNumber;

  @ApiModelProperty(value = "1：标准链接，2：自定义链接", required = true)
  private Integer bizType;

  @ApiModelProperty(
      value =
          "jdbc类型（1：mysql,2:oracle,3:db2,4:hive,5:postgreSql,6:impala,7:sqlserver），当bizType=1时此字段不能为空")
  private Integer jdbcType;

  @ApiModelProperty(value = "自定义链接，当bizType=2时此字段不能为空")
  private String connectLink;
}
