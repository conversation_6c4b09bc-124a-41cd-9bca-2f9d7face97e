package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.util.ExpressionBuilderUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.LinkedHashMap;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("查询业务图谱某个事件之间连线悬浮显示数据请求对象")
public class QueryTreeLineWithRequest {

  @ApiModelProperty(value = "topicId", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  private Integer sheetId;

  @ApiModelProperty(value = "事件名称", required = true)
  @NotNull(message = "{400007}")
  private String event1;

  @ApiModelProperty(value = "事件名称", required = true)
  @NotNull(message = "{400007}")
  private String event2;

  private String columnName;

  private String expression;

  @ApiModelProperty(value = "分组信息")
  private LinkedHashMap<String, Set<String>> groupInfo;

  public String getEvent1() {
    return event1;
  }

  public String getEvent2() {
    return event2;
  }

  public boolean getGroupInfoFlag() {
    return ExpressionBuilderUtil.checkGroup(groupInfo);
  }
}
