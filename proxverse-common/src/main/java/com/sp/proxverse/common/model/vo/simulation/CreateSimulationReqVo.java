package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-19 18:13
 */
@Data
@ApiModel("创建流程仿真")
public class CreateSimulationReqVo {

  @ApiModelProperty(value = "父Id")
  @NotNull(message = "{400006}")
  public Integer parentTopicId;

  @ApiModelProperty(value = "名称")
  @NotNull(message = "{400007}")
  public String topicName;

  @ApiModelProperty(value = "Id 当不为空是更新")
  public Integer id;

  private Integer dataModelId;
}
