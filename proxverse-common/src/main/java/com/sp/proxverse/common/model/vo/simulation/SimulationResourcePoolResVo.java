package com.sp.proxverse.common.model.vo.simulation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-21 14:07
 */
@Data
@ApiModel("流程资源池响应")
public class SimulationResourcePoolResVo {

  @ApiModelProperty("资源Id")
  private Integer resourcePoolId;

  @ApiModelProperty("资源名称")
  private String resourcePoolName;

  @ApiModelProperty("事件个数")
  private Integer eventNumber;

  @ApiModelProperty("事件")
  private List<SimulationResourcePoolEvent> events;

  @Data
  public static class SimulationResourcePoolEvent {

    private Integer eventId;

    private String elementId;

    private String name;
  }
}
