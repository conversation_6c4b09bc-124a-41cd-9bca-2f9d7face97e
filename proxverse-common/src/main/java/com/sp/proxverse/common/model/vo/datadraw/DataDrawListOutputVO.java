package com.sp.proxverse.common.model.vo.datadraw;

import com.sp.proxverse.common.model.job.IncFileArgs;
import com.sp.proxverse.common.model.vo.dataconnector.request.DataExtractionPreviewReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据提取List列表VO对象")
public class DataDrawListOutputVO {
  @Tolerate
  public DataDrawListOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据链接ID")
  private Integer dataExtractorId;

  @ApiModelProperty(value = "数据链接ID")
  private Integer dataConnectorId;

  @ApiModelProperty(value = "名称")
  private String name;

  @ApiModelProperty(value = "数据链接名称")
  private String dataConnectorname;

  @ApiModelProperty(value = "单位MB")
  private String parquetSize;

  @ApiModelProperty(value = "数据任务名称")
  private String dataTaskName;

  @ApiModelProperty(value = "类型（1：jdbc链接，2：kafka链接）")
  private Integer type;

  private Integer jdbcType;

  @ApiModelProperty(value = "使用中的数据模型")
  private String usingDataModel;

  @ApiModelProperty(value = "状态（1：可用，0：不可用）")
  private Integer status;

  @ApiModelProperty(value = "kafka 配置信息")
  private DataExtractionPreviewReq kafkaConfigInfo;

  @ApiModelProperty(value = "file 配置信息")
  private IncFileArgs incFileArgs;
}
