package com.sp.proxverse.common.model.vo.simulation;

import com.sp.proxverse.common.model.bo.SimulationRunResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-27 16:55
 */
@Data
@ApiModel("运行流程仿真结果响应")
public class RunSimulationResultResVo {

  @ApiModelProperty("结果")
  List<SimulationRunResult> results;
}
