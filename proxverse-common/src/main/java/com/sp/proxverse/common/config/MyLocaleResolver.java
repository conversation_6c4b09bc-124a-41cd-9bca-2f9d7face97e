package com.sp.proxverse.common.config;

import com.sp.proxverse.common.util.CookieUtil;
import java.util.Locale;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;

@Configuration
public class MyLocaleResolver implements LocaleResolver {

  @Autowired private HttpServletRequest request;

  @Autowired private CookieUtil cookieUtil;

  public Locale getLocale() {
    return resolveLocale(request);
  }

  @Override
  public Locale resolveLocale(HttpServletRequest httpServletRequest) {
    String lang = cookieUtil.getCookieValue("lang", httpServletRequest);

    try {
      if (StringUtils.isBlank(lang)) {
        // 这里获取浏览器的默认语言
        return Locale.CHINESE;
      }
    } catch (Exception e) {
      return Locale.CHINESE;
    }
    if (Objects.equals(lang, "en")) {
      return Locale.ENGLISH;
    }
    if (Objects.equals(lang, "zh")) {
      return Locale.CHINESE;
    }
    return Locale.CHINESE;
  }

  @Override
  public void setLocale(
      HttpServletRequest httpServletRequest,
      HttpServletResponse httpServletResponse,
      Locale locale) {
    // comment empty
  }
}
