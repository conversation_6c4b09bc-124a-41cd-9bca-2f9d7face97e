package com.sp.proxverse.common.model.vo.topicDataModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-04-18 11:44 上午
 */
@ApiModel
@Getter
@Setter
public class TopicDataModelResVo {

  @ApiModelProperty(value = "数据信息")
  private List<TopicDataModelOutputVO> contents;

  @ApiModelProperty(value = "总行数")
  private Integer total;

  @ApiModelProperty(value = "第几页")
  private Integer pageNum;

  @ApiModelProperty(value = "页大小")
  private Integer pageSize;

  public static TopicDataModelResVo emptyRet(Integer pageNum, Integer pageSize) {
    TopicDataModelResVo vo = new TopicDataModelResVo();
    vo.setTotal(0);
    vo.setContents(new ArrayList<>());
    vo.setPageNum(pageNum);
    vo.setPageSize(pageSize);
    return vo;
  }
}
