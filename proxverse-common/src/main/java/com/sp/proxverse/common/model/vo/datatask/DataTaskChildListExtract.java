package com.sp.proxverse.common.model.vo.datatask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-11 11:39
 */
@Data
@ApiModel("数据任务孩子列表数据提取")
public class DataTaskChildListExtract {

  private Integer dataTaskChildId;

  private Integer dataExtractId;

  private String name;

  private Integer dataTaskId;

  private String connectName;

  @ApiModelProperty(" 0运行，1，暂停")
  private Integer state;

  private String type;
}
