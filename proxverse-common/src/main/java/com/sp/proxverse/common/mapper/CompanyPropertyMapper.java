package com.sp.proxverse.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sp.proxverse.common.model.po.CompanyPropertyPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 公司配置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Mapper
@DS("oauth2")
public interface CompanyPropertyMapper extends BaseMapper<CompanyPropertyPo> {

  @InterceptorIgnore(tenantLine = "on")
  List<CompanyPropertyPo> getCompanyProperty(
      @Param("companyId") Integer companyId, @Param("propertyKey") String propertyKey);

  @InterceptorIgnore(tenantLine = "on")
  boolean removeCompanyProperty(
      @Param("companyId") Integer companyId, @Param("propertyKey") String propertyKey);
}
