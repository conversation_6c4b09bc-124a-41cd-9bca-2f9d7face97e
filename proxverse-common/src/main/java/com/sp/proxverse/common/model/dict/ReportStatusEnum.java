package com.sp.proxverse.common.model.dict;

public enum ReportStatusEnum {
  PENDING(0, "pending"),
  PROCESSING(1, "processing"),
  COMPLETED(2, "completed"),
  FAILED(3, "failed"),
  ;

  private final Integer value;
  private final String name;

  ReportStatusEnum(Integer value, String name) {
    this.value = value;
    this.name = name;
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  @Override
  public String toString() {
    return name;
  }
}
