package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@ApiModel("偏差事件中kpi数据DTO")
public class DeviationKipDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty("kpiId")
  private Integer kpiId;

  @ApiModelProperty("kpi名称")
  private String kpiName;

  @ApiModelProperty(value = "所属事件名称", hidden = true)
  private String eventName;

  @ApiModelProperty("此偏差事件在此kpi中计算的值")
  private String value;

  @ApiModelProperty("此偏差事件在此kpi中计算的值与全量kpi相比是上升还是下降（0：下降，1：上升），为null则不展示箭头了")
  private Integer upDown;

  @ApiModelProperty("与全量kpi相比的差值")
  private BigDecimal upDownValue;

  private String formatting;

  private String format;

  private String columnType;

  private String unit;
}
