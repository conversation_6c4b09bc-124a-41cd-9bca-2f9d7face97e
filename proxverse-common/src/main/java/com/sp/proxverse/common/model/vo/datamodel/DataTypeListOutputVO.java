package com.sp.proxverse.common.model.vo.datamodel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取数据类型列表VO对象")
public class DataTypeListOutputVO {
  @Tolerate
  public DataTypeListOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "数据类型枚举值")
  private Integer dataType;

  @ApiModelProperty(value = "数据类型描述")
  private String dataTypeName;
}
