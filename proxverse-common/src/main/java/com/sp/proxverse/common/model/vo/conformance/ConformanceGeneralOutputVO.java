package com.sp.proxverse.common.model.vo.conformance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询一致性流程的概览数据VO对象")
public class ConformanceGeneralOutputVO {
  @Tolerate
  public ConformanceGeneralOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "一致比例（包含允许流程），后台已经乘以100")
  private String conformanceRate;

  @ApiModelProperty(value = "一致案例（包含允许流程）")
  private String conformanceCaseNum;

  @ApiModelProperty(value = "不一致案例")
  private String unConformanceCaseNum;

  @ApiModelProperty(value = "一致变体")
  private String conformanceVariantNum;

  @ApiModelProperty(value = "不一致变体")
  private String unConformanceVariantNum;
}
