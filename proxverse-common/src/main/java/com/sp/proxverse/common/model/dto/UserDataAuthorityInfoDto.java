package com.sp.proxverse.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-04-06 6:15 下午
 */
@Data
public class UserDataAuthorityInfoDto {
  @ApiModelProperty(value = "id")
  private Integer id;

  @ApiModelProperty(value = "用户Id")
  private Integer userId;

  @ApiModelProperty(value = "用户名称")
  private String userName;

  @ApiModelProperty(value = "USER,ANALYST,ROOT")
  private String userLevel;

  @ApiModelProperty(value = "权限值：2：读权限，4：修改权限，8：分配权限；具体的值是权限之和", required = true)
  private Integer authorityValue;

  @ApiModelProperty(value = "数据ID")
  private String dataId;

  @ApiModelProperty(value = "数据类型，1：topic")
  private Integer dataType;
}
