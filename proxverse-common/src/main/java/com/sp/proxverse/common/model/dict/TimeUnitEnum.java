package com.sp.proxverse.common.model.dict;

import java.util.Objects;
import java.util.stream.Stream;

public enum TimeUnitEnum {
  SECOND(1, "秒", "SECONDS"),
  MIN(2, "分", "MINUTES"),
  HOUR(3, "时", "HOURS"),
  DAY(4, "天", "DAYS"),
  MONTHS(5, "月", "MONTHS"),
  ;

  private final Integer value;

  private final String name;

  private final String unitType;

  TimeUnitEnum(Integer value, String name, String unitType) {
    this.value = value;
    this.name = name;
    this.unitType = unitType;
  }

  public static TimeUnitEnum getDescByCode(Integer code) {
    if (Objects.isNull(code)) {
      return null;
    }
    TimeUnitEnum[] values = TimeUnitEnum.values();
    for (TimeUnitEnum value : values) {
      if (Objects.equals(value.getValue(), code)) {
        return value;
      }
    }
    return null;
  }

  /**
   * 根据文件后缀名得到枚举int值
   *
   * @param name
   * @return
   */
  public static Integer filterFileType(String name) {
    TimeUnitEnum fileTypeEnum =
        Stream.of(values()).filter(e -> Objects.equals(e.getName(), name)).findFirst().orElse(null);
    return Objects.isNull(fileTypeEnum) ? null : fileTypeEnum.getValue();
  }

  public Integer getValue() {
    return value;
  }

  public String getName() {
    return name;
  }

  public String getUnitType() {
    return unitType;
  }
}
