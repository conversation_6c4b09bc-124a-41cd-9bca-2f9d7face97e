package com.sp.proxverse.utils

import com.sp.proxverse.common.exception.BizException
import com.sp.proxverse.common.model.i18n.I18nConst
import com.sp.proxverse.common.util.I18nUtil

object CheckInvalid {

  private val validNameFormat = "([\\w_]+)".r

  def validateName(name: String): Unit = {
    if (!validNameFormat.pattern.matcher(name).matches()) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.CHINESE_CHARACTERS_ERROR))
    }
  }

  def isValiadte(name: String): Boolean = {
    return validNameFormat.pattern.matcher(name).matches()
  }

}
