package com.sp.proxverse.common

import com.sp.proxverse.common.model.vo.processExplorer.{EventNode, LineNode}
import scala.collection.mutable
import scala.collection.mutable.ListBuffer

/**
 * <AUTHOR>
 * @create 2023-05-04 18:01
 */
// 定义节点访问状态
class Graph() {
  var lineGroupBySource: mutable.Map[String, List[LineNode]] =
    mutable.Map.empty[String, List[LineNode]]
  var lineGroupByTarget: mutable.Map[String, List[LineNode]] =
    mutable.Map.empty[String, List[LineNode]]

  val unloadedEventMap: mutable.LinkedHashMap[String, EventNode] = mutable.LinkedHashMap()
  val loadedEventMap: mutable.LinkedHashMap[String, EventNode] = mutable.LinkedHashMap()
  val eventMap: mutable.LinkedHashMap[String, EventNode] = mutable.LinkedHashMap()
  private var gradientsValue: Integer = _

  def getLoadGradientValue(): Int = {
    this.gradientsValue
  }

  /**
   * 该接口会修改node loadStatus
   */
  def loadGradient(): Unit = {
    gradientsValue += 0
    // 返回的梯度, [事件集合, 线集合 ]
    val gradientResult = mutable.ListBuffer[(Set[EventNode], Set[(String, String)])]()
    var flag = true

    while (flag) {
      gradientsValue = gradientsValue + 1
      if (unloadedEventMap.isEmpty) {
        return gradientResult.toList
      }

      val firstNode = unloadedEventMap.remove(unloadedEventMap.head._1)
      firstNode.get.setGradient(gradientsValue)

      loadedEventMap.put(firstNode.get.getName, firstNode.get)
      if (firstNode.get.getNumber <= 0) {
        return gradientResult.toList
      }

      // 计算事件梯度, 分为向下搜索和向上搜索
      findPathBFS(firstNode.get, false)
      findPathBFS(firstNode.get, true)

      if (unloadedEventMap.size == 0) {
        flag = false
      }
    }

  }

  private def findPathBFS(node: EventNode, isFindPathSourceBFS: Boolean): Unit = {
    val queue = mutable.Queue[mutable.ListBuffer[EventNode]]()
    queue.enqueue(mutable.ListBuffer(node))

    var depth = 0
    // 创建集合，用于存储已经遍历过的节点
    val visited = mutable.Set[EventNode]()

    val visitedLine = mutable.Map.empty[String, LineNode]

    var successFlag = false
    // 当队列非空
    while (queue.nonEmpty && !successFlag) {
      // 取出队首路径
      val currentPath = queue.dequeue()
      // 取出路径中的终止节点
      val currentNode = currentPath.last

      // 如果当前节点已经被遍历过，则忽略
      if (!visited.contains(currentNode)) { // 否则
        // 将当前节点加入已访问集合
        visited.add(currentNode)
        // 遍历所有到达当前节点的边
        if (isFindPathSourceBFS) {
          // 获取起始节点
          val sourceLineList = this.lineGroupBySource.get(currentNode.getName)
          if (sourceLineList.isDefined) {
            sourceLineList.get.foreach {
              case edge if edge.getSource == edge.getTarget =>
              // 忽略满足条件的情况
              case edge if this.loadedEventMap.contains(edge.getTarget) =>
                loadedLine(edge, currentPath, visitedLine)
                loadedEvent(currentPath)
                successFlag = true
                return
              case edge =>
                visitedLine += s"${edge.getSource},${edge.getTarget}" -> edge
                val fromNode = this.unloadedEventMap(edge.getTarget)
                val path = currentPath :+ fromNode
                queue.enqueue(path)
            }
          }

        } else { // 遍历当前节点的所有边        找到所有的 target
          val targetLineList = this.lineGroupByTarget.get(currentNode.getName)
          if (targetLineList.isDefined) {
            targetLineList.get.foreach {
              case edge if edge.getSource == edge.getTarget =>
              // 忽略满足条件的情况
              case edge if this.loadedEventMap.contains(edge.getSource) =>
                loadedLine(edge, currentPath.reverse, visitedLine)
                loadedEvent(currentPath)
                successFlag = true
                return
              case edge =>
                visitedLine += s"${edge.getSource},${edge.getTarget}" -> edge
                val toNode = this.unloadedEventMap(edge.getSource)
                val path = currentPath :+ toNode
                queue.enqueue(path)
            }
          }
        }
      }
      depth += 1
    }
  }

  private def loadedLine(
      line: LineNode,
      events: mutable.ListBuffer[EventNode],
      visitedLine: mutable.Map[String, LineNode]): Unit = {
    if (line != null && line.getGradient < 0) {
      line.setDefaultLine(true)
      line.setGradient(gradientsValue)
    }

    if (events.size > 1) {
      val result = events.sliding(2).map { case ListBuffer(a, b) => (a, b) }.toList
      for (elem <- result) {
        val maybeNodes = visitedLine.get(s"${elem._1.getName},${elem._2.getName}")
        if (maybeNodes.nonEmpty && maybeNodes.get.getGradient < 0) {
          maybeNodes.get.setDefaultLine(true)
          maybeNodes.get.setGradient(gradientsValue)
        }
      }

    }

  }

  private def loadedEvent(events: mutable.ListBuffer[EventNode]): Unit = {
    for (elem <- events) {
      elem.setGradient(gradientsValue)
      if (!this.loadedEventMap.contains(elem.getName)) {
        this.loadedEventMap.put(elem.getName, elem)
      }

      if (this.unloadedEventMap.contains(elem.getName)) {
        this.unloadedEventMap.remove(elem.getName)
      }
    }
  }
}
