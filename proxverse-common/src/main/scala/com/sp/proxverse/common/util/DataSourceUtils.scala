package com.sp.proxverse.common.util

object DataSourceUtils {
  def makeDatabaseName(poolId: Int): String = {
    s"pool_${poolId}"
  }

  def makeTableName(modelID: Int, fileName: String, id: String): String = {
    s"`t_${modelID}_${fileName}_${id}`"
  }

  def makeTableName(modelID: Int, fileName: String, id: Int): String = {
    s"`t_${modelID}_${fileName}_${id}`"
  }

  def makeFileTableName(fileName: String, id: String, dataType: Integer): String = {
    var tableName = s"`${fileName}`"
    if (dataType == null || dataType != 6) {
      tableName = this.makeFileTableName(fileName, id)
    }
    tableName
  }

  def makeFileTableName(fileName: String, id: String): String = {
    s"`t_${fileName}_${id}`"
  }

  def makeFileSourceTableName(fileName: String, id: String): String = {
    s"`t_${fileName}_${id}_source`"
  }

  def sourceTableNameToTableName(fileName: String): String = {
    fileName.replace("_source", "")
  }

  def makeBucketTableName(tableName: String): String = {
    s"`${tableName.replace("`", "")}_bucket`"
  }

  def makeFullTableName(tableName: String, database: String): String = {
    s"${database}.${tableName}"
  }

  def makeVariantIDDictTable(modelID: Int): String = {
    s"`d_${modelID}_variantID`"
  }

  def toSQLColumn(columnName: String): String = {
    s"`${columnName}`"
  }

  def extractDatabaseName(fullTableName: String): String = {
    val dotIndex = fullTableName.indexOf('.')
    if (dotIndex < 0) {
      ""
    } else {
      fullTableName.substring(0, dotIndex)
    }
  }

  def extractTableName(fullTableName: String): String = {
    if (fullTableName == null) {
      return ""
    }
    val dotIndex = fullTableName.indexOf('.')
    if (dotIndex < 0) {
      ""
    } else {
      val tableName = fullTableName.substring(dotIndex + 1, fullTableName.length)
      tableName.replace("`", "")
    }
  }

  def makeModelTableName(table: String, versionNumber: Integer): String = {
    s"`${table}_v_${versionNumber}`"
  }

  def makeModelTableNameByFullTableName(fullTableName: String, versionNumber: Integer): String = {
    if (versionNumber == 0 || versionNumber == null) {
      return fullTableName
    }
    val fullTableNameRep = fullTableName.replace("`", "")
    val dotIndex = fullTableNameRep.indexOf('.')
    if (dotIndex < 0) {
      ""
    } else {
      val database = fullTableNameRep.substring(0, dotIndex)
      val tableName = fullTableNameRep.substring(dotIndex + 1, fullTableNameRep.length)
      makeFullTableName(makeModelTableName(tableName, versionNumber), database)
    }
  }

  def ignoreVersionModelTableName(fullTableName: String): String = {
    val fullTableNameRep = fullTableName.replace("`", "")
    if (fullTableName == null) {
      ""
    } else {
      fullTableNameRep.split("_v_")(0)
    }
  }

  def quoteTableName(table: String): String = {
    if (table.contains("`")) {
      return table;
    }
    s"`$table`";
  }
}
