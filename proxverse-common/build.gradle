/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
  id 'scala'
}
repositories {
  mavenLocal()
  maven { url 'http://121.37.155.119:8081/repository/maven-public/' }
  maven { url 'https://jindodata-binary.oss-cn-shanghai.aliyuncs.com/mvn-repo/' }
}
sourceSets {
  main {
    scala {
      srcDirs = ['src/main/scala', 'src/main/java']
    }
    java {
      srcDirs = []
    }
  }

  test {
    scala {
      srcDirs = ['src/test/scala', 'src/test/java']
    }
    java {
      srcDirs = []
    }
  }
}
dependencies {
  implementation 'org.springframework.boot:spring-boot-starter-web:2.3.2.RELEASE'
  implementation 'org.projectlombok:lombok:1.18.20'
  annotationProcessor 'org.projectlombok:lombok:1.18.20'
  implementation 'io.springfox:springfox-swagger2:2.9.2'
  implementation 'com.github.xiaoymin:swagger-bootstrap-ui:1.9.6'
  implementation 'io.springfox:springfox-swagger-ui:2.9.2'
  implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.7'
  implementation 'javax.validation:validation-api:2.0.1.Final'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'org.apache.commons:commons-lang3:3.12.0'
  implementation 'org.apache.commons:commons-collections4:4.4'
  implementation 'org.springframework.boot:spring-boot-starter-mail:2.3.2.RELEASE'
  implementation 'org.codehaus.jackson:jackson-core-asl:1.9.13'
  implementation 'org.apache.httpcomponents:httpclient:4.5.13'
  implementation 'com.belerweb:pinyin4j:2.5.1'
  implementation 'org.xerial.snappy:snappy-java:1.1.8.4'
  implementation 'org.apache.spark:spark-catalyst_2.12:3.3.1-prx-0.0.2'
  implementation 'org.apache.hadoop:hadoop-client-api:3.3.1'
  implementation 'org.springframework.cloud:spring-cloud-starter-oauth2:2.2.0.RELEASE'
  implementation 'org.springframework.boot:spring-boot-starter-oauth2-client:2.3.2.RELEASE'
  implementation 'org.apache.hadoop:hadoop-client-runtime:3.3.1'
  implementation 'org.scala-lang:scala-compiler:2.12.15'
  implementation 'org.scala-lang:scala-library:2.12.15'
  implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:3.5.1'
  implementation 'org.apache.commons:commons-csv:1.8'
  implementation 'mysql:mysql-connector-java:8.0.28'
  implementation 'com.sp:proxverse-spark-common:1.0.0'
  implementation 'cn.dev33:sa-token-spring-boot-starter:1.37.0'
}

description = 'proxverse-common'


// 如果需要，可以配置 Scala 编译选项
compileScala {
  scalaCompileOptions.additionalParameters = ["-feature", "-deprecation"]
}

// 如果需要，可以配置 Java 编译选项
compileJava {
  options.encoding = 'UTF-8'
}


