-- 流程挖掘平台一致性检查问题修复脚本
-- 针对 topicSheetId 927 的问题

-- 1. 检查现有表状态
SHOW TABLES IN conformance_db LIKE 'reason_927*';
SHOW TABLES IN conformance_db LIKE 'conformance_927*';

-- 2. 检查表结构和数据
DESCRIBE conformance_db.reason_927;
DESCRIBE conformance_db.conformance_927;

-- 3. 检查数据完整性
SELECT COUNT(*) FROM conformance_db.reason_927;
SELECT COUNT(*) FROM conformance_db.conformance_927;

-- 4. 如果表存在但数据有问题，删除表以触发重新创建
-- DROP TABLE IF EXISTS conformance_db.reason_927;
-- DROP TABLE IF EXISTS conformance_db.conformance_927;

-- 5. 检查相关的变体表数据
SELECT COUNT(*) FROM variant_table WHERE sheet_id = 927;
