spring:
  # flyway
  flyway:
    enabled: false
    # sql编码
    encoding: utf-8
    # 版本文件路径
    locations: classpath:ddl/mysql/migration
    validate-on-migrate: true
    # 是否以当前数据为基线
    baselineOnMigrate: true
    # 当前数据为基线的时候版本号
    baselineVersion: 1.0.0
    # 是否允许执行比当前版本号低的版本sql
    outOfOrder: true

  # LDAP
  ldap:
    ldapEnabled: true
    urls: ldap://192.168.1.121:389
    base: dc=example,dc=org
    username: cn=admin,dc=example,dc=org
    password: admin
    groupId: 1
    userBase: OU=理士国际
    groupBase: OU=理士国际
  application:
    name: pm-project
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false


  datasource:
    dynamic:
      # 设置默认的数据源或数据源组，默认值即为master
      primary: engine
      # 严格匹配数据源，默认false，true未匹配到指定数据源时抛异常，false使用默认数据源
      strict: false
      datasource:
        oauth2:
          username: root
          password: password
          url: ******************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        engine:
          username: root
          password: password
          url: *******************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        execution:
          username: root
          password: password
          url: *********************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
  servlet:
    multipart:
      max-file-size: 20981970
      max-request-size: 20981970
  security:
    oauth2:
      client:
        provider:
          okta:
              authorization-uri: https://dev-50350041.okta.com/oauth2/v1/authorize
              token-uri: https://dev-50350041.okta.com/oauth2/v1/token
              user-info-uri: https://dev-50350041.okta.com/oauth2/v1/userinfo
              user-name-attribute: preferred_username
              jwk-set-uri: https://dev-50350041.okta.com/oauth2/v1/keys
          demo:
              authorization-uri: authorize
              token-uri: token
              user-info-uri: userinfo
              user-name-attribute: name
              jwk-set-uri: keys

          prx:
              #  获取访问令牌
              token-uri: http://localhost:8887/oauth/token
              authorization-uri: http://localhost:8887/oauth/authorize
              user-info-uri: http://localhost:8887/oauth/userinfo
              user-name-attribute: username
        registration:
            prx:
              client-id: oidcOauth
              client-secret: secret
              authorization-grant-type: authorization_code
              client-name: proxverse_oauth
              scope: "login"
              redirect-uri: "http://localhost:8888/login/oauth2/code/prx"
            demo:
              client-id: clientId
              client-secret: secret
              authorization-grant-type: authorization_code
              redirect-uri: redirectURL
              scope: "openid, profile, email, address, phone, offline_access"
              client-name: demo
            okta:
              client-id: 0oa8jp0qmnMGgQhFW5d7
              client-secret: yH6jC0lm-9XKa3-KZSTwAvdNO7kIB8JyyQwVg3Bj
              authorization-grant-type: authorization_code
              redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
              scope: "openid, profile, email, address, phone, offline_access"
              client-name: okta

  redis:
    host: localhost
    port: 63379
    timeout: 5000
    jedis:
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 50
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 3000
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 2

# 统一缓存框架配置
cache:
  # 缓存类型：guava（本地缓存）或 redis（分布式缓存）
  type: guava

  # 是否启用缓存统计
  enable-stats: true

  # 缓存键前缀
  key-prefix: "proxverse:cache"

  # Redis配置
  redis:
    # 是否启用Redis缓存
    enabled: true
    # 是否启用降级到Guava缓存
    fallback-to-guava: true
    # 默认过期时间（小时）
    default-expire-hours: 6
    # 连接超时时间（毫秒）
    timeout-ms: 3000
    # 最大重试次数
    max-retries: 3
    # 序列化类型：json, jdk, kryo
    serialization: json

  # Guava配置（作为降级备选）
  guava:
    # 最大缓存条目数
    maximum-size: 1000
    # 访问后过期时间
    expire-after-access: 6
    # 访问后过期时间单位
    expire-after-access-unit: HOURS
    # 写入后过期时间（-1表示不设置）
    expire-after-write: -1
    # 写入后过期时间单位
    expire-after-write-unit: HOURS
    # 初始容量
    initial-capacity: 16
    # 并发级别
    concurrency-level: 4
    # 是否启用软引用
    soft-values: false
    # 是否启用弱引用
    weak-values: false
    # 是否启用弱键
    weak-keys: false

server:
  port: 8888
  sleuth:
    log:
      slf4j:
        enabled: true
    sampler:
      # 抽样率，默认0.1
      probability: 1.0


  servlet:
    multipart:
      enabled: true
      max-file-size: 2000MB
      max-request-size: 2000MB

oauth2.license.path: /Users/<USER>/Downloads/sp/proxverse-project/process/filePath/license
logging:
  level:
    com.sp.spengine.mapper: info
    root: info
    org.springframework.web.servlet.DispatcherServlet: info
    org.springframework.cloud.sleuth: info
    com.baomidou.mybatisplus: info
    org:
      apache:
        spark:
          storage:
            BlockManagerInfo: WARN

csv.file.save.path: /Users/<USER>/Downloads/sp/proxverse-project/process/filePath/tmp/upload/
csv.child.file.save.path: /Users/<USER>/Downloads/sp/proxverse-project/process/filePath/tmp/upload/child/
del.file.path: /Users/<USER>/Downloads/sp/proxverse-project/process/filePath/tmp

business.file.path: /Users/<USER>/Downloads/sp/proxverse-project/process/business/filePath/

spring.cache.enabled: false
spark.store.path: /Users/<USER>/Downloads/sp/proxverse-project/process/filePath/tmp/warehouse/
spark.metastore.jdbc.url: *****************************************************************
spark.metastore.jdbc.user: root
spark.metastore.jdbc.password: password
spark.master: local[16]




mybatis-plus:
  mapper-locations: classpath:mapper/**.xml



spark.conf: "{\"spark.sql.legacy.timeParserPolicy\": \"LEGACY\",\"spark.sql.parquet.datetimeRebaseModeInWrite\": \"LEGACY\",\"spark.sql.legacy.parquet.int96RebaseModeInWrite\": \"LEGACY\",\"spark.sql.pql.encodedColDict.encodeWithModelBuild\":\"true\",\"spark.sql.legacy.parquet.int96RebaseModeInRead\": \"LEGACY\",\"spark.sql.pql.planCacheEnabled\":\"false\"}"

spark.sql.columnar.enabled: false


prx:
  simulation:
    file:
      baseDir: /Users/<USER>/Documents/shangping_develop/backend/process-mining-platform2/process-mining-platform
  pql:
    redisCache:
      enabled: false

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  log-Level: info
  # 每次清理过期数据间隔的时间 （单位: 秒） ，默认值30秒，设置为-1代表不启动定时清理
  data-refresh-period: 3600
  # 自定义配置: 是否使用mySql存储session信息
  enable-mysql-memory: true

#默认密码
prx.admin.local.initPassword: proxverse
prx.admin.passwordHistoryCheckEnabled: true
#快速开始的csv文件默认发票文件
prx.dataModel.file.quickStartDefaultFile: proxverse_demo
# 上传文件的限制大小默认1GB
prx.dataModel.file.maxUploadSize: 1g
#快速开始的限制大小默认50MB
prx.dataModel.file.quickStartMaxUploadSize: 50m
#流程图显示的最大event默认500
prx.workbench.processTree.maxQueryEvents: 1000
#流程图显示的最大connect默认500
prx.workbench.processTree.maxQueryConnects: 1000
#日志保存的默认天数默认14天
prx.gateway.log.keepDays: 14
#echarts查询top数量
prx.workbench.component.echartsQueryTopCount: 1000
prx.admin.license.enabled: false

#密码高级设置
prx.admin.loginLockEnabled: true
prx.admin.loginFailMaxLimit: 5
prx.admin.pwdChangeEnabled: true
prx.admin.pwdChangeMonths: 6

# SAP Test
#prx.admin.sap.enableLoad: true
#prx.admin.sap.enableLogin: true
#prx.admin.sap.loadIP: **************
#prx.admin.sap.jdbcType: 8
#prx.admin.sap.loadPort: 30015
#prx.admin.sap.loadDbName: LHD
#prx.admin.sap.loadSchemaName: SAPABAP1
#prx.admin.sap.loadTableName: PA0001
#prx.admin.sap.loadUserName: PROXVERSE_USER
#prx.admin.sap.loadPassword: Pro_258369*
#prx.admin.sap.nameColumn: SNAME
#prx.admin.sap.idColumn: PERNR
#prx.admin.sap.removeResignation: false
#prx.admin.sap.tokenKey: leoch123
#prx.admin.sap.loadSql: SELECT DISTINCT t2.PERNR, t1.SNAME FROM SAPABAP1.PA0001 t1 INNER JOIN ( SELECT PERNR, MAX(AEDTM) AS MaxAEDTM FROM SAPABAP1.PA0001 t2 GROUP BY PERNR ) t2 ON t1.PERNR = t2.PERNR AND t1.AEDTM = t2.MaxAEDTM WHERE t1.PERSG <> 'F';
#prx.admin.sap.deleteSql: SELECT DISTINCT t2.PERNR, t1.SNAME FROM SAPABAP1.PA0001 t1 INNER JOIN ( SELECT PERNR, MAX(AEDTM) AS MaxAEDTM FROM SAPABAP1.PA0001 t2 GROUP BY PERNR ) t2 ON t1.PERNR = t2.PERNR AND t1.AEDTM = t2.MaxAEDTM WHERE t1.PERSG = 'F';
#prx.admin.sap.initRoleID:
#
#



# SAP prod


prx.admin.sap.enableLoad: true
prx.admin.sap.enableLogin: true
prx.admin.sap.loadIP: **************
prx.admin.sap.jdbcType: 8
prx.admin.sap.loadPort: 30015
prx.admin.sap.loadDbName: SHP
prx.admin.sap.loadSchemaName: SAPABAP1
prx.admin.sap.loadTableName: PA0001
prx.admin.sap.loadUserName: PROXVERSE_USER
prx.admin.sap.loadPassword: Pro_147258*
prx.admin.sap.nameColumn: SNAME
prx.admin.sap.idColumn: PERNR
prx.admin.sap.removeResignation: false
prx.admin.sap.tokenKey: leoch123
prx.admin.sap.loadSql: SELECT DISTINCT t2.PERNR, t1.SNAME FROM SAPABAP1.PA0001 t1 INNER JOIN ( SELECT PERNR, MAX(ENDDA) AS MaxAEDTM FROM SAPABAP1.PA0001 t2 GROUP BY PERNR ) t2 ON t1.PERNR = t2.PERNR AND t1.ENDDA = t2.MaxAEDTM WHERE t1.PERSG <> 'F' AND MANDT = '800';
prx.admin.sap.deleteSql: SELECT DISTINCT t2.PERNR, t1.SNAME FROM SAPABAP1.PA0001 t1 INNER JOIN ( SELECT PERNR, MAX(ENDDA) AS MaxAEDTM FROM SAPABAP1.PA0001 t2 GROUP BY PERNR ) t2 ON t1.PERNR = t2.PERNR AND t1.ENDDA = t2.MaxAEDTM WHERE t1.PERSG = 'F' AND MANDT = '800';
prx.admin.sap.initRoleID:
prx.admin.sap.initPassword: proxverse
spark.sql.starry.enabled: false
spark.sql.pql.columnCacheEnabled: false


# TopicFilter缓存配置 - 切换到Redis
proxverse:
  topic-filter:
    cache:
      # 缓存类型：guava（本地缓存）或 redis（分布式缓存）
      type: guava
      # 缓存过期时间（小时）
      expire-hours: 6
      # 最大缓存条目数
      max-size: 100
      # Redis缓存键前缀
      redis-key-prefix: "proxverse:topic-filter"
      # 是否启用缓存降级策略（当Redis不可用时使用本地缓存）
      enable-fallback: true
      # Redis连接超时时间（毫秒）
      redis-timeout-ms: 3000